<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Restaurant Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>🧪 Restaurant Management API Test</h1>
    <p>Use this page to test if your API backend is working correctly.</p>

    <div class="test-section">
        <h2>📡 API Health Check</h2>
        <button class="test-button" onclick="testHealth()">Test API Health</button>
        <div id="healthResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📋 Orders Endpoint</h2>
        <button class="test-button" onclick="testOrders()">Test Get Orders</button>
        <button class="test-button" onclick="testCreateOrder()">Test Create Order</button>
        <div id="ordersResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>💰 Expenses Endpoint</h2>
        <button class="test-button" onclick="testExpenses()">Test Get Expenses</button>
        <button class="test-button" onclick="testCreateExpense()">Test Create Expense</button>
        <div id="expensesResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🍽️ Menu Items Endpoint</h2>
        <button class="test-button" onclick="testMenuItems()">Test Get Menu Items</button>
        <div id="menuResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📦 Database Connection Test</h2>
        <button class="test-button" onclick="testDatabaseManager()">Test Database Manager</button>
        <div id="dbResult" class="result" style="display: none;"></div>
    </div>

    <script src="assets/js/database-connection.js"></script>
    <script>
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            
            const status = isSuccess ? '✅ SUCCESS' : '❌ ERROR';
            element.innerHTML = `<div class="status">${status}</div>${JSON.stringify(data, null, 2)}`;
        }

        async function testHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                showResult('healthResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('healthResult', {
                    error: error.message,
                    note: 'API endpoint not accessible. Make sure PHP backend is set up.'
                }, false);
            }
        }

        async function testOrders() {
            try {
                const response = await fetch('/api/orders?limit=5');
                const data = await response.json();
                showResult('ordersResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('ordersResult', {
                    error: error.message
                }, false);
            }
        }

        async function testCreateOrder() {
            try {
                const testOrder = {
                    customer_name: 'Test Customer',
                    order_type: 'dine-in',
                    items: [
                        { name: 'Test Item', price: 10.99, quantity: 1 }
                    ],
                    total_amount: 10.99
                };

                const response = await fetch('/api/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testOrder)
                });
                const data = await response.json();
                showResult('ordersResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('ordersResult', {
                    error: error.message
                }, false);
            }
        }

        async function testExpenses() {
            try {
                const response = await fetch('/api/expenses?limit=5');
                const data = await response.json();
                showResult('expensesResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('expensesResult', {
                    error: error.message
                }, false);
            }
        }

        async function testCreateExpense() {
            try {
                const testExpense = {
                    category: 'other',
                    description: 'Test Expense',
                    amount: 25.00,
                    expense_date: new Date().toISOString().split('T')[0]
                };

                const response = await fetch('/api/expenses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testExpense)
                });
                const data = await response.json();
                showResult('expensesResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('expensesResult', {
                    error: error.message
                }, false);
            }
        }

        async function testMenuItems() {
            try {
                const response = await fetch('/api/menu-items');
                const data = await response.json();
                showResult('menuResult', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok);
            } catch (error) {
                showResult('menuResult', {
                    error: error.message
                }, false);
            }
        }

        async function testDatabaseManager() {
            try {
                if (typeof window.dbManager === 'undefined') {
                    showResult('dbResult', {
                        error: 'Database manager not loaded',
                        note: 'Make sure database-connection.js is loaded'
                    }, false);
                    return;
                }

                console.log('Testing database manager...');
                const orders = await window.dbManager.getOrders({ limit: 3 });
                
                showResult('dbResult', {
                    dbManagerExists: !!window.dbManager,
                    ordersType: typeof orders,
                    ordersIsArray: Array.isArray(orders),
                    ordersLength: orders ? orders.length : 'N/A',
                    sampleOrders: orders
                }, true);
            } catch (error) {
                showResult('dbResult', {
                    error: error.message,
                    stack: error.stack
                }, false);
            }
        }

        // Auto-run health check on page load
        window.addEventListener('load', () => {
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>
