RewriteEngine On

# Handle CORS preflight requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ index.php [QSA,L]

# Route all API requests to index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Set environment variables for better error handling
SetEnv ENVIRONMENT development

# Enable error reporting for development
php_flag display_errors on
php_value error_reporting E_ALL

# Set proper headers for JSON responses
<FilesMatch "\.php$">
    Header always set Content-Type "application/json; charset=utf-8"
</FilesMatch>
