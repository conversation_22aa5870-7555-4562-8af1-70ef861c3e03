<?php
/**
 * Database Configuration
 * Handles database connection and configuration
 */

class Database {
    private $host;
    private $database_name;
    private $username;
    private $password;
    private $connection;
    
    public function __construct() {
        // Load configuration from environment or use defaults
        $this->host = $_ENV['DB_HOST'] ?? 'localhost';
        $this->database_name = $_ENV['DB_NAME'] ?? 'zaiqa_restaurant';
        $this->username = $_ENV['DB_USER'] ?? 'root';
        $this->password = $_ENV['DB_PASS'] ?? '';
        
        // For development, you can set these directly:
        // $this->host = 'localhost';
        // $this->database_name = 'zaiqa_restaurant';
        // $this->username = 'root';
        // $this->password = '';
    }
    
    /**
     * Get database connection
     */
    public function getConnection() {
        $this->connection = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->database_name . ";charset=utf8mb4";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            // Create tables if they don't exist
            $this->createTablesIfNotExist();
            
        } catch (PDOException $e) {
            error_log("Database connection error: " . $e->getMessage());
            return null;
        }
        
        return $this->connection;
    }
    
    /**
     * Create database tables if they don't exist
     */
    private function createTablesIfNotExist() {
        $tables = [
            'orders' => "
                CREATE TABLE IF NOT EXISTS orders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    order_number VARCHAR(50) UNIQUE NOT NULL,
                    customer_name VARCHAR(255),
                    customer_phone VARCHAR(20),
                    order_type ENUM('dine-in', 'takeaway', 'delivery') DEFAULT 'dine-in',
                    status ENUM('pending', 'preparing', 'ready', 'completed', 'cancelled') DEFAULT 'pending',
                    items JSON NOT NULL,
                    subtotal DECIMAL(10,2) NOT NULL,
                    tax_amount DECIMAL(10,2) DEFAULT 0,
                    discount_amount DECIMAL(10,2) DEFAULT 0,
                    total_amount DECIMAL(10,2) NOT NULL,
                    payment_method ENUM('cash', 'card', 'online') DEFAULT 'cash',
                    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_order_date (created_at),
                    INDEX idx_status (status),
                    INDEX idx_order_type (order_type)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'expenses' => "
                CREATE TABLE IF NOT EXISTS expenses (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    category ENUM('ingredients', 'staff', 'utilities', 'rent', 'equipment', 'marketing', 'other') NOT NULL,
                    subcategory VARCHAR(100),
                    description TEXT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_method ENUM('cash', 'card', 'bank_transfer', 'cheque') DEFAULT 'cash',
                    vendor_name VARCHAR(255),
                    receipt_number VARCHAR(100),
                    expense_date DATE NOT NULL,
                    notes TEXT,
                    created_by VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_expense_date (expense_date),
                    INDEX idx_category (category),
                    INDEX idx_amount (amount)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'menu_items' => "
                CREATE TABLE IF NOT EXISTS menu_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    category VARCHAR(100) NOT NULL,
                    description TEXT,
                    price DECIMAL(8,2) NOT NULL,
                    cost DECIMAL(8,2) DEFAULT 0,
                    image_url VARCHAR(500),
                    is_available BOOLEAN DEFAULT TRUE,
                    preparation_time INT DEFAULT 15,
                    ingredients JSON,
                    allergens JSON,
                    nutritional_info JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_category (category),
                    INDEX idx_available (is_available),
                    INDEX idx_price (price)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'inventory' => "
                CREATE TABLE IF NOT EXISTS inventory (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    item_name VARCHAR(255) NOT NULL,
                    category VARCHAR(100) NOT NULL,
                    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
                    unit VARCHAR(50) NOT NULL,
                    minimum_stock DECIMAL(10,2) DEFAULT 0,
                    maximum_stock DECIMAL(10,2) DEFAULT 0,
                    cost_per_unit DECIMAL(8,2) DEFAULT 0,
                    supplier VARCHAR(255),
                    last_restocked DATE,
                    expiry_date DATE,
                    location VARCHAR(100),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_category (category),
                    INDEX idx_stock_level (current_stock),
                    INDEX idx_expiry (expiry_date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'business_days' => "
                CREATE TABLE IF NOT EXISTS business_days (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    business_date DATE NOT NULL UNIQUE,
                    opening_balance DECIMAL(10,2) DEFAULT 0,
                    closing_balance DECIMAL(10,2) DEFAULT 0,
                    total_sales DECIMAL(10,2) DEFAULT 0,
                    total_expenses DECIMAL(10,2) DEFAULT 0,
                    cash_sales DECIMAL(10,2) DEFAULT 0,
                    card_sales DECIMAL(10,2) DEFAULT 0,
                    owner_withdrawals DECIMAL(10,2) DEFAULT 0,
                    status ENUM('open', 'closed') DEFAULT 'open',
                    notes TEXT,
                    opened_by VARCHAR(100),
                    closed_by VARCHAR(100),
                    opened_at TIMESTAMP NULL,
                    closed_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_business_date (business_date),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'owner_withdrawals' => "
                CREATE TABLE IF NOT EXISTS owner_withdrawals (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    amount DECIMAL(10,2) NOT NULL,
                    reason VARCHAR(255) NOT NULL,
                    description TEXT,
                    withdrawal_date DATE NOT NULL,
                    business_day_id INT,
                    created_by VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
                    INDEX idx_withdrawal_date (withdrawal_date),
                    INDEX idx_amount (amount)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];
        
        foreach ($tables as $tableName => $sql) {
            try {
                $this->connection->exec($sql);
                error_log("Table '$tableName' created or verified successfully");
            } catch (PDOException $e) {
                error_log("Error creating table '$tableName': " . $e->getMessage());
            }
        }
    }
    
    /**
     * Get database info
     */
    public function getDatabaseInfo() {
        return [
            'host' => $this->host,
            'database' => $this->database_name,
            'username' => $this->username,
            'connected' => $this->connection !== null
        ];
    }
}
?>
