<?php
/**
 * Business Day Controller
 * Handles all business day-related API operations
 */

class BusinessDayController {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get all business days
     */
    public function getAll($params = []) {
        try {
            $sql = "SELECT * FROM business_days WHERE 1=1";
            $bindings = [];
            
            if (!empty($params['start_date'])) {
                $sql .= " AND business_date >= :start_date";
                $bindings[':start_date'] = $params['start_date'];
            }
            
            if (!empty($params['end_date'])) {
                $sql .= " AND business_date <= :end_date";
                $bindings[':end_date'] = $params['end_date'];
            }
            
            if (!empty($params['status'])) {
                $sql .= " AND status = :status";
                $bindings[':status'] = $params['status'];
            }
            
            $sql .= " ORDER BY business_date DESC";
            
            if (!empty($params['limit'])) {
                $sql .= " LIMIT :limit";
                $bindings[':limit'] = (int)$params['limit'];
            }
            
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                if ($key === ':limit') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value);
                }
            }
            
            $stmt->execute();
            $days = $stmt->fetchAll();
            
            return [
                'success' => true,
                'data' => $days,
                'count' => count($days)
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get business day by ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT * FROM business_days WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $day = $stmt->fetch();
            
            if (!$day) {
                return [
                    'success' => false,
                    'error' => 'Business day not found'
                ];
            }
            
            return [
                'success' => true,
                'data' => $day
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create new business day
     */
    public function create($data) {
        try {
            $required = ['business_date'];
            foreach ($required as $field) {
                if (!isset($data[$field])) {
                    return [
                        'success' => false,
                        'error' => "Missing required field: $field"
                    ];
                }
            }
            
            $sql = "INSERT INTO business_days (
                business_date, opening_balance, closing_balance, total_sales, total_expenses,
                cash_sales, card_sales, owner_withdrawals, status, notes, opened_by, opened_at
            ) VALUES (
                :business_date, :opening_balance, :closing_balance, :total_sales, :total_expenses,
                :cash_sales, :card_sales, :owner_withdrawals, :status, :notes, :opened_by, :opened_at
            )";
            
            $stmt = $this->db->prepare($sql);
            
            $stmt->bindValue(':business_date', $data['business_date']);
            $stmt->bindValue(':opening_balance', $data['opening_balance'] ?? 0);
            $stmt->bindValue(':closing_balance', $data['closing_balance'] ?? 0);
            $stmt->bindValue(':total_sales', $data['total_sales'] ?? 0);
            $stmt->bindValue(':total_expenses', $data['total_expenses'] ?? 0);
            $stmt->bindValue(':cash_sales', $data['cash_sales'] ?? 0);
            $stmt->bindValue(':card_sales', $data['card_sales'] ?? 0);
            $stmt->bindValue(':owner_withdrawals', $data['owner_withdrawals'] ?? 0);
            $stmt->bindValue(':status', $data['status'] ?? 'open');
            $stmt->bindValue(':notes', $data['notes'] ?? null);
            $stmt->bindValue(':opened_by', $data['opened_by'] ?? 'system');
            $stmt->bindValue(':opened_at', $data['opened_at'] ?? date('Y-m-d H:i:s'));
            
            $stmt->execute();
            
            return $this->getById($this->db->lastInsertId());
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update business day
     */
    public function update($id, $data) {
        try {
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $updateFields = [];
            $bindings = [':id' => $id];
            
            $allowedFields = [
                'business_date', 'opening_balance', 'closing_balance', 'total_sales', 'total_expenses',
                'cash_sales', 'card_sales', 'owner_withdrawals', 'status', 'notes', 'closed_by', 'closed_at'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = :$field";
                    $bindings[":$field"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return [
                    'success' => false,
                    'error' => 'No valid fields to update'
                ];
            }
            
            $sql = "UPDATE business_days SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            
            return $this->getById($id);
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete business day
     */
    public function delete($id) {
        try {
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $sql = "DELETE FROM business_days WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'message' => 'Business day deleted successfully'
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get current business day
     */
    public function getCurrentDay() {
        try {
            $today = date('Y-m-d');
            $sql = "SELECT * FROM business_days WHERE business_date = :today";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':today', $today);
            $stmt->execute();
            
            $day = $stmt->fetch();
            
            if (!$day) {
                // Create new business day for today
                return $this->create([
                    'business_date' => $today,
                    'status' => 'open',
                    'opened_by' => 'system',
                    'opened_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            return [
                'success' => true,
                'data' => $day
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
}
?>
