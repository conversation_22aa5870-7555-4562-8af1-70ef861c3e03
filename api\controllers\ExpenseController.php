<?php
/**
 * Expense Controller
 * Handles all expense-related API operations
 */

class ExpenseController {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get all expenses with optional filtering
     */
    public function getAll($params = []) {
        try {
            $sql = "SELECT * FROM expenses WHERE 1=1";
            $bindings = [];
            
            // Apply filters
            if (!empty($params['start_date'])) {
                $sql .= " AND expense_date >= :start_date";
                $bindings[':start_date'] = $params['start_date'];
            }
            
            if (!empty($params['end_date'])) {
                $sql .= " AND expense_date <= :end_date";
                $bindings[':end_date'] = $params['end_date'];
            }
            
            if (!empty($params['category']) && $params['category'] !== 'all') {
                $sql .= " AND category = :category";
                $bindings[':category'] = $params['category'];
            }
            
            if (!empty($params['min_amount'])) {
                $sql .= " AND amount >= :min_amount";
                $bindings[':min_amount'] = $params['min_amount'];
            }
            
            if (!empty($params['max_amount'])) {
                $sql .= " AND amount <= :max_amount";
                $bindings[':max_amount'] = $params['max_amount'];
            }
            
            if (!empty($params['payment_method'])) {
                $sql .= " AND payment_method = :payment_method";
                $bindings[':payment_method'] = $params['payment_method'];
            }
            
            // Add ordering
            $sql .= " ORDER BY expense_date DESC, created_at DESC";
            
            // Add limit if specified
            if (!empty($params['limit'])) {
                $sql .= " LIMIT :limit";
                $bindings[':limit'] = (int)$params['limit'];
            }
            
            $stmt = $this->db->prepare($sql);
            
            // Bind parameters
            foreach ($bindings as $key => $value) {
                if ($key === ':limit') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value);
                }
            }
            
            $stmt->execute();
            $expenses = $stmt->fetchAll();
            
            return [
                'success' => true,
                'data' => $expenses,
                'count' => count($expenses),
                'filters_applied' => array_filter($params)
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get expense by ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT * FROM expenses WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $expense = $stmt->fetch();
            
            if (!$expense) {
                return [
                    'success' => false,
                    'error' => 'Expense not found'
                ];
            }
            
            return [
                'success' => true,
                'data' => $expense
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create new expense
     */
    public function create($data) {
        try {
            // Validate required fields
            $required = ['category', 'description', 'amount', 'expense_date'];
            foreach ($required as $field) {
                if (!isset($data[$field])) {
                    return [
                        'success' => false,
                        'error' => "Missing required field: $field"
                    ];
                }
            }
            
            $sql = "INSERT INTO expenses (
                category, subcategory, description, amount, payment_method,
                vendor_name, receipt_number, expense_date, notes, created_by
            ) VALUES (
                :category, :subcategory, :description, :amount, :payment_method,
                :vendor_name, :receipt_number, :expense_date, :notes, :created_by
            )";
            
            $stmt = $this->db->prepare($sql);
            
            $stmt->bindValue(':category', $data['category']);
            $stmt->bindValue(':subcategory', $data['subcategory'] ?? null);
            $stmt->bindValue(':description', $data['description']);
            $stmt->bindValue(':amount', $data['amount']);
            $stmt->bindValue(':payment_method', $data['payment_method'] ?? 'cash');
            $stmt->bindValue(':vendor_name', $data['vendor_name'] ?? null);
            $stmt->bindValue(':receipt_number', $data['receipt_number'] ?? null);
            $stmt->bindValue(':expense_date', $data['expense_date']);
            $stmt->bindValue(':notes', $data['notes'] ?? null);
            $stmt->bindValue(':created_by', $data['created_by'] ?? 'system');
            
            $stmt->execute();
            
            $expenseId = $this->db->lastInsertId();
            
            // Return the created expense
            return $this->getById($expenseId);
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update expense
     */
    public function update($id, $data) {
        try {
            // Check if expense exists
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $updateFields = [];
            $bindings = [':id' => $id];
            
            // Build dynamic update query
            $allowedFields = [
                'category', 'subcategory', 'description', 'amount', 'payment_method',
                'vendor_name', 'receipt_number', 'expense_date', 'notes', 'created_by'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = :$field";
                    $bindings[":$field"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return [
                    'success' => false,
                    'error' => 'No valid fields to update'
                ];
            }
            
            $sql = "UPDATE expenses SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            
            // Return updated expense
            return $this->getById($id);
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete expense
     */
    public function delete($id) {
        try {
            // Check if expense exists
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $sql = "DELETE FROM expenses WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'message' => 'Expense deleted successfully'
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get expense summary by category
     */
    public function getSummaryByCategory($params = []) {
        try {
            $sql = "SELECT 
                        category,
                        COUNT(*) as count,
                        SUM(amount) as total_amount,
                        AVG(amount) as avg_amount,
                        MIN(amount) as min_amount,
                        MAX(amount) as max_amount
                    FROM expenses 
                    WHERE 1=1";
            
            $bindings = [];
            
            if (!empty($params['start_date'])) {
                $sql .= " AND expense_date >= :start_date";
                $bindings[':start_date'] = $params['start_date'];
            }
            
            if (!empty($params['end_date'])) {
                $sql .= " AND expense_date <= :end_date";
                $bindings[':end_date'] = $params['end_date'];
            }
            
            $sql .= " GROUP BY category ORDER BY total_amount DESC";
            
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            $summary = $stmt->fetchAll();
            
            return [
                'success' => true,
                'data' => $summary
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
}
?>
