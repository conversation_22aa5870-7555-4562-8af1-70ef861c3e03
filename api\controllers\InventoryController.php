<?php
/**
 * Inventory Controller
 * Handles all inventory-related API operations
 */

class InventoryController {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get all inventory items
     */
    public function getAll($params = []) {
        try {
            $sql = "SELECT *, 
                    CASE 
                        WHEN current_stock <= minimum_stock THEN 'low'
                        WHEN current_stock >= maximum_stock THEN 'high'
                        ELSE 'normal'
                    END as stock_status
                    FROM inventory WHERE 1=1";
            $bindings = [];
            
            if (!empty($params['category'])) {
                $sql .= " AND category = :category";
                $bindings[':category'] = $params['category'];
            }
            
            if (!empty($params['low_stock'])) {
                $sql .= " AND current_stock <= minimum_stock";
            }
            
            $sql .= " ORDER BY category, item_name";
            
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            $items = $stmt->fetchAll();
            
            return [
                'success' => true,
                'data' => $items,
                'count' => count($items)
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get inventory item by ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT *, 
                    CASE 
                        WHEN current_stock <= minimum_stock THEN 'low'
                        WHEN current_stock >= maximum_stock THEN 'high'
                        ELSE 'normal'
                    END as stock_status
                    FROM inventory WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $item = $stmt->fetch();
            
            if (!$item) {
                return [
                    'success' => false,
                    'error' => 'Inventory item not found'
                ];
            }
            
            return [
                'success' => true,
                'data' => $item
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create new inventory item
     */
    public function create($data) {
        try {
            $required = ['item_name', 'category', 'current_stock', 'unit'];
            foreach ($required as $field) {
                if (!isset($data[$field])) {
                    return [
                        'success' => false,
                        'error' => "Missing required field: $field"
                    ];
                }
            }
            
            $sql = "INSERT INTO inventory (
                item_name, category, current_stock, unit, minimum_stock, maximum_stock,
                cost_per_unit, supplier, last_restocked, expiry_date, location, notes
            ) VALUES (
                :item_name, :category, :current_stock, :unit, :minimum_stock, :maximum_stock,
                :cost_per_unit, :supplier, :last_restocked, :expiry_date, :location, :notes
            )";
            
            $stmt = $this->db->prepare($sql);
            
            $stmt->bindValue(':item_name', $data['item_name']);
            $stmt->bindValue(':category', $data['category']);
            $stmt->bindValue(':current_stock', $data['current_stock']);
            $stmt->bindValue(':unit', $data['unit']);
            $stmt->bindValue(':minimum_stock', $data['minimum_stock'] ?? 0);
            $stmt->bindValue(':maximum_stock', $data['maximum_stock'] ?? 0);
            $stmt->bindValue(':cost_per_unit', $data['cost_per_unit'] ?? 0);
            $stmt->bindValue(':supplier', $data['supplier'] ?? null);
            $stmt->bindValue(':last_restocked', $data['last_restocked'] ?? null);
            $stmt->bindValue(':expiry_date', $data['expiry_date'] ?? null);
            $stmt->bindValue(':location', $data['location'] ?? null);
            $stmt->bindValue(':notes', $data['notes'] ?? null);
            
            $stmt->execute();
            
            return $this->getById($this->db->lastInsertId());
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update inventory item
     */
    public function update($id, $data) {
        try {
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $updateFields = [];
            $bindings = [':id' => $id];
            
            $allowedFields = [
                'item_name', 'category', 'current_stock', 'unit', 'minimum_stock', 'maximum_stock',
                'cost_per_unit', 'supplier', 'last_restocked', 'expiry_date', 'location', 'notes'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = :$field";
                    $bindings[":$field"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return [
                    'success' => false,
                    'error' => 'No valid fields to update'
                ];
            }
            
            $sql = "UPDATE inventory SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            
            return $this->getById($id);
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete inventory item
     */
    public function delete($id) {
        try {
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $sql = "DELETE FROM inventory WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'message' => 'Inventory item deleted successfully'
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
}
?>
