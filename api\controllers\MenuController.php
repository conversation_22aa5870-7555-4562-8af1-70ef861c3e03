<?php
/**
 * Menu Controller
 * Handles all menu item-related API operations
 */

class MenuController {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get all menu items
     */
    public function getAll($params = []) {
        try {
            $sql = "SELECT * FROM menu_items WHERE 1=1";
            $bindings = [];
            
            if (!empty($params['category'])) {
                $sql .= " AND category = :category";
                $bindings[':category'] = $params['category'];
            }
            
            if (isset($params['available'])) {
                $sql .= " AND is_available = :available";
                $bindings[':available'] = $params['available'] ? 1 : 0;
            }
            
            $sql .= " ORDER BY category, name";
            
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            $items = $stmt->fetchAll();
            
            // Decode JSON fields
            foreach ($items as &$item) {
                $item['ingredients'] = json_decode($item['ingredients'], true);
                $item['allergens'] = json_decode($item['allergens'], true);
                $item['nutritional_info'] = json_decode($item['nutritional_info'], true);
            }
            
            return [
                'success' => true,
                'data' => $items,
                'count' => count($items)
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get menu item by ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT * FROM menu_items WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $item = $stmt->fetch();
            
            if (!$item) {
                return [
                    'success' => false,
                    'error' => 'Menu item not found'
                ];
            }
            
            // Decode JSON fields
            $item['ingredients'] = json_decode($item['ingredients'], true);
            $item['allergens'] = json_decode($item['allergens'], true);
            $item['nutritional_info'] = json_decode($item['nutritional_info'], true);
            
            return [
                'success' => true,
                'data' => $item
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create new menu item
     */
    public function create($data) {
        try {
            $required = ['name', 'category', 'price'];
            foreach ($required as $field) {
                if (!isset($data[$field])) {
                    return [
                        'success' => false,
                        'error' => "Missing required field: $field"
                    ];
                }
            }
            
            $sql = "INSERT INTO menu_items (
                name, category, description, price, cost, image_url,
                is_available, preparation_time, ingredients, allergens, nutritional_info
            ) VALUES (
                :name, :category, :description, :price, :cost, :image_url,
                :is_available, :preparation_time, :ingredients, :allergens, :nutritional_info
            )";
            
            $stmt = $this->db->prepare($sql);
            
            $stmt->bindValue(':name', $data['name']);
            $stmt->bindValue(':category', $data['category']);
            $stmt->bindValue(':description', $data['description'] ?? null);
            $stmt->bindValue(':price', $data['price']);
            $stmt->bindValue(':cost', $data['cost'] ?? 0);
            $stmt->bindValue(':image_url', $data['image_url'] ?? null);
            $stmt->bindValue(':is_available', $data['is_available'] ?? true, PDO::PARAM_BOOL);
            $stmt->bindValue(':preparation_time', $data['preparation_time'] ?? 15);
            $stmt->bindValue(':ingredients', json_encode($data['ingredients'] ?? []));
            $stmt->bindValue(':allergens', json_encode($data['allergens'] ?? []));
            $stmt->bindValue(':nutritional_info', json_encode($data['nutritional_info'] ?? []));
            
            $stmt->execute();
            
            return $this->getById($this->db->lastInsertId());
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update menu item
     */
    public function update($id, $data) {
        try {
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $updateFields = [];
            $bindings = [':id' => $id];
            
            $allowedFields = [
                'name', 'category', 'description', 'price', 'cost', 'image_url',
                'is_available', 'preparation_time', 'ingredients', 'allergens', 'nutritional_info'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = :$field";
                    if (in_array($field, ['ingredients', 'allergens', 'nutritional_info'])) {
                        $bindings[":$field"] = json_encode($data[$field]);
                    } else {
                        $bindings[":$field"] = $data[$field];
                    }
                }
            }
            
            if (empty($updateFields)) {
                return [
                    'success' => false,
                    'error' => 'No valid fields to update'
                ];
            }
            
            $sql = "UPDATE menu_items SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            
            return $this->getById($id);
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete menu item
     */
    public function delete($id) {
        try {
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $sql = "DELETE FROM menu_items WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'message' => 'Menu item deleted successfully'
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
}
?>
