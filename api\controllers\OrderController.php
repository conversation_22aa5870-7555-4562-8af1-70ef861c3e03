<?php
/**
 * Order Controller
 * Handles all order-related API operations
 */

class OrderController {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get all orders with optional filtering
     */
    public function getAll($params = []) {
        try {
            $sql = "SELECT * FROM orders WHERE 1=1";
            $bindings = [];
            
            // Apply filters
            if (!empty($params['start_date'])) {
                $sql .= " AND DATE(created_at) >= :start_date";
                $bindings[':start_date'] = $params['start_date'];
            }
            
            if (!empty($params['end_date'])) {
                $sql .= " AND DATE(created_at) <= :end_date";
                $bindings[':end_date'] = $params['end_date'];
            }
            
            if (!empty($params['status'])) {
                $sql .= " AND status = :status";
                $bindings[':status'] = $params['status'];
            }
            
            if (!empty($params['order_type'])) {
                $sql .= " AND order_type = :order_type";
                $bindings[':order_type'] = $params['order_type'];
            }
            
            if (!empty($params['payment_method'])) {
                $sql .= " AND payment_method = :payment_method";
                $bindings[':payment_method'] = $params['payment_method'];
            }
            
            // Add ordering
            $sql .= " ORDER BY created_at DESC";
            
            // Add limit if specified
            if (!empty($params['limit'])) {
                $sql .= " LIMIT :limit";
                $bindings[':limit'] = (int)$params['limit'];
            }
            
            $stmt = $this->db->prepare($sql);
            
            // Bind parameters
            foreach ($bindings as $key => $value) {
                if ($key === ':limit') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value);
                }
            }
            
            $stmt->execute();
            $orders = $stmt->fetchAll();
            
            // Decode JSON fields
            foreach ($orders as &$order) {
                $order['items'] = json_decode($order['items'], true);
            }
            
            return [
                'success' => true,
                'data' => $orders,
                'count' => count($orders),
                'filters_applied' => array_filter($params)
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get order by ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT * FROM orders WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $order = $stmt->fetch();
            
            if (!$order) {
                return [
                    'success' => false,
                    'error' => 'Order not found'
                ];
            }
            
            // Decode JSON fields
            $order['items'] = json_decode($order['items'], true);
            
            return [
                'success' => true,
                'data' => $order
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create new order
     */
    public function create($data) {
        try {
            // Validate required fields
            $required = ['items', 'total_amount'];
            foreach ($required as $field) {
                if (!isset($data[$field])) {
                    return [
                        'success' => false,
                        'error' => "Missing required field: $field"
                    ];
                }
            }
            
            // Generate order number if not provided
            if (empty($data['order_number'])) {
                $data['order_number'] = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            }
            
            $sql = "INSERT INTO orders (
                order_number, customer_name, customer_phone, order_type, status,
                items, subtotal, tax_amount, discount_amount, total_amount,
                payment_method, payment_status, notes
            ) VALUES (
                :order_number, :customer_name, :customer_phone, :order_type, :status,
                :items, :subtotal, :tax_amount, :discount_amount, :total_amount,
                :payment_method, :payment_status, :notes
            )";
            
            $stmt = $this->db->prepare($sql);
            
            $stmt->bindValue(':order_number', $data['order_number']);
            $stmt->bindValue(':customer_name', $data['customer_name'] ?? null);
            $stmt->bindValue(':customer_phone', $data['customer_phone'] ?? null);
            $stmt->bindValue(':order_type', $data['order_type'] ?? 'dine-in');
            $stmt->bindValue(':status', $data['status'] ?? 'pending');
            $stmt->bindValue(':items', json_encode($data['items']));
            $stmt->bindValue(':subtotal', $data['subtotal'] ?? $data['total_amount']);
            $stmt->bindValue(':tax_amount', $data['tax_amount'] ?? 0);
            $stmt->bindValue(':discount_amount', $data['discount_amount'] ?? 0);
            $stmt->bindValue(':total_amount', $data['total_amount']);
            $stmt->bindValue(':payment_method', $data['payment_method'] ?? 'cash');
            $stmt->bindValue(':payment_status', $data['payment_status'] ?? 'pending');
            $stmt->bindValue(':notes', $data['notes'] ?? null);
            
            $stmt->execute();
            
            $orderId = $this->db->lastInsertId();
            
            // Return the created order
            return $this->getById($orderId);
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update order
     */
    public function update($id, $data) {
        try {
            // Check if order exists
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $updateFields = [];
            $bindings = [':id' => $id];
            
            // Build dynamic update query
            $allowedFields = [
                'customer_name', 'customer_phone', 'order_type', 'status',
                'items', 'subtotal', 'tax_amount', 'discount_amount', 'total_amount',
                'payment_method', 'payment_status', 'notes'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = :$field";
                    if ($field === 'items') {
                        $bindings[":$field"] = json_encode($data[$field]);
                    } else {
                        $bindings[":$field"] = $data[$field];
                    }
                }
            }
            
            if (empty($updateFields)) {
                return [
                    'success' => false,
                    'error' => 'No valid fields to update'
                ];
            }
            
            $sql = "UPDATE orders SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            
            foreach ($bindings as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            
            // Return updated order
            return $this->getById($id);
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete order
     */
    public function delete($id) {
        try {
            // Check if order exists
            $existing = $this->getById($id);
            if (!$existing['success']) {
                return $existing;
            }
            
            $sql = "DELETE FROM orders WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'message' => 'Order deleted successfully'
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
}
?>
