<?php
/**
 * Professional Restaurant Reports Controller
 * Handles all analytics and reporting functionality
 */

class ReportsController {
    private $db;
    private $userRole;
    
    public function __construct($database, $userRole = 'viewer') {
        $this->db = $database;
        $this->userRole = $userRole;
    }
    
    /**
     * Get comprehensive dashboard data
     */
    public function getDashboardData($dateRange = '7') {
        try {
            $endDate = date('Y-m-d');
            $startDate = date('Y-m-d', strtotime("-{$dateRange} days"));
            
            $data = [
                'summary' => $this->getSummaryStats($startDate, $endDate),
                'sales_trends' => $this->getSalesTrends($startDate, $endDate),
                'top_items' => $this->getTopSellingItems($startDate, $endDate),
                'expense_breakdown' => $this->getExpenseBreakdown($startDate, $endDate),
                'peak_hours' => $this->getPeakHours($startDate, $endDate),
                'cash_flow' => $this->getCashFlow($startDate, $endDate),
                'staff_performance' => $this->getStaffPerformance($startDate, $endDate)
            ];
            
            return ['success' => true, 'data' => $data];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get summary statistics
     */
    private function getSummaryStats($startDate, $endDate) {
        $sql = "
            SELECT 
                COUNT(DISTINCT bd.business_date) as business_days,
                COALESCE(SUM(bd.total_sales), 0) as total_revenue,
                COALESCE(SUM(bd.total_expenses), 0) as total_expenses,
                COALESCE(SUM(bd.net_profit), 0) as net_profit,
                COALESCE(SUM(bd.order_count), 0) as total_orders,
                COALESCE(AVG(bd.total_sales), 0) as avg_daily_sales,
                COALESCE(SUM(ow.amount), 0) as total_withdrawals,
                (SELECT opening_balance FROM business_days WHERE business_date = ? LIMIT 1) as opening_balance,
                (SELECT closing_balance FROM business_days WHERE business_date = ? ORDER BY business_date DESC LIMIT 1) as current_balance
            FROM business_days bd
            LEFT JOIN owner_withdrawals ow ON bd.id = ow.business_day_id
            WHERE bd.business_date BETWEEN ? AND ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate, $startDate, $endDate]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get sales trends data
     */
    private function getSalesTrends($startDate, $endDate) {
        $sql = "
            SELECT 
                bd.business_date,
                bd.total_sales,
                bd.order_count,
                COALESCE(bd.total_sales / NULLIF(bd.order_count, 0), 0) as avg_order_value
            FROM business_days bd
            WHERE bd.business_date BETWEEN ? AND ?
            ORDER BY bd.business_date ASC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get top selling items
     */
    private function getTopSellingItems($startDate, $endDate, $limit = 10) {
        $sql = "
            SELECT 
                oia.item_name,
                oia.category,
                SUM(oia.quantity) as total_quantity,
                SUM(oia.total_price) as total_revenue,
                AVG(oia.unit_price) as avg_price,
                SUM(oia.profit_margin) as total_profit,
                AVG(oia.profit_percentage) as avg_profit_percentage
            FROM order_items_analytics oia
            JOIN orders_analytics oa ON oia.order_analytics_id = oa.id
            WHERE oa.order_date BETWEEN ? AND ?
            AND oa.order_status = 'completed'
            GROUP BY oia.item_name, oia.category
            ORDER BY total_quantity DESC
            LIMIT ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get expense breakdown
     */
    private function getExpenseBreakdown($startDate, $endDate) {
        $sql = "
            SELECT 
                ea.expense_category,
                COUNT(*) as transaction_count,
                SUM(ea.amount) as total_amount,
                AVG(ea.amount) as avg_amount
            FROM expenses_analytics ea
            WHERE ea.expense_date BETWEEN ? AND ?
            AND ea.status = 'paid'
            GROUP BY ea.expense_category
            ORDER BY total_amount DESC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get peak hours analysis
     */
    private function getPeakHours($startDate, $endDate) {
        $sql = "
            SELECT 
                HOUR(oa.order_time) as hour,
                COUNT(*) as order_count,
                SUM(oa.total_amount) as total_revenue,
                AVG(oa.total_amount) as avg_order_value
            FROM orders_analytics oa
            WHERE oa.order_date BETWEEN ? AND ?
            AND oa.order_status = 'completed'
            GROUP BY HOUR(oa.order_time)
            ORDER BY hour ASC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get cash flow data
     */
    private function getCashFlow($startDate, $endDate) {
        $sql = "
            SELECT 
                cfa.transaction_date,
                cfa.transaction_type,
                cfa.category,
                SUM(cfa.amount) as total_amount
            FROM cash_flow_analytics cfa
            WHERE cfa.transaction_date BETWEEN ? AND ?
            GROUP BY cfa.transaction_date, cfa.transaction_type, cfa.category
            ORDER BY cfa.transaction_date ASC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get staff performance
     */
    private function getStaffPerformance($startDate, $endDate) {
        $sql = "
            SELECT 
                sa.staff_name,
                sa.role,
                COUNT(DISTINCT sa.attendance_date) as days_worked,
                SUM(sa.hours_worked) as total_hours,
                SUM(sa.net_pay) as total_wages,
                SUM(sa.orders_served) as total_orders_served,
                SUM(sa.total_sales) as total_sales_generated,
                AVG(sa.total_sales / NULLIF(sa.orders_served, 0)) as avg_order_value
            FROM staff_analytics sa
            WHERE sa.attendance_date BETWEEN ? AND ?
            AND sa.status IN ('present', 'late')
            GROUP BY sa.staff_id, sa.staff_name, sa.role
            ORDER BY total_sales_generated DESC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Business Day Management
     */
    public function setMorningBalance($date, $amount, $notes = '') {
        try {
            $sql = "
                INSERT INTO business_days (business_date, opening_balance, notes)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                opening_balance = VALUES(opening_balance),
                notes = VALUES(notes),
                updated_at = CURRENT_TIMESTAMP
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$date, $amount, $notes]);
            
            return ['success' => true, 'message' => 'Morning balance set successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Record owner withdrawal
     */
    public function recordOwnerWithdrawal($data) {
        try {
            $this->db->beginTransaction();
            
            // Get or create business day
            $businessDayId = $this->getOrCreateBusinessDay($data['withdrawal_date']);
            
            // Insert withdrawal
            $sql = "
                INSERT INTO owner_withdrawals 
                (business_day_id, amount, description, withdrawal_type, withdrawal_date, withdrawal_time, withdrawn_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $businessDayId,
                $data['amount'],
                $data['description'],
                $data['withdrawal_type'] ?? 'cash',
                $data['withdrawal_date'],
                $data['withdrawal_time'] ?? date('H:i:s'),
                $data['withdrawn_by'] ?? null
            ]);
            
            // Update business day totals
            $this->updateBusinessDayTotals($businessDayId);
            
            $this->db->commit();
            return ['success' => true, 'message' => 'Withdrawal recorded successfully'];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * End business day
     */
    public function endBusinessDay($date, $actualClosingBalance, $notes = '') {
        try {
            $this->db->beginTransaction();
            
            $businessDayId = $this->getOrCreateBusinessDay($date);
            
            // Calculate final totals
            $totals = $this->calculateDayTotals($date);
            
            $sql = "
                UPDATE business_days SET
                closing_balance = ?,
                expected_closing = ?,
                cash_difference = ?,
                total_sales = ?,
                total_expenses = ?,
                net_profit = ?,
                order_count = ?,
                is_finalized = TRUE,
                finalized_at = CURRENT_TIMESTAMP,
                notes = ?
                WHERE id = ?
            ";
            
            $expectedClosing = $totals['opening_balance'] + $totals['total_sales'] - $totals['total_expenses'];
            $cashDifference = $actualClosingBalance - $expectedClosing;
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $actualClosingBalance,
                $expectedClosing,
                $cashDifference,
                $totals['total_sales'],
                $totals['total_expenses'],
                $totals['net_profit'],
                $totals['order_count'],
                $notes,
                $businessDayId
            ]);
            
            $this->db->commit();
            return [
                'success' => true, 
                'message' => 'Business day finalized successfully',
                'summary' => $totals
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Helper method to get or create business day
     */
    private function getOrCreateBusinessDay($date) {
        $sql = "SELECT id FROM business_days WHERE business_date = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        $result = $stmt->fetch();
        
        if ($result) {
            return $result['id'];
        }
        
        $sql = "INSERT INTO business_days (business_date) VALUES (?)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        return $this->db->lastInsertId();
    }
    
    /**
     * Calculate day totals
     */
    private function calculateDayTotals($date) {
        $sql = "
            SELECT 
                COALESCE(bd.opening_balance, 0) as opening_balance,
                COALESCE(SUM(oa.total_amount), 0) as total_sales,
                COALESCE(SUM(ea.amount), 0) as total_expenses,
                COUNT(DISTINCT oa.id) as order_count
            FROM business_days bd
            LEFT JOIN orders_analytics oa ON bd.id = oa.business_day_id AND oa.order_status = 'completed'
            LEFT JOIN expenses_analytics ea ON bd.id = ea.business_day_id AND ea.status = 'paid'
            WHERE bd.business_date = ?
            GROUP BY bd.id, bd.opening_balance
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $result['net_profit'] = $result['total_sales'] - $result['total_expenses'];
            return $result;
        }
        
        return [
            'opening_balance' => 0,
            'total_sales' => 0,
            'total_expenses' => 0,
            'net_profit' => 0,
            'order_count' => 0
        ];
    }
    
    /**
     * Update business day totals
     */
    private function updateBusinessDayTotals($businessDayId) {
        $sql = "
            UPDATE business_days bd SET
            total_expenses = (
                SELECT COALESCE(SUM(amount), 0) 
                FROM expenses_analytics 
                WHERE business_day_id = bd.id AND status = 'paid'
            ),
            net_profit = total_sales - (
                SELECT COALESCE(SUM(amount), 0) 
                FROM expenses_analytics 
                WHERE business_day_id = bd.id AND status = 'paid'
            )
            WHERE bd.id = ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$businessDayId]);
    }

    /**
     * Get withdrawal records with filtering
     */
    public function getWithdrawalRecords($startDate, $endDate, $filters = []) {
        try {
            $sql = "
                SELECT
                    ow.*,
                    bd.business_date,
                    u.name as withdrawn_by_name
                FROM owner_withdrawals ow
                JOIN business_days bd ON ow.business_day_id = bd.id
                LEFT JOIN users u ON ow.withdrawn_by = u.id
                WHERE ow.withdrawal_date BETWEEN ? AND ?
            ";

            $params = [$startDate, $endDate];

            if (!empty($filters['withdrawal_type'])) {
                $sql .= " AND ow.withdrawal_type = ?";
                $params[] = $filters['withdrawal_type'];
            }

            if (!empty($filters['min_amount'])) {
                $sql .= " AND ow.amount >= ?";
                $params[] = $filters['min_amount'];
            }

            if (!empty($filters['max_amount'])) {
                $sql .= " AND ow.amount <= ?";
                $params[] = $filters['max_amount'];
            }

            $sql .= " ORDER BY ow.withdrawal_date DESC, ow.withdrawal_time DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return ['success' => true, 'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get inventory analytics
     */
    public function getInventoryAnalytics($startDate, $endDate) {
        try {
            $sql = "
                SELECT
                    ia.item_name,
                    ia.category,
                    SUM(ia.stock_used) as total_used,
                    SUM(ia.stock_wasted) as total_wasted,
                    SUM(ia.total_cost_used) as total_cost,
                    SUM(ia.waste_cost) as total_waste_cost,
                    AVG(ia.closing_stock) as avg_stock_level,
                    COUNT(DISTINCT ia.usage_date) as usage_days
                FROM inventory_analytics ia
                WHERE ia.usage_date BETWEEN ? AND ?
                GROUP BY ia.inventory_item_id, ia.item_name, ia.category
                ORDER BY total_used DESC
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$startDate, $endDate]);

            return ['success' => true, 'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get profit analysis by item
     */
    public function getProfitAnalysis($startDate, $endDate, $groupBy = 'item') {
        try {
            $groupByClause = $groupBy === 'category' ? 'oia.category' : 'oia.item_name, oia.category';
            $selectClause = $groupBy === 'category' ? 'oia.category as name' : 'oia.item_name as name, oia.category';

            $sql = "
                SELECT
                    {$selectClause},
                    SUM(oia.quantity) as total_quantity,
                    SUM(oia.total_price) as total_revenue,
                    SUM(oia.cost_price * oia.quantity) as total_cost,
                    SUM(oia.profit_margin) as total_profit,
                    AVG(oia.profit_percentage) as avg_profit_percentage,
                    COUNT(DISTINCT oa.id) as order_count
                FROM order_items_analytics oia
                JOIN orders_analytics oa ON oia.order_analytics_id = oa.id
                WHERE oa.order_date BETWEEN ? AND ?
                AND oa.order_status = 'completed'
                GROUP BY {$groupByClause}
                ORDER BY total_profit DESC
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$startDate, $endDate]);

            return ['success' => true, 'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Export data to various formats
     */
    public function exportData($type, $format, $startDate, $endDate, $filters = []) {
        try {
            $data = [];

            switch ($type) {
                case 'dashboard':
                    $result = $this->getDashboardData(
                        (new DateTime($endDate))->diff(new DateTime($startDate))->days
                    );
                    $data = $result['data'];
                    break;

                case 'withdrawals':
                    $result = $this->getWithdrawalRecords($startDate, $endDate, $filters);
                    $data = $result['data'];
                    break;

                case 'inventory':
                    $result = $this->getInventoryAnalytics($startDate, $endDate);
                    $data = $result['data'];
                    break;

                case 'profit':
                    $result = $this->getProfitAnalysis($startDate, $endDate);
                    $data = $result['data'];
                    break;

                default:
                    throw new Exception('Invalid export type');
            }

            switch ($format) {
                case 'csv':
                    return $this->exportToCSV($data, $type);
                case 'excel':
                    return $this->exportToExcel($data, $type);
                case 'pdf':
                    return $this->exportToPDF($data, $type, $startDate, $endDate);
                default:
                    throw new Exception('Invalid export format');
            }

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Check user permissions
     */
    public function checkPermission($action) {
        $permissions = [
            'owner' => ['view', 'export', 'manage', 'approve'],
            'manager' => ['view', 'export', 'manage'],
            'cashier' => ['view'],
            'staff' => [],
            'viewer' => ['view']
        ];

        return in_array($action, $permissions[$this->userRole] ?? []);
    }

    /**
     * Get real-time alerts
     */
    public function getAlerts() {
        try {
            $alerts = [];

            // Low stock alerts
            $sql = "
                SELECT item_name, closing_stock, unit
                FROM inventory_analytics
                WHERE usage_date = CURDATE()
                AND closing_stock < (SELECT setting_value FROM reports_settings WHERE setting_key = 'low_stock_threshold')
            ";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $lowStock = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($lowStock as $item) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Low Stock Alert',
                    'message' => "{$item['item_name']} is running low ({$item['closing_stock']} {$item['unit']} remaining)",
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            }

            // High expense alerts (expenses > 20% of daily sales)
            $sql = "
                SELECT bd.business_date, bd.total_sales, bd.total_expenses
                FROM business_days bd
                WHERE bd.business_date = CURDATE()
                AND bd.total_expenses > (bd.total_sales * 0.2)
                AND bd.total_sales > 0
            ";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $highExpenses = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($highExpenses) {
                $expensePercentage = ($highExpenses['total_expenses'] / $highExpenses['total_sales']) * 100;
                $alerts[] = [
                    'type' => 'danger',
                    'title' => 'High Expense Alert',
                    'message' => "Today's expenses are {$expensePercentage:.1f}% of sales (PKR {$highExpenses['total_expenses']})",
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            }

            return ['success' => true, 'data' => $alerts];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // Export helper methods would be implemented here
    private function exportToCSV($data, $type) {
        // CSV export implementation
        return ['success' => true, 'file_url' => '/exports/data.csv'];
    }

    private function exportToExcel($data, $type) {
        // Excel export implementation
        return ['success' => true, 'file_url' => '/exports/data.xlsx'];
    }

    private function exportToPDF($data, $type, $startDate, $endDate) {
        // PDF export implementation
        return ['success' => true, 'file_url' => '/exports/report.pdf'];
    }
}
