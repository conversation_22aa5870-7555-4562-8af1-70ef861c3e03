<?php
/**
 * Restaurant Management System API
 * Main API endpoint handler
 */

// Enable CORS for frontend access
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include configuration and database connection
require_once 'config/database.php';
require_once 'controllers/OrderController.php';
require_once 'controllers/ExpenseController.php';
require_once 'controllers/MenuController.php';
require_once 'controllers/InventoryController.php';
require_once 'controllers/BusinessDayController.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = trim($_SERVER['PATH_INFO'] ?? $_SERVER['REQUEST_URI'] ?? '', '/');

// Remove query string from path
$path = strtok($path, '?');

// Remove 'api/' prefix if present
$path = preg_replace('/^api\//', '', $path);

// Parse path segments
$segments = array_filter(explode('/', $path));

try {
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('Database connection failed');
    }
    
    // Route requests to appropriate controllers
    $response = null;
    
    switch ($segments[0] ?? '') {
        case 'orders':
            $controller = new OrderController($db);
            $response = handleRequest($controller, $method, array_slice($segments, 1));
            break;
            
        case 'expenses':
            $controller = new ExpenseController($db);
            $response = handleRequest($controller, $method, array_slice($segments, 1));
            break;
            
        case 'menu-items':
            $controller = new MenuController($db);
            $response = handleRequest($controller, $method, array_slice($segments, 1));
            break;
            
        case 'inventory':
            $controller = new InventoryController($db);
            $response = handleRequest($controller, $method, array_slice($segments, 1));
            break;
            
        case 'business-days':
            $controller = new BusinessDayController($db);
            $response = handleRequest($controller, $method, array_slice($segments, 1));
            break;
            
        case 'health':
            $response = [
                'success' => true,
                'message' => 'API is healthy',
                'timestamp' => date('Y-m-d H:i:s'),
                'database' => 'connected'
            ];
            break;
            
        default:
            http_response_code(404);
            $response = [
                'success' => false,
                'error' => 'Endpoint not found',
                'available_endpoints' => [
                    'orders',
                    'expenses', 
                    'menu-items',
                    'inventory',
                    'business-days',
                    'health'
                ]
            ];
            break;
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Handle request routing to controller methods
 */
function handleRequest($controller, $method, $segments) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($method) {
        case 'GET':
            if (empty($segments)) {
                return $controller->getAll($_GET);
            } else {
                return $controller->getById($segments[0]);
            }
            
        case 'POST':
            return $controller->create($input);
            
        case 'PUT':
            if (empty($segments)) {
                throw new Exception('ID required for PUT request');
            }
            return $controller->update($segments[0], $input);
            
        case 'DELETE':
            if (empty($segments)) {
                throw new Exception('ID required for DELETE request');
            }
            return $controller->delete($segments[0]);
            
        default:
            throw new Exception('Method not allowed');
    }
}
?>
