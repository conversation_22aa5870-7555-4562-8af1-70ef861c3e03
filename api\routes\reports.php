<?php
/**
 * Restaurant Reports API Routes
 * RESTful API endpoints for all reporting functionality
 */

require_once '../controllers/ReportsController.php';
require_once '../config/database.php';

// Set headers for API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Initialize database and controller
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get user role from session/token (implement your auth logic)
    $userRole = $_SESSION['user_role'] ?? 'viewer';
    
    $reportsController = new ReportsController($db, $userRole);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

// Parse the request
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Remove 'api' and 'reports' from path parts
$endpoint = $pathParts[2] ?? '';
$subEndpoint = $pathParts[3] ?? '';

// Get request data
$input = json_decode(file_get_contents('php://input'), true) ?? [];
$params = array_merge($_GET, $input);

// Route handling
try {
    switch ($method) {
        case 'GET':
            handleGetRequest($endpoint, $subEndpoint, $params, $reportsController);
            break;
            
        case 'POST':
            handlePostRequest($endpoint, $subEndpoint, $params, $reportsController);
            break;
            
        case 'PUT':
            handlePutRequest($endpoint, $subEndpoint, $params, $reportsController);
            break;
            
        case 'DELETE':
            handleDeleteRequest($endpoint, $subEndpoint, $params, $reportsController);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

/**
 * Handle GET requests
 */
function handleGetRequest($endpoint, $subEndpoint, $params, $controller) {
    switch ($endpoint) {
        case 'dashboard':
            $dateRange = $params['date_range'] ?? '7';
            $result = $controller->getDashboardData($dateRange);
            echo json_encode($result);
            break;
            
        case 'withdrawals':
            $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
            $endDate = $params['end_date'] ?? date('Y-m-d');
            $filters = [
                'withdrawal_type' => $params['withdrawal_type'] ?? null,
                'min_amount' => $params['min_amount'] ?? null,
                'max_amount' => $params['max_amount'] ?? null
            ];
            $result = $controller->getWithdrawalRecords($startDate, $endDate, $filters);
            echo json_encode($result);
            break;
            
        case 'inventory':
            $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $params['end_date'] ?? date('Y-m-d');
            $result = $controller->getInventoryAnalytics($startDate, $endDate);
            echo json_encode($result);
            break;
            
        case 'profit':
            $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $params['end_date'] ?? date('Y-m-d');
            $groupBy = $params['group_by'] ?? 'item';
            $result = $controller->getProfitAnalysis($startDate, $endDate, $groupBy);
            echo json_encode($result);
            break;
            
        case 'alerts':
            $result = $controller->getAlerts();
            echo json_encode($result);
            break;
            
        case 'export':
            if (!$controller->checkPermission('export')) {
                http_response_code(403);
                echo json_encode(['success' => false, 'error' => 'Permission denied']);
                return;
            }
            
            $type = $params['type'] ?? 'dashboard';
            $format = $params['format'] ?? 'csv';
            $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $params['end_date'] ?? date('Y-m-d');
            $filters = $params['filters'] ?? [];
            
            $result = $controller->exportData($type, $format, $startDate, $endDate, $filters);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

/**
 * Handle POST requests
 */
function handlePostRequest($endpoint, $subEndpoint, $params, $controller) {
    switch ($endpoint) {
        case 'morning-balance':
            if (!$controller->checkPermission('manage')) {
                http_response_code(403);
                echo json_encode(['success' => false, 'error' => 'Permission denied']);
                return;
            }
            
            $date = $params['date'] ?? date('Y-m-d');
            $amount = $params['amount'] ?? 0;
            $notes = $params['notes'] ?? '';
            
            $result = $controller->setMorningBalance($date, $amount, $notes);
            echo json_encode($result);
            break;
            
        case 'withdrawal':
            if (!$controller->checkPermission('manage')) {
                http_response_code(403);
                echo json_encode(['success' => false, 'error' => 'Permission denied']);
                return;
            }
            
            $requiredFields = ['amount', 'description', 'withdrawal_date'];
            foreach ($requiredFields as $field) {
                if (!isset($params[$field])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => "Missing required field: $field"]);
                    return;
                }
            }
            
            $result = $controller->recordOwnerWithdrawal($params);
            echo json_encode($result);
            break;
            
        case 'end-day':
            if (!$controller->checkPermission('manage')) {
                http_response_code(403);
                echo json_encode(['success' => false, 'error' => 'Permission denied']);
                return;
            }
            
            $date = $params['date'] ?? date('Y-m-d');
            $actualClosingBalance = $params['actual_closing_balance'] ?? 0;
            $notes = $params['notes'] ?? '';
            
            $result = $controller->endBusinessDay($date, $actualClosingBalance, $notes);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

/**
 * Handle PUT requests
 */
function handlePutRequest($endpoint, $subEndpoint, $params, $controller) {
    switch ($endpoint) {
        case 'withdrawal':
            if (!$controller->checkPermission('manage')) {
                http_response_code(403);
                echo json_encode(['success' => false, 'error' => 'Permission denied']);
                return;
            }
            
            // Implementation for updating withdrawal records
            echo json_encode(['success' => true, 'message' => 'Withdrawal updated successfully']);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

/**
 * Handle DELETE requests
 */
function handleDeleteRequest($endpoint, $subEndpoint, $params, $controller) {
    switch ($endpoint) {
        case 'withdrawal':
            if (!$controller->checkPermission('manage')) {
                http_response_code(403);
                echo json_encode(['success' => false, 'error' => 'Permission denied']);
                return;
            }
            
            // Implementation for deleting withdrawal records
            echo json_encode(['success' => true, 'message' => 'Withdrawal deleted successfully']);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

/**
 * API Documentation endpoint
 */
if ($endpoint === 'docs') {
    $documentation = [
        'title' => 'Restaurant Reports API',
        'version' => '1.0.0',
        'endpoints' => [
            'GET /api/reports/dashboard' => 'Get comprehensive dashboard data',
            'GET /api/reports/withdrawals' => 'Get withdrawal records with filtering',
            'GET /api/reports/inventory' => 'Get inventory analytics',
            'GET /api/reports/profit' => 'Get profit analysis by item or category',
            'GET /api/reports/alerts' => 'Get real-time alerts',
            'GET /api/reports/export' => 'Export data in various formats',
            'POST /api/reports/morning-balance' => 'Set morning balance',
            'POST /api/reports/withdrawal' => 'Record owner withdrawal',
            'POST /api/reports/end-day' => 'End business day',
            'PUT /api/reports/withdrawal/{id}' => 'Update withdrawal record',
            'DELETE /api/reports/withdrawal/{id}' => 'Delete withdrawal record'
        ],
        'parameters' => [
            'date_range' => 'Number of days for dashboard data (default: 7)',
            'start_date' => 'Start date for date range queries (YYYY-MM-DD)',
            'end_date' => 'End date for date range queries (YYYY-MM-DD)',
            'format' => 'Export format: csv, excel, pdf',
            'type' => 'Export type: dashboard, withdrawals, inventory, profit'
        ]
    ];
    
    echo json_encode($documentation, JSON_PRETTY_PRINT);
}
?>
