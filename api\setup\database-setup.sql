-- Restaurant Management System Database Setup
-- Run this script to create the database and tables

-- Create database (uncomment if needed)
-- CREATE DATABASE IF NOT EXISTS zaiqa_restaurant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE zaiqa_restaurant;

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(255),
    customer_phone VARCHAR(20),
    order_type ENUM('dine-in', 'takeaway', 'delivery') DEFAULT 'dine-in',
    status ENUM('pending', 'preparing', 'ready', 'completed', 'cancelled') DEFAULT 'pending',
    items JSON NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'online') DEFAULT 'cash',
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_date (created_at),
    INDEX idx_status (status),
    INDEX idx_order_type (order_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Expenses table
CREATE TABLE IF NOT EXISTS expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category ENUM('ingredients', 'staff', 'utilities', 'rent', 'equipment', 'marketing', 'other') NOT NULL,
    subcategory VARCHAR(100),
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'bank_transfer', 'cheque') DEFAULT 'cash',
    vendor_name VARCHAR(255),
    receipt_number VARCHAR(100),
    expense_date DATE NOT NULL,
    notes TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_expense_date (expense_date),
    INDEX idx_category (category),
    INDEX idx_amount (amount)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Menu items table
CREATE TABLE IF NOT EXISTS menu_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(8,2) NOT NULL,
    cost DECIMAL(8,2) DEFAULT 0,
    image_url VARCHAR(500),
    is_available BOOLEAN DEFAULT TRUE,
    preparation_time INT DEFAULT 15,
    ingredients JSON,
    allergens JSON,
    nutritional_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_available (is_available),
    INDEX idx_price (price)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inventory table
CREATE TABLE IF NOT EXISTS inventory (
    id INT AUTO_INCREMENT PRIMARY KEY,
    item_name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    unit VARCHAR(50) NOT NULL,
    minimum_stock DECIMAL(10,2) DEFAULT 0,
    maximum_stock DECIMAL(10,2) DEFAULT 0,
    cost_per_unit DECIMAL(8,2) DEFAULT 0,
    supplier VARCHAR(255),
    last_restocked DATE,
    expiry_date DATE,
    location VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_stock_level (current_stock),
    INDEX idx_expiry (expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Business days table
CREATE TABLE IF NOT EXISTS business_days (
    id INT AUTO_INCREMENT PRIMARY KEY,
    business_date DATE NOT NULL UNIQUE,
    opening_balance DECIMAL(10,2) DEFAULT 0,
    closing_balance DECIMAL(10,2) DEFAULT 0,
    total_sales DECIMAL(10,2) DEFAULT 0,
    total_expenses DECIMAL(10,2) DEFAULT 0,
    cash_sales DECIMAL(10,2) DEFAULT 0,
    card_sales DECIMAL(10,2) DEFAULT 0,
    owner_withdrawals DECIMAL(10,2) DEFAULT 0,
    status ENUM('open', 'closed') DEFAULT 'open',
    notes TEXT,
    opened_by VARCHAR(100),
    closed_by VARCHAR(100),
    opened_at TIMESTAMP NULL,
    closed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_business_date (business_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Owner withdrawals table
CREATE TABLE IF NOT EXISTS owner_withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    amount DECIMAL(10,2) NOT NULL,
    reason VARCHAR(255) NOT NULL,
    description TEXT,
    withdrawal_date DATE NOT NULL,
    business_day_id INT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_withdrawal_date (withdrawal_date),
    INDEX idx_amount (amount)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
INSERT IGNORE INTO menu_items (name, category, description, price, cost, is_available) VALUES
('Chicken Biryani', 'Main Course', 'Aromatic basmati rice with tender chicken pieces', 15.99, 8.50, TRUE),
('Beef Karahi', 'Main Course', 'Spicy beef curry cooked in traditional karahi', 18.99, 10.00, TRUE),
('Chicken Tikka', 'Appetizers', 'Grilled chicken marinated in yogurt and spices', 12.99, 6.50, TRUE),
('Naan Bread', 'Bread', 'Fresh baked traditional bread', 3.99, 1.50, TRUE),
('Basmati Rice', 'Rice', 'Fragrant long-grain rice', 4.99, 2.00, TRUE),
('Mango Lassi', 'Beverages', 'Refreshing yogurt drink with mango', 4.99, 2.50, TRUE);

INSERT IGNORE INTO inventory (item_name, category, current_stock, unit, minimum_stock, cost_per_unit) VALUES
('Chicken Breast', 'Meat', 50.0, 'kg', 10.0, 8.50),
('Beef', 'Meat', 30.0, 'kg', 5.0, 12.00),
('Basmati Rice', 'Grains', 100.0, 'kg', 20.0, 3.50),
('Onions', 'Vegetables', 25.0, 'kg', 5.0, 1.20),
('Tomatoes', 'Vegetables', 20.0, 'kg', 5.0, 2.50),
('Yogurt', 'Dairy', 15.0, 'kg', 3.0, 4.00);

-- Insert sample orders for testing
INSERT IGNORE INTO orders (order_number, customer_name, order_type, status, items, subtotal, total_amount, payment_method, payment_status) VALUES
('ORD-********-0001', 'John Doe', 'dine-in', 'completed', 
 '[{"name":"Chicken Biryani","price":15.99,"quantity":2},{"name":"Naan Bread","price":3.99,"quantity":2}]', 
 39.96, 39.96, 'cash', 'paid'),
('ORD-********-0002', 'Jane Smith', 'takeaway', 'completed',
 '[{"name":"Beef Karahi","price":18.99,"quantity":1},{"name":"Basmati Rice","price":4.99,"quantity":1}]',
 23.98, 23.98, 'card', 'paid');

-- Insert sample expenses for testing
INSERT IGNORE INTO expenses (category, description, amount, expense_date, payment_method) VALUES
('ingredients', 'Fresh vegetables purchase', 150.00, CURDATE(), 'cash'),
('utilities', 'Electricity bill', 200.00, CURDATE(), 'bank_transfer'),
('staff', 'Chef salary', 1500.00, CURDATE(), 'bank_transfer');

-- Create today's business day
INSERT IGNORE INTO business_days (business_date, opening_balance, status, opened_by, opened_at) VALUES
(CURDATE(), 500.00, 'open', 'system', NOW());

COMMIT;
