/* Enhanced Reports Page Styles */

.enhanced-reports-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    background: #f8f9fa;
    min-height: 100vh;
}

/* Header Styles */
.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-title h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.header-subtitle {
    margin: 5px 0 0 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.header-actions .btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-1px);
}

/* Form Controls */
.form-select, .form-control {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    background: white;
}

.form-select:focus, .form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* Dropdown */
.export-dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 8px 16px;
    color: #495057;
    text-decoration: none;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #007bff;
}

/* Summary Cards */
.summary-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.revenue-card .card-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.expense-card .card-icon {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
}

.profit-card .card-icon {
    background: linear-gradient(135deg, #007bff, #6610f2);
}

.orders-card .card-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.cash-card .card-icon {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.balance-card .card-icon {
    background: linear-gradient(135deg, #6c757d, #495057);
}

.card-content h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.card-content p {
    margin: 5px 0 0 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 500;
}

.trend.positive {
    color: #28a745;
}

.trend.negative {
    color: #dc3545;
}

.trend.neutral {
    color: #6c757d;
}

/* Navigation Tabs */
.reports-navigation {
    margin-bottom: 30px;
}

.nav-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.nav-link {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link:hover {
    background: #f8f9fa;
    color: #495057;
}

.nav-link.active {
    background: #007bff;
    color: white;
}

/* Tab Content */
.tab-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 600px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* End of Day Summary */
.eod-summary-container {
    max-width: 100%;
}

.eod-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.eod-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.8rem;
}

.eod-date {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 500;
}

.eod-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.eod-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.eod-section h3 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.eod-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.eod-details {
    font-size: 0.85rem;
    color: #6c757d;
}

.cash-difference.positive {
    color: #28a745;
}

.cash-difference.negative {
    color: #dc3545;
}

.eod-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.chart-container, .table-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.chart-container h3, .table-container h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.chart-container canvas {
    max-height: 300px;
}

/* Data Tables */
.data-table-container {
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.status-danger {
    background: #f8d7da;
    color: #721c24;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
}

/* Profit/Loss Colors */
.profit-positive {
    color: #28a745 !important;
    font-weight: 600;
}

.profit-negative {
    color: #dc3545 !important;
    font-weight: 600;
}

.margin-high {
    color: #28a745;
    font-weight: 600;
}

.margin-medium {
    color: #ffc107;
    font-weight: 600;
}

.margin-low {
    color: #dc3545;
    font-weight: 600;
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-dialog {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-form .form-group {
    margin-bottom: 20px;
}

.modal-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.modal-form .form-control,
.modal-form .form-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
}

.modal-form textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

.cash-info {
    background: #e7f3ff;
    padding: 15px;
    border-radius: 6px;
    margin-top: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.warning-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 6px;
    margin-top: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.ebd-summary {
    max-width: 100%;
}

.ebd-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.ebd-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

/* No Data / Error States */
.no-data, .error-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

.error-message {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
}

/* Notifications */
.success-notification, .error-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease;
}

.success-notification {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.error-notification {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.notification-content {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    font-size: 1.2rem;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #007bff;
    margin-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-reports-container {
        padding: 10px;
    }

    .reports-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .summary-cards-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .nav-tabs {
        flex-direction: column;
    }

    .nav-link {
        justify-content: center;
    }

    .eod-grid {
        grid-template-columns: 1fr;
    }

    .eod-actions {
        flex-direction: column;
    }

    .modal-dialog {
        width: 95%;
        margin: 10px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}