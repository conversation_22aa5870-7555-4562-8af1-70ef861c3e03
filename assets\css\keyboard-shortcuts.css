/**
 * Keyboard Shortcuts System Styles
 * Modern, clean styling for the shortcuts interface
 */

/* Shortcuts Help Modal */
.shortcuts-help-modal .modal-content {
    max-width: 900px;
    max-height: 80vh;
    overflow: hidden;
}

.shortcuts-help-modal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0;
}

/* Shortcuts Tabs */
.shortcuts-tabs {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    background: #f8f9fa;
    margin: 0;
    padding: 0;
}

.shortcuts-tab {
    background: none;
    border: none;
    padding: 12px 24px;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.shortcuts-tab:hover {
    background: #e9ecef;
    color: #333;
}

.shortcuts-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: white;
}

/* Shortcuts Help Content */
.shortcuts-help-list {
    padding: 20px;
}

.shortcuts-category {
    margin-bottom: 30px;
}

.shortcuts-category h3 {
    color: #333;
    font-size: 1.2em;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e0e0e0;
    display: flex;
    align-items: center;
}

.shortcuts-category h3:before {
    content: "⌨️";
    margin-right: 8px;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 12px;
}

.shortcut-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.shortcut-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.shortcut-key {
    background: #333;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
    margin-right: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.shortcut-description {
    flex: 1;
    color: #555;
    font-size: 0.95em;
}

/* Calculator Modal */
.calculator-modal {
    max-width: 350px;
}

.calculator {
    background: #2c3e50;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.calculator-display {
    margin-bottom: 15px;
}

.calculator-display input {
    width: 100%;
    height: 60px;
    font-size: 2em;
    text-align: right;
    background: #34495e;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0 15px;
    font-family: 'Courier New', monospace;
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.calculator-buttons button {
    height: 60px;
    border: none;
    border-radius: 8px;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #3498db;
    color: white;
}

.calculator-buttons button:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.calculator-buttons button:active {
    transform: scale(0.95);
}

.calculator-zero {
    grid-column: span 2;
}

.calculator-equals {
    background: #e74c3c !important;
    grid-row: span 2;
}

/* Search Modal */
.search-modal {
    max-width: 600px;
}

.search-container input {
    width: 100%;
    padding: 15px;
    font-size: 1.1em;
    border: 2px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
}

.search-result-item {
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #f0f8ff;
    border-color: #007bff;
    transform: translateX(5px);
}

.result-type {
    font-size: 0.8em;
    color: #666;
    text-transform: uppercase;
    font-weight: bold;
}

.result-title {
    font-size: 1.1em;
    font-weight: bold;
    color: #333;
    margin: 4px 0;
}

.result-subtitle {
    color: #666;
    font-size: 0.9em;
}

.no-results {
    text-align: center;
    color: #666;
    padding: 40px;
    font-style: italic;
}

/* Customize Shortcuts */
.shortcuts-customize {
    padding: 20px;
}

.customize-header {
    margin-bottom: 25px;
}

.customize-header h4 {
    color: #333;
    margin-bottom: 8px;
}

.customize-header p {
    color: #666;
    margin: 0;
}

.customize-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.customize-shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.customize-shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    transition: all 0.3s ease;
}

.customize-shortcut-item.custom {
    border-left-color: #ffc107;
    background: #fffbf0;
}

.customize-shortcut-item:hover {
    background: #e9ecef;
    transform: translateX(3px);
}

.shortcut-info {
    flex: 1;
}

.shortcut-key-display {
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    display: inline-block;
    margin-bottom: 5px;
}

.shortcut-desc {
    font-weight: 500;
    color: #333;
    margin-bottom: 3px;
}

.shortcut-category {
    font-size: 0.8em;
    color: #666;
    text-transform: uppercase;
}

.shortcut-actions {
    display: flex;
    gap: 8px;
}

/* Custom Shortcut Modal */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1em;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.form-group button {
    margin-top: 8px;
}

/* Dark Theme Support */
.dark-theme .shortcuts-tab {
    color: #ccc;
}

.dark-theme .shortcuts-tab.active {
    color: #4dabf7;
    background: #2d3748;
}

.dark-theme .shortcuts-tabs {
    background: #1a202c;
    border-bottom-color: #4a5568;
}

.dark-theme .shortcut-item {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-theme .shortcut-item:hover {
    background: #4a5568;
}

.dark-theme .shortcut-description {
    color: #cbd5e0;
}

.dark-theme .customize-shortcut-item {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-theme .customize-shortcut-item:hover {
    background: #4a5568;
}

.dark-theme .shortcut-desc {
    color: #e2e8f0;
}

.dark-theme .shortcut-category {
    color: #a0aec0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shortcuts-help-modal .modal-content {
        max-width: 95vw;
        margin: 10px;
    }
    
    .shortcuts-grid {
        grid-template-columns: 1fr;
    }
    
    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .shortcut-key {
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    .customize-shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .shortcut-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Shortcut Activation Notification */
.shortcut-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.shortcut-notification.show {
    transform: translateX(0);
}

.shortcut-notification .shortcut-key {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

.shortcut-notification .shortcut-desc {
    font-size: 0.9em;
    font-weight: 500;
}

/* Floating Shortcuts Reference */
.floating-shortcuts-reference {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    min-width: 200px;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.floating-shortcuts-reference.collapsed {
    min-width: 150px;
}

.reference-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #333;
}

.reference-toggle {
    background: none;
    border: none;
    font-size: 1.2em;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reference-toggle:hover {
    color: #007bff;
}

.reference-content {
    padding: 10px;
}

.reference-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 8px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.reference-item:hover {
    background: #f0f8ff;
}

.ref-key {
    background: #333;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.8em;
    font-weight: bold;
    min-width: 25px;
    text-align: center;
}

.ref-desc {
    color: #666;
    font-size: 0.85em;
}

/* Animation for shortcut activation */
@keyframes shortcutActivated {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.shortcut-activated {
    animation: shortcutActivated 0.3s ease;
}

/* Dark theme support for new elements */
.dark-theme .shortcut-notification {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
}

.dark-theme .shortcut-notification .shortcut-key {
    background: #4dabf7;
}

.dark-theme .floating-shortcuts-reference {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

.dark-theme .reference-header {
    background: #1a202c;
    border-bottom-color: #4a5568;
    color: #e2e8f0;
}

.dark-theme .reference-item:hover {
    background: #4a5568;
}

.dark-theme .ref-desc {
    color: #cbd5e0;
}

/* POS Modal Keyboard Navigation Styles */
.menu-item-card.selected,
.menu-item.selected {
    border: 3px solid #007bff !important;
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.3) !important;
    transform: scale(1.02) !important;
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff) !important;
    position: relative;
    z-index: 10;
}

.menu-item-card.selected::before,
.menu-item.selected::before {
    content: "⌨️";
    position: absolute;
    top: -5px;
    right: -5px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    z-index: 11;
}

.tab-btn.keyboard-selected {
    background: #007bff !important;
    color: white !important;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* POS Shortcut Indicators */
.pos-modal .shortcut-hint {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    z-index: 10;
}

.pos-modal .menu-item-card:nth-child(1) .shortcut-hint::after { content: "1"; }
.pos-modal .menu-item-card:nth-child(2) .shortcut-hint::after { content: "2"; }
.pos-modal .menu-item-card:nth-child(3) .shortcut-hint::after { content: "3"; }
.pos-modal .menu-item-card:nth-child(4) .shortcut-hint::after { content: "4"; }
.pos-modal .menu-item-card:nth-child(5) .shortcut-hint::after { content: "5"; }

/* Cart item highlighting for keyboard navigation */
.cart-item.keyboard-focus {
    background: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
}

/* POS Shortcuts Help Overlay */
.pos-shortcuts-help {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.95);
    color: white;
    padding: 15px;
    border-radius: 10px;
    z-index: 10001;
    min-width: 250px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.pos-shortcuts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: bold;
    font-size: 1.1em;
}

.pos-shortcuts-header button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2em;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
}

.pos-shortcuts-header button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.pos-shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.pos-shortcut-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pos-shortcut-key {
    background: #007bff;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    font-weight: bold;
    min-width: 35px;
    text-align: center;
}

.pos-shortcut-desc {
    font-size: 0.9em;
    color: #e0e0e0;
}

/* Mobile responsiveness for new elements */
@media (max-width: 768px) {
    .shortcut-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }

    .shortcut-notification.show {
        transform: translateY(0);
    }

    .floating-shortcuts-reference {
        bottom: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
    }

    .reference-content {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 5px;
    }

    .menu-item-card.selected::before,
    .menu-item.selected::before {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }
}
