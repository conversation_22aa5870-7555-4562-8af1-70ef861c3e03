/* Modern Professional Reports & Analytics Page Styles */

:root {
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f1f5f9;

    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;

    --border-color: #e2e8f0;
    --border-light: #f1f5f9;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

.reports-analytics-container {
    background: var(--bg-primary);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--text-primary);
    line-height: 1.6;
}

/* Header Section */
.reports-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(8px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-title h1 {
    margin: 0;
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.header-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.header-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    box-shadow: var(--shadow-md);
}

/* Modern Professional Buttons */
.btn-modern {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    white-space: nowrap;
    box-shadow: var(--shadow-sm);
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-modern:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-primary-modern {
    background: var(--primary-color);
    color: white;
}

.btn-primary-modern:hover {
    background: var(--primary-dark);
}

.btn-success-modern {
    background: var(--success-color);
    color: white;
}

.btn-success-modern:hover {
    background: #059669;
}

.btn-warning-modern {
    background: var(--warning-color);
    color: white;
}

.btn-warning-modern:hover {
    background: #d97706;
}

.btn-danger-modern {
    background: var(--danger-color);
    color: white;
}

.btn-danger-modern:hover {
    background: #dc2626;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-outline:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Main Content */
.reports-main {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Tabs */
.dashboard-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    transition: all 0.2s ease;
    position: relative;
}

.tab-button.active {
    color: var(--primary-color);
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--primary-color);
}

.tab-button:hover:not(.active) {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

/* Quick Stats Cards */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    box-shadow: var(--shadow-md);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    background: var(--bg-tertiary);
}

.trend-up {
    color: var(--success-color);
    background: #ecfdf5;
}

.trend-down {
    color: var(--danger-color);
    background: #fef2f2;
}

.trend-neutral {
    color: var(--text-muted);
    background: var(--bg-tertiary);
}

/* Filters Section */
.filters-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filters-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.filter-input {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filter-input:hover {
    border-color: var(--text-secondary);
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
    height: fit-content;
}

.analytics-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-title i {
    color: var(--primary-color);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-action-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.card-action-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
}

/* Data Table */
.data-table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th {
    background: var(--bg-tertiary);
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-light);
    color: var(--text-secondary);
    vertical-align: middle;
}

.data-table tr:hover {
    background: var(--bg-tertiary);
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* Table badges and status indicators */
.table-badge {
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-success {
    background: #ecfdf5;
    color: var(--success-color);
}

.badge-warning {
    background: #fffbeb;
    color: var(--warning-color);
}

.badge-danger {
    background: #fef2f2;
    color: var(--danger-color);
}

.badge-info {
    background: #f0f9ff;
    color: var(--info-color);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--border-color) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--radius-md);
    height: 1rem;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-content {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-xl);
    max-width: 300px;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .reports-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .reports-main {
        padding: 1rem;
    }

    .quick-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-tabs {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .dashboard-tabs::-webkit-scrollbar {
        display: none;
    }

    .tab-button {
        white-space: nowrap;
        min-width: fit-content;
    }

    .data-table-container {
        font-size: 0.75rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .header-title h1 {
        font-size: 1.5rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .btn-modern {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    .chart-container {
        height: 250px;
    }
}

/* Print Styles */
@media print {
    .reports-header,
    .filters-section,
    .card-actions {
        display: none;
    }

    .reports-analytics-container {
        background: white;
    }

    .analytics-card,
    .stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
