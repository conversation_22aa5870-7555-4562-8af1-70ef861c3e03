/* Reports Page Styles - Copied from zaiqa3 */

/* Reports Container */
.reports-container {
    padding: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
}

.reports-header {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.header-content h2 {
    margin: 0;
    color: var(--gray-900);
    font-size: 1.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-content h2 i {
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    gap: 0.75rem;
}

.date-range {
    color: var(--gray-600);
    font-size: 0.875rem;
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.separator {
    color: var(--gray-400);
}

/* Metrics Overview */
.metrics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
}

.metric-card.revenue {
    border-left: 4px solid var(--success-color);
}

.metric-card.orders {
    border-left: 4px solid var(--primary-color);
}

.metric-card.customers {
    border-left: 4px solid var(--info-color);
}

.metric-card.profit {
    border-left: 4px solid var(--warning-color);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.metric-card.revenue .metric-icon {
    background: var(--success-color);
}

.metric-card.orders .metric-icon {
    background: var(--primary-color);
}

.metric-card.customers .metric-icon {
    background: var(--info-color);
}

.metric-card.profit .metric-icon {
    background: var(--warning-color);
}

.metric-content h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
}

.metric-content p {
    margin: 0 0 0.5rem 0;
    color: var(--gray-600);
    font-weight: 500;
}

.metric-change {
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--error-color);
}

.metric-detail {
    font-size: 0.875rem;
    color: var(--gray-500);
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.chart-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.chart-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.chart-header i {
    color: var(--primary-color);
}

/* Trend Chart */
.trend-chart {
    display: flex;
    align-items: end;
    gap: 0.5rem;
    height: 200px;
    padding: 1rem 0;
}

.trend-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    position: relative;
}

.bar {
    background: linear-gradient(to top, var(--primary-color), var(--primary-light));
    width: 100%;
    min-height: 4px;
    border-radius: 4px 4px 0 0;
    margin-bottom: auto;
    transition: all 0.3s ease;
}

.bar-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-top: 0.5rem;
    text-align: center;
}

.bar-value {
    font-size: 0.75rem;
    color: var(--gray-800);
    font-weight: 600;
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.trend-bar:hover .bar-value {
    opacity: 1;
}

/* Peak Hours Chart */
.peak-hours-chart {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    height: 200px;
    overflow-y: auto;
}

.hour-bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    background: var(--gray-50);
    position: relative;
}

.hour-fill {
    height: 20px;
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    border-radius: 10px;
    transition: width 0.3s ease;
    min-width: 4px;
}

.hour-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    min-width: 50px;
}

.hour-value {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-left: auto;
}

/* Analytics Section */
.analytics-section {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header i {
    color: var(--primary-color);
}

/* Top Items Grid */
.top-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.item-card {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.item-rank {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.item-info {
    flex: 1;
}

.item-info h5 {
    margin: 0 0 0.5rem 0;
    color: var(--gray-900);
    font-weight: 600;
    font-size: 1rem;
}

.item-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.item-stats .quantity {
    color: var(--gray-600);
}

.item-stats .revenue {
    color: var(--success-color);
    font-weight: 600;
}

.item-percentage {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.percentage-bar {
    width: 60px;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.percentage-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.item-percentage span {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.category-card {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1rem;
    border-left: 4px solid var(--info-color);
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.category-header h5 {
    margin: 0;
    color: var(--gray-900);
    font-weight: 600;
}

.category-percentage {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--info-color);
}

.category-stats {
    margin-bottom: 1rem;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.stat .label {
    color: var(--gray-600);
}

.stat .value {
    color: var(--gray-900);
    font-weight: 600;
}

.category-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.category-fill {
    height: 100%;
    background: var(--info-color);
    transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .reports-container {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .metrics-overview {
        grid-template-columns: 1fr;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .top-items-grid,
    .category-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

/* Modal Styles for Analytics */
.item-analysis-modal,
.category-insights-modal,
.end-of-day-modal {
    max-width: 800px;
    width: 90vw;
}

.analysis-grid,
.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.analysis-item,
.insight-card {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1rem;
    border-left: 4px solid var(--primary-color);
}

.analysis-item .item-rank {
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.item-details h4,
.insight-card h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.item-metrics,
.category-metrics {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.metric {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.metric .label {
    color: var(--gray-600);
}

.metric .value {
    color: var(--gray-900);
    font-weight: 600;
}

.progress-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

/* End of Day Modal */
.eod-summary {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.summary-section h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
    font-size: 1.125rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border-left: 3px solid var(--primary-color);
}

.summary-item .label {
    color: var(--gray-600);
    font-weight: 500;
}

.summary-item .value {
    color: var(--gray-900);
    font-weight: 600;
}

.top-performers {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.performer-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.performer-item .rank {
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
}

.performer-item .name {
    color: var(--gray-900);
    font-weight: 600;
}

.performer-item .quantity {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.performer-item .revenue {
    color: var(--success-color);
    font-weight: 600;
    font-size: 0.875rem;
}

.eod-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .item-analysis-modal,
    .category-insights-modal,
    .end-of-day-modal {
        width: 95vw;
        max-width: none;
    }

    .analysis-grid,
    .insights-grid {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .eod-actions {
        flex-direction: column;
    }
}

/* Additional Analytics Sections */

/* Profit Analysis Table */
.profit-analysis-table {
    overflow-x: auto;
    margin-top: 1rem;
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    background: var(--white);
    border-radius: 0.5rem;
    overflow: hidden;
}

.analysis-table th,
.analysis-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.analysis-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
}

.analysis-table .profit {
    color: var(--success-color);
    font-weight: 600;
}

.analysis-table .loss {
    color: var(--error-color);
    font-weight: 600;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.profitable {
    background: var(--success-color);
    color: white;
}

.status-badge.loss {
    background: var(--error-color);
    color: white;
}

/* Expense Grid */
.expense-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.expense-card {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1rem;
    border-left: 4px solid var(--warning-color);
    transition: all 0.3s ease;
}

.expense-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.expense-card h5 {
    margin: 0 0 0.75rem 0;
    color: var(--gray-900);
    font-weight: 600;
    font-size: 1rem;
}

.expense-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--warning-color);
    margin-bottom: 0.75rem;
}

.expense-details {
    font-size: 0.875rem;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    color: var(--gray-600);
}

/* KPI Grid */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.kpi-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.kpi-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.kpi-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.kpi-unit {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
}

/* Inventory Analytics */
.inventory-analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.inventory-stat-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.inventory-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--primary-color);
}

.stat-icon.warning {
    background: var(--warning-color);
}

.stat-icon.success {
    background: var(--success-color);
}

.stat-icon.info {
    background: var(--info-color);
}

.stat-content h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.stat-content p {
    margin: 0 0 0.25rem 0;
    color: var(--gray-600);
    font-weight: 500;
}

.stat-content small {
    color: var(--gray-500);
    font-size: 0.75rem;
}

/* Top Usage Items */
.top-usage-items {
    margin-top: 2rem;
}

.top-usage-items h5 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.usage-items-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.usage-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.usage-item:hover {
    background: var(--gray-100);
}

.usage-rank {
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
}

.usage-name {
    color: var(--gray-900);
    font-weight: 600;
}

.usage-quantity {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.usage-reason {
    color: var(--gray-500);
    font-size: 0.75rem;
}

/* Financial Health Grid */
.financial-health-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
    margin-bottom: 2rem;
}

.financial-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.financial-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.financial-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
}

.financial-header h5 {
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.financial-card.revenue .financial-header {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    color: #166534;
    border: 1px solid #bbf7d0;
}

.financial-card.expenses .financial-header {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: #991b1b;
    border: 1px solid #fecaca;
}

.financial-card.profit .financial-header {
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.financial-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.financial-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-100);
    font-size: 0.9rem;
}

.financial-item:last-child {
    border-bottom: none;
}

.financial-item.total {
    border-top: 2px solid var(--gray-200);
    padding-top: 0.75rem;
    margin-top: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
}

.financial-item span:first-child {
    color: var(--gray-700);
    font-weight: 500;
}

.amount {
    font-weight: 600;
    font-size: 0.95rem;
}

.amount.positive {
    color: #059669;
}

.amount.negative {
    color: #dc2626;
}

.amount.info {
    color: var(--primary-color);
}

/* Financial Breakdown Modal */
.financial-breakdown-content {
    max-height: 70vh;
    overflow-y: auto;
}

.breakdown-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.breakdown-section h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.breakdown-grid {
    display: grid;
    gap: 0.75rem;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 0.375rem;
    border: 1px solid var(--gray-200);
}

.breakdown-item.total {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    border-color: var(--primary-color);
}

.breakdown-item span:last-child {
    font-weight: 600;
}

.breakdown-item .positive {
    color: #059669;
}

.breakdown-item .negative {
    color: #dc2626;
}

.financial-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.financial-card.revenue {
    border-left: 4px solid var(--success-color);
}

.financial-card.expenses {
    border-left: 4px solid var(--error-color);
}

.financial-card.profit {
    border-left: 4px solid var(--primary-color);
}

.financial-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
}

.financial-header h5 {
    margin: 0;
    color: var(--gray-900);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.financial-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.financial-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.financial-item.total {
    border-top: 1px solid var(--gray-200);
    padding-top: 0.75rem;
    margin-top: 0.5rem;
}

.amount.positive {
    color: var(--success-color);
    font-weight: 600;
}

.amount.negative {
    color: var(--error-color);
    font-weight: 600;
}

.amount.info {
    color: var(--primary-color);
    font-weight: 600;
}

/* Customer Analytics */
.customer-analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.customer-metric {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.customer-metric:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.customer-metric .metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    background: var(--primary-color);
}

.customer-metric .metric-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.customer-metric .metric-content p {
    margin: 0 0 0.25rem 0;
    color: var(--gray-600);
    font-weight: 500;
    font-size: 0.875rem;
}

.customer-metric .metric-content small {
    color: var(--gray-500);
    font-size: 0.75rem;
}

/* Customer Segments */
.customer-segments {
    margin-top: 2rem;
}

.customer-segments h5 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.segments-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.segment-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.segment-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 150px;
}

.segment-name {
    color: var(--gray-900);
    font-weight: 600;
    font-size: 0.875rem;
}

.segment-count {
    color: var(--gray-600);
    font-size: 0.75rem;
}

.segment-bar {
    flex: 1;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.segment-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.segment-percentage {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
    min-width: 40px;
    text-align: right;
}

/* Enhanced Analytics Modals */
.inventory-analytics-modal,
.financial-breakdown-modal {
    max-width: 900px;
    width: 90vw;
}

.analytics-tabs {
    display: flex;
    border-bottom: 2px solid var(--gray-200);
    margin-bottom: 2rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-content {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Usage Analysis */
.usage-analysis {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.usage-detail-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
    border-left: 4px solid var(--primary-color);
}

.usage-detail-item .usage-rank {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
}

.usage-info h5 {
    margin: 0 0 0.5rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.usage-metrics {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.usage-metrics .metric {
    color: var(--gray-600);
}

/* Stock Analysis */
.stock-analysis,
.cost-analysis {
    margin-top: 1rem;
}

.stock-summary,
.cost-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border-left: 3px solid var(--primary-color);
}

.summary-item .label {
    color: var(--gray-600);
    font-weight: 500;
}

.summary-item .value {
    color: var(--gray-900);
    font-weight: 600;
}

.summary-item .value.warning {
    color: var(--warning-color);
}

.summary-item .value.error {
    color: var(--error-color);
}

/* Breakdown Charts */
.breakdown-chart,
.expense-breakdown-chart {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.chart-item,
.expense-chart-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.chart-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.chart-bar,
.expense-bar {
    height: 20px;
    background: var(--gray-200);
    border-radius: 10px;
    overflow: hidden;
}

.chart-bar.revenue .bar-fill {
    background: linear-gradient(to right, var(--success-color), #10b981);
}

.expense-bar .bar-fill {
    background: linear-gradient(to right, var(--error-color), #ef4444);
}

.bar-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

/* Profit Breakdown */
.profit-breakdown {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.profit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border-left: 3px solid var(--primary-color);
}

.profit-item .label {
    color: var(--gray-600);
    font-weight: 500;
}

.profit-item .value {
    font-weight: 600;
}

.profit-item .value.positive {
    color: var(--success-color);
}

.profit-item .value.negative {
    color: var(--error-color);
}

/* Responsive Design for New Sections */
@media (max-width: 768px) {
    .inventory-analytics-modal,
    .financial-breakdown-modal {
        width: 95vw;
        max-width: none;
    }

    .analytics-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: left;
        border-bottom: 1px solid var(--gray-200);
        border-right: none;
    }

    .tab-btn.active {
        border-bottom-color: var(--gray-200);
        border-left: 3px solid var(--primary-color);
        background: var(--gray-50);
    }

    .financial-health-grid,
    .customer-analytics-grid,
    .inventory-analytics-grid,
    .expense-grid,
    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .usage-detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .stock-summary,
    .cost-summary {
        grid-template-columns: 1fr;
    }

    .profit-analysis-table {
        font-size: 0.75rem;
    }

    .analysis-table th,
    .analysis-table td {
        padding: 0.5rem;
    }
}

/* Date Filter Section */
.date-filter-section {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.date-filter-controls {
    display: flex;
    align-items: end;
    gap: 1rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
}

.filter-group .form-control {
    padding: 0.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    min-width: 140px;
}

.current-period {
    color: var(--gray-600);
    font-size: 0.875rem;
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Owner Withdrawals Section */
.withdrawals-overview {
    margin-top: 1rem;
}

.withdrawal-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.withdrawal-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.withdrawal-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.withdrawal-card.total {
    border-left: 4px solid var(--error-color);
}

.withdrawal-card.count {
    border-left: 4px solid var(--primary-color);
}

.withdrawal-card.average {
    border-left: 4px solid var(--info-color);
}

.withdrawal-card.recent {
    border-left: 4px solid var(--warning-color);
}

.withdrawal-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.withdrawal-card.total .card-icon {
    background: var(--error-color);
}

.withdrawal-card.count .card-icon {
    background: var(--primary-color);
}

.withdrawal-card.average .card-icon {
    background: var(--info-color);
}

.withdrawal-card.recent .card-icon {
    background: var(--warning-color);
}

.withdrawal-card .card-content h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.withdrawal-card .card-content p {
    margin: 0 0 0.25rem 0;
    color: var(--gray-600);
    font-weight: 500;
    font-size: 0.875rem;
}

.withdrawal-card .card-content small {
    color: var(--gray-500);
    font-size: 0.75rem;
}

/* Recent Withdrawals */
.recent-withdrawals {
    margin-top: 2rem;
}

.recent-withdrawals h5 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.withdrawals-table {
    overflow-x: auto;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
}

.withdrawal-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    background: var(--white);
}

.withdrawal-table th,
.withdrawal-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.withdrawal-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
}

.withdrawal-table .amount {
    color: var(--error-color);
    font-weight: 600;
}

.method-badge,
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.method-badge.cash {
    background: var(--success-color);
    color: white;
}

.method-badge.bank_transfer {
    background: var(--primary-color);
    color: white;
}

.method-badge.cheque {
    background: var(--warning-color);
    color: white;
}

.status-badge.completed {
    background: var(--success-color);
    color: white;
}

.status-badge.pending {
    background: var(--warning-color);
    color: white;
}

.status-badge.cancelled {
    background: var(--error-color);
    color: white;
}

/* Withdrawal Modal Styles */
.withdrawal-details-modal,
.add-withdrawal-modal {
    max-width: 900px;
    width: 90vw;
}

.withdrawal-filters {
    display: flex;
    gap: 1rem;
    align-items: end;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    flex-wrap: wrap;
}

.withdrawal-summary {
    margin-bottom: 1.5rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border-left: 3px solid var(--primary-color);
}

.summary-card .label {
    color: var(--gray-600);
    font-weight: 500;
}

.summary-card .value {
    color: var(--gray-900);
    font-weight: 600;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.form-group .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.3s ease;
}

.form-group .form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

/* Responsive Design for New Features */
@media (max-width: 768px) {
    .date-filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .withdrawal-summary-cards {
        grid-template-columns: 1fr;
    }

    .withdrawal-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .withdrawal-details-modal,
    .add-withdrawal-modal {
        width: 95vw;
        max-width: none;
    }
}
