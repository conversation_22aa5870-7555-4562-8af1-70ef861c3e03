/**
 * Zaiqa AI Assistant - Advanced Restaurant Management System with Full Automation
 * Provides intelligent insights, predictions, natural language interface, and system management
 */

class ZaiqaAIAssistant {
    constructor() {
        this.isInitialized = false;
        this.isOnline = false;
        this.localModelEndpoint = 'http://localhost:11434'; // Ollama default
        this.fallbackEndpoint = 'http://localhost:1234'; // LM Studio default
        this.currentModel = null;
        this.conversationHistory = [];
        this.learningData = this.loadLearningData();
        this.insights = [];
        this.predictions = {};

        // Enhanced system management capabilities
        this.systemMonitoring = true;
        this.autoCorrection = true;
        this.fileSystemAccess = true;
        this.automationQueue = [];
        this.errorLog = [];
        this.lastSystemCheck = null;
        this.pendingActions = [];
        this.executedActions = [];

        // Enhanced connectivity and error tolerance
        this.errorTolerance = true;
        this.persistentConnectivity = true;
        this.internetIntegration = true;
        this.detailedResponseMode = true;
        this.connectionRetryAttempts = 0;
        this.maxRetryAttempts = 5;
        this.connectionCheckInterval = null;
        this.lastConnectionCheck = null;
        this.externalDataSources = {};
        this.weatherData = null;
        this.marketTrends = null;

        // Unrestricted command processing
        this.unrestrictedMode = true;
        this.safetyRestrictions = false;
        this.commandLimitations = false;

        // Real internet AI integration
        this.internetAIServices = {
            openai: { enabled: true, apiKey: null, model: 'gpt-4' },
            claude: { enabled: true, apiKey: null, model: 'claude-3-sonnet-20240229' },
            gemini: { enabled: true, apiKey: null, model: 'gemini-pro' },
            cohere: { enabled: true, apiKey: null, model: 'command' },
            openrouter: {
                enabled: true,
                apiKey: null,
                model: 'mistralai/mistral-7b-instruct:free',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                freeModels: [
                    'mistralai/mistral-7b-instruct:free',
                    'meta-llama/llama-3.1-8b-instruct:free',
                    'microsoft/phi-3-mini-128k-instruct:free',
                    'nousresearch/nous-capybara-7b:free',
                    'openchat/openchat-7b:free'
                ]
            }
        };
        this.preferredAIService = 'local'; // local, openai, claude, gemini, cohere, openrouter
        this.fallbackAIServices = ['openrouter', 'openai', 'claude', 'gemini', 'cohere'];

        // Multi-language support with Roman Urdu
        this.multiLanguageSupport = true;
        this.supportedLanguages = ['en', 'ur', 'roman_urdu', 'mixed'];
        this.currentLanguage = 'mixed'; // auto-detect
        this.translationService = true;
        this.culturalContext = 'pakistani';
        this.localTerminology = {};
        this.urduPatterns = {};
        this.romanUrduPatterns = {};
        this.mixedLanguagePatterns = {};
        this.romanUrduVocabulary = {};
        this.romanUrduNumbers = {};

        // Unlimited system capabilities
        this.unlimitedAccess = true;
        this.fileSystemPermissions = 'full'; // read, write, create, edit, delete
        this.dynamicPageCreation = true;
        this.realtimeFeatureDevelopment = true;
        this.selfImprovementCapabilities = true;
        this.completeDatabaseAccess = true;
        this.systemModificationRights = true;

        // Human-like intelligence
        this.contextualUnderstanding = true;
        this.predictiveCapabilities = true;
        this.intelligentAssumptions = true;
        this.naturalConversationFlow = true;
        this.emotionalIntelligence = true;
        this.proactiveActions = true;
        this.commonSenseReasoning = true;

        // Advanced learning and adaptation
        this.realtimeLearning = true;
        this.internetResearch = true;
        this.autonomousImprovement = true;
        this.expertLevelDecisionMaking = true;

        // Natural language command patterns with fuzzy matching
        this.commandPatterns = this.initializeCommandPatterns();
        this.fuzzyMatchingThreshold = 0.7;
        this.contextHistory = [];
        this.commonTypos = this.initializeTypoCorrections();
        this.synonyms = this.initializeSynonyms();

        // System monitoring intervals
        this.monitoringIntervals = {};

        // Load saved preferences
        this.loadSavedPreferences();

        this.init();
    }

    async init() {
        try {
            console.log('🤖 Initializing Zaiqa AI Assistant...');

            // Check for local AI availability
            await this.detectLocalAI();

            // Initialize learning system
            this.initializeLearningSystem();

            // Start background analysis
            this.startBackgroundAnalysis();

            // Setup UI components
            this.setupAIInterface();

            // Initialize system management
            this.initializeSystemManagement();

            // Start system monitoring
            this.startSystemMonitoring();

            // Initialize enhanced connectivity
            this.initializePersistentConnectivity();

            // Initialize internet integration
            this.initializeInternetIntegration();

            // Initialize internet AI services
            this.initializeInternetAIServices();

            // Initialize multi-language support with Roman Urdu
            this.initializeMultiLanguageSupport();

            // Initialize Roman Urdu processing
            this.initializeRomanUrduSupport();

            // Initialize unlimited system capabilities
            this.initializeUnlimitedCapabilities();

            // Initialize human-like intelligence
            this.initializeHumanLikeIntelligence();

            // Initialize voice recognition
            this.initializeVoiceRecognition();

            // Show unrestricted mode indicator
            this.showUnrestrictedModeIndicator();

            this.isInitialized = true;
            console.log('✅ Unrestricted Zaiqa AI Assistant with Multi-Language & Internet AI Support initialized successfully');
        } catch (error) {
            console.error('❌ AI Assistant initialization failed:', error);
            this.isInitialized = false;
            // Continue with limited functionality
            this.setupBasicInterface();
        }
    }

    async detectLocalAI() {
        const endpoints = [
            { url: this.localModelEndpoint, name: 'Ollama' },
            { url: this.fallbackEndpoint, name: 'LM Studio' },
            { url: 'http://localhost:8080', name: 'Text Generation WebUI' },
            { url: 'http://localhost:5000', name: 'Custom AI Server' }
        ];

        for (const endpoint of endpoints) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 3000);

                const response = await fetch(`${endpoint.url}/api/tags`, {
                    method: 'GET',
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    this.localModelEndpoint = endpoint.url;
                    this.isOnline = true;
                    console.log(`✅ Connected to ${endpoint.name} at ${endpoint.url}`);

                    // Get available models
                    try {
                        const models = await response.json();
                        this.currentModel = models.models?.[0]?.name || 'llama2';
                    } catch (jsonError) {
                        console.log('Using default model due to JSON parse error');
                        this.currentModel = 'llama2';
                    }
                    break;
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    console.log(`⏱️ ${endpoint.name} connection timeout at ${endpoint.url}`);
                } else {
                    console.log(`❌ ${endpoint.name} not available at ${endpoint.url}`);
                }
            }
        }

        if (!this.isOnline) {
            console.log('⚠️ No local AI detected. Running in offline mode with rule-based insights.');
        }
    }

    setupBasicInterface() {
        try {
            // Setup minimal interface without AI features
            this.createAIChatWidget();
            this.createAIInsightsPanel();
            console.log('✅ Basic AI interface setup complete');
        } catch (error) {
            console.error('Failed to setup basic interface:', error);
        }
    }

    initializeLearningSystem() {
        try {
            // Load historical patterns
            this.patterns = {
                sales: this.analyzeSalesPatterns(),
                inventory: this.analyzeInventoryPatterns(),
                customer: this.analyzeCustomerPatterns(),
                operational: this.analyzeOperationalPatterns()
            };

            // Initialize prediction models
            this.predictionModels = {
                inventory: new InventoryPredictor(this.patterns.inventory),
                sales: new SalesPredictor(this.patterns.sales),
                staffing: new StaffingPredictor(this.patterns.operational)
            };

            console.log('✅ Learning system initialized');
        } catch (error) {
            console.error('Failed to initialize learning system:', error);
            // Initialize with empty patterns
            this.patterns = { sales: {}, inventory: {}, customer: {}, operational: {} };
            this.predictionModels = {};
        }
    }

    startBackgroundAnalysis() {
        try {
            // Run analysis every 5 minutes
            this.analysisInterval = setInterval(() => {
                this.performBackgroundAnalysis();
            }, 5 * 60 * 1000);

            // Initial analysis with delay
            setTimeout(() => {
                this.performBackgroundAnalysis();
            }, 3000);

            console.log('✅ Background analysis started');
        } catch (error) {
            console.error('Failed to start background analysis:', error);
        }
    }

    async performBackgroundAnalysis() {
        try {
            // Analyze current data
            const currentData = this.gatherCurrentData();
            
            // Generate insights
            const newInsights = await this.generateInsights(currentData);
            
            // Update predictions
            this.updatePredictions(currentData);
            
            // Check for anomalies
            const anomalies = this.detectAnomalies(currentData);
            
            // Update UI with new insights
            this.updateInsightsDisplay(newInsights, anomalies);
            
            // Learn from new data
            this.updateLearningData(currentData);
            
        } catch (error) {
            console.error('Background analysis error:', error);
        }
    }

    gatherCurrentData() {
        try {
            return {
                orders: this.safeParseJSON(localStorage.getItem('restaurantOrders'), []),
                inventory: this.safeParseJSON(localStorage.getItem('inventoryItems'), []),
                staff: this.safeParseJSON(localStorage.getItem('staffMembers'), []),
                expenses: this.safeParseJSON(localStorage.getItem('expenses'), []),
                udhars: this.safeParseJSON(localStorage.getItem('udhars'), []),
                khata: this.safeParseJSON(localStorage.getItem('khata'), []),
                tables: this.safeParseJSON(localStorage.getItem('restaurantTables'), []),
                menuItems: this.safeParseJSON(localStorage.getItem('menuItems'), []),
                timestamp: new Date().toISOString(),
                dayOfWeek: new Date().getDay(),
                hour: new Date().getHours(),
                weather: this.getWeatherData() // If available
            };
        } catch (error) {
            console.error('Error gathering current data:', error);
            return {
                orders: [], inventory: [], staff: [], expenses: [],
                udhars: [], khata: [], tables: [], menuItems: [],
                timestamp: new Date().toISOString(),
                dayOfWeek: new Date().getDay(),
                hour: new Date().getHours(),
                weather: null
            };
        }
    }

    initializeCommandPatterns() {
        return {
            // Sales Management Commands
            sales: [
                {
                    pattern: /customer\s+(\w+)\s+bought\s+items?\s+worth\s+(\d+)\s*(?:rupees?|pkr|rs)?/i,
                    action: 'addSaleTransaction',
                    params: ['customer', 'amount']
                },
                {
                    pattern: /table\s+(\d+):\s*(.+?),\s*(\d+)\s+people?,\s*(.+?),\s*bill\s+done/i,
                    action: 'processTableOrder',
                    params: ['tableNumber', 'items', 'customerCount', 'additionalItems']
                },
                {
                    pattern: /add\s+sale\s+of\s+(\d+)\s*(?:rupees?|pkr|rs)?/i,
                    action: 'addQuickSale',
                    params: ['amount']
                }
            ],

            // Expense Management Commands
            expenses: [
                {
                    pattern: /bought\s+(.+?)\s+for\s+(\d+)\s*(?:rupees?|pkr|rs)?/i,
                    action: 'addExpense',
                    params: ['item', 'amount']
                },
                {
                    pattern: /staff\s+member\s+(.+?)\s+took\s+(\d+)\s*(?:rupees?|pkr|rs)?\s+advance/i,
                    action: 'addStaffAdvance',
                    params: ['staffName', 'amount']
                },
                {
                    pattern: /paid\s+(\d+)\s*(?:rupees?|pkr|rs)?\s+for\s+(.+)/i,
                    action: 'addExpense',
                    params: ['amount', 'description']
                }
            ],

            // Menu & Pricing Commands
            menu: [
                {
                    pattern: /set\s+(.+?)\s+price\s+to\s+(\d+)\s*(?:rupees?|pkr|rs)?/i,
                    action: 'updateMenuPrice',
                    params: ['itemName', 'price']
                },
                {
                    pattern: /add\s+menu\s+item\s+(.+?)\s+for\s+(\d+)\s*(?:rupees?|pkr|rs)?/i,
                    action: 'addMenuItem',
                    params: ['itemName', 'price']
                },
                {
                    pattern: /remove\s+(.+?)\s+from\s+menu/i,
                    action: 'removeMenuItem',
                    params: ['itemName']
                }
            ],

            // Inventory Commands
            inventory: [
                {
                    pattern: /add\s+(\d+)\s+(.+?)\s+to\s+inventory/i,
                    action: 'addInventoryItem',
                    params: ['quantity', 'itemName']
                },
                {
                    pattern: /use\s+(\d+)\s+(.+?)\s+from\s+inventory/i,
                    action: 'useInventoryItem',
                    params: ['quantity', 'itemName']
                },
                {
                    pattern: /check\s+(.+?)\s+stock/i,
                    action: 'checkInventoryStock',
                    params: ['itemName']
                }
            ],

            // Staff Management Commands
            staff: [
                {
                    pattern: /(.+?)\s+worked\s+today/i,
                    action: 'markStaffAttendance',
                    params: ['staffName']
                },
                {
                    pattern: /pay\s+(.+?)\s+daily\s+wage/i,
                    action: 'payStaffDaily',
                    params: ['staffName']
                },
                {
                    pattern: /add\s+staff\s+member\s+(.+?)\s+with\s+salary\s+(\d+)/i,
                    action: 'addStaffMember',
                    params: ['staffName', 'salary']
                }
            ],

            // System Commands
            system: [
                {
                    pattern: /check\s+system\s+errors?/i,
                    action: 'checkSystemErrors',
                    params: []
                },
                {
                    pattern: /fix\s+all\s+errors?/i,
                    action: 'autoFixErrors',
                    params: []
                },
                {
                    pattern: /backup\s+data/i,
                    action: 'backupSystemData',
                    params: []
                },
                {
                    pattern: /generate\s+report/i,
                    action: 'generateComprehensiveReport',
                    params: []
                }
            ]
        };
    }

    initializeSystemManagement() {
        console.log('🔧 Initializing System Management...');

        // Initialize error detection with try-catch
        try {
            this.errorDetectors = {
                orderValidation: this.createOrderValidator(),
                staffPaymentTracking: this.createStaffPaymentTracker(),
                dataIntegrity: this.createDataIntegrityChecker(),
                inventoryConsistency: this.createInventoryConsistencyChecker()
            };
        } catch (error) {
            console.log('⚠️ Error detector initialization failed, using basic mode:', error.message);
            this.errorDetectors = {
                orderValidation: () => true,
                staffPaymentTracking: () => [],
                dataIntegrity: () => true,
                inventoryConsistency: () => true
            };
        }

        // Initialize automation handlers with try-catch
        try {
            this.automationHandlers = {
                sales: this.createSalesAutomation(),
                expenses: this.createExpenseAutomation(),
                menu: this.createMenuAutomation(),
                inventory: this.createInventoryAutomation(),
                staff: this.createStaffAutomation(),
                system: this.createSystemAutomation()
            };
        } catch (error) {
            console.log('⚠️ Automation handler initialization failed, using basic mode:', error.message);
            this.automationHandlers = {
                sales: { process: () => ({ success: true, message: 'Basic sales processing' }) },
                expenses: { process: () => ({ success: true, message: 'Basic expense processing' }) },
                menu: { process: () => ({ success: true, message: 'Basic menu processing' }) },
                inventory: { process: () => ({ success: true, message: 'Basic inventory processing' }) },
                staff: { process: () => ({ success: true, message: 'Basic staff processing' }) },
                system: { process: () => ({ success: true, message: 'Basic system processing' }) }
            };
        }

        console.log('✅ System Management initialized');
    }

    // Missing function implementations
    createOrderValidator() {
        return {
            validate: (order) => {
                try {
                    return order && order.amount && order.amount > 0;
                } catch (error) {
                    console.log('Order validation error:', error);
                    return true; // Default to valid
                }
            }
        };
    }

    createStaffPaymentTracker() {
        return {
            track: (payment) => {
                try {
                    return payment && payment.staffName && payment.amount;
                } catch (error) {
                    console.log('Staff payment tracking error:', error);
                    return [];
                }
            }
        };
    }

    createDataIntegrityChecker() {
        return {
            check: (data) => {
                try {
                    return data !== null && data !== undefined;
                } catch (error) {
                    console.log('Data integrity check error:', error);
                    return true;
                }
            }
        };
    }

    createInventoryConsistencyChecker() {
        return {
            check: (inventory) => {
                try {
                    return inventory && Array.isArray(inventory);
                } catch (error) {
                    console.log('Inventory consistency check error:', error);
                    return true;
                }
            }
        };
    }

    initializeTypoCorrections() {
        return {
            // Common restaurant-related typos
            'karhai': 'karahi',
            'karhahi': 'karahi',
            'karai': 'karahi',
            'biryani': 'biryani',
            'biriyani': 'biryani',
            'biryaani': 'biryani',
            'pepsi': 'pepsi',
            'pesi': 'pepsi',
            'pepci': 'pepsi',
            'coke': 'coke',
            'coca': 'coke',
            'coce': 'coke',
            'roti': 'roti',
            'rooti': 'roti',
            'naan': 'naan',
            'nan': 'naan',
            'chicken': 'chicken',
            'chiken': 'chicken',
            'chickan': 'chicken',
            'beef': 'beef',
            'beaf': 'beef',
            'mutton': 'mutton',
            'muton': 'mutton',
            'rice': 'rice',
            'ryce': 'rice',
            'dal': 'dal',
            'daal': 'dal',
            'table': 'table',
            'tabel': 'table',
            'customer': 'customer',
            'custmer': 'customer',
            'customr': 'customer',
            'staff': 'staff',
            'staf': 'staff',
            'expense': 'expense',
            'expence': 'expense',
            'expens': 'expense',
            'bought': 'bought',
            'bough': 'bought',
            'bot': 'bought',
            'rupees': 'rupees',
            'rupee': 'rupees',
            'rupes': 'rupees',
            'rs': 'rupees',
            'pkr': 'rupees',
            'advance': 'advance',
            'advnce': 'advance',
            'advace': 'advance',
            'salary': 'salary',
            'salry': 'salary',
            'wage': 'wage',
            'wages': 'wage',
            'inventory': 'inventory',
            'inventroy': 'inventory',
            'inventry': 'inventory',
            'stock': 'stock',
            'stok': 'stock',
            'check': 'check',
            'chek': 'check',
            'chck': 'check',
            'generate': 'generate',
            'genrate': 'generate',
            'generete': 'generate',
            'report': 'report',
            'reprt': 'report',
            'reort': 'report',
            'backup': 'backup',
            'bakup': 'backup',
            'backp': 'backup',
            'error': 'error',
            'eror': 'error',
            'erro': 'error',
            'system': 'system',
            'systm': 'system',
            'sistem': 'system'
        };
    }

    initializeSynonyms() {
        return {
            // Action synonyms
            'add': ['add', 'create', 'make', 'insert', 'put', 'include', 'enter'],
            'remove': ['remove', 'delete', 'take out', 'eliminate', 'drop', 'cancel'],
            'update': ['update', 'change', 'modify', 'edit', 'alter', 'set'],
            'check': ['check', 'see', 'look', 'view', 'show', 'display', 'tell me'],
            'buy': ['buy', 'bought', 'purchase', 'purchased', 'get', 'got'],
            'sell': ['sell', 'sold', 'sale'],
            'pay': ['pay', 'paid', 'give money', 'payment'],
            'work': ['work', 'worked', 'job', 'shift'],

            // Item synonyms
            'karahi': ['karahi', 'karhai', 'curry', 'gravy'],
            'biryani': ['biryani', 'biriyani', 'rice dish'],
            'drink': ['drink', 'beverage', 'pepsi', 'coke', 'juice', 'water'],
            'bread': ['bread', 'roti', 'naan', 'chapati'],
            'meat': ['meat', 'chicken', 'beef', 'mutton', 'gosht'],

            // Money synonyms
            'money': ['money', 'rupees', 'rs', 'pkr', 'cash', 'amount'],
            'price': ['price', 'cost', 'rate', 'amount', 'charge'],

            // People synonyms
            'customer': ['customer', 'client', 'guest', 'person'],
            'staff': ['staff', 'employee', 'worker', 'member'],

            // Time synonyms
            'today': ['today', 'aaj', 'now', 'current'],
            'yesterday': ['yesterday', 'kal', 'previous day'],

            // Status synonyms
            'done': ['done', 'complete', 'finished', 'ready', 'completed'],
            'fix': ['fix', 'repair', 'solve', 'correct', 'resolve']
        };
    }

    initializePersistentConnectivity() {
        console.log('🔗 Initializing persistent connectivity...');

        // Start connection monitoring
        this.connectionCheckInterval = setInterval(() => {
            this.checkAndMaintainConnection();
        }, 10000); // Check every 10 seconds

        // Initial connection check
        this.checkAndMaintainConnection();

        console.log('✅ Persistent connectivity initialized');
    }

    initializeInternetIntegration() {
        console.log('🌐 Initializing internet integration...');

        // Check internet connectivity
        this.checkInternetConnectivity().then(isOnline => {
            if (isOnline) {
                this.initializeExternalDataSources();
                this.startPeriodicDataUpdates();
            }
        });

        console.log('✅ Internet integration initialized');
    }

    initializeInternetAIServices() {
        console.log('🌐 Initializing Internet AI Services...');

        // Load API keys from localStorage if available
        const savedKeys = localStorage.getItem('zaiqaAIKeys');
        if (savedKeys) {
            try {
                const keys = JSON.parse(savedKeys);
                Object.keys(this.internetAIServices).forEach(service => {
                    if (keys[service]) {
                        this.internetAIServices[service].apiKey = keys[service];
                    }
                });
            } catch (error) {
                console.log('Failed to load saved API keys:', error);
            }
        }

        // Test internet AI services availability
        this.testInternetAIServices();

        console.log('✅ Internet AI Services initialized');
    }

    initializeMultiLanguageSupport() {
        console.log('🌍 Initializing Multi-Language Support...');

        // Initialize local Pakistani restaurant terminology
        this.localTerminology = this.initializeLocalTerminology();

        // Initialize Urdu command patterns
        this.urduPatterns = this.initializeUrduPatterns();

        // Initialize mixed language patterns
        this.mixedLanguagePatterns = this.initializeMixedLanguagePatterns();

        // Setup language detection
        this.setupLanguageDetection();

        console.log('✅ Multi-Language Support initialized');
    }

    initializeRomanUrduSupport() {
        console.log('🔤 Initializing Roman Urdu Support...');

        // Initialize Roman Urdu vocabulary
        this.romanUrduVocabulary = this.initializeRomanUrduVocabulary();

        // Initialize Roman Urdu numbers
        this.romanUrduNumbers = this.initializeRomanUrduNumbers();

        // Initialize Roman Urdu command patterns
        this.romanUrduPatterns = this.initializeRomanUrduPatterns();

        // Setup Roman Urdu detection and processing
        this.setupRomanUrduProcessing();

        console.log('✅ Roman Urdu Support initialized');
    }

    initializeRomanUrduVocabulary() {
        return {
            // Food and Restaurant Items
            food: {
                'karahi': 'karahi',
                'karhahi': 'karahi',
                'karai': 'karahi',
                'biryani': 'biryani',
                'biriyani': 'biryani',
                'pulao': 'pulao',
                'pilaf': 'pulao',
                'khana': 'food',
                'khaana': 'food',
                'gosht': 'meat',
                'murgh': 'chicken',
                'murghi': 'chicken',
                'chawal': 'rice',
                'dal': 'dal',
                'daal': 'dal',
                'roti': 'roti',
                'rooti': 'roti',
                'naan': 'naan',
                'nan': 'naan',
                'chai': 'tea',
                'chay': 'tea',
                'pani': 'water',
                'paani': 'water',
                'doodh': 'milk',
                'dudh': 'milk',
                'cheeni': 'sugar',
                'chini': 'sugar',
                'namak': 'salt',
                'mirch': 'pepper',
                'pyaz': 'onion',
                'pyaaz': 'onion',
                'tamatar': 'tomato',
                'aloo': 'potato',
                'aaloo': 'potato'
            },

            // Business and People
            business: {
                'customer': 'customer',
                'custmer': 'customer',
                'gahak': 'customer',
                'gaahak': 'customer',
                'staff': 'staff',
                'staf': 'staff',
                'kamgar': 'worker',
                'mulazim': 'employee',
                'malik': 'owner',
                'manager': 'manager',
                'waiter': 'waiter',
                'cook': 'cook',
                'chef': 'chef',
                'cashier': 'cashier'
            },

            // Money and Transactions
            money: {
                'paisa': 'money',
                'paise': 'money',
                'rupee': 'rupee',
                'rupay': 'rupee',
                'rupaiya': 'rupee',
                'taka': 'money',
                'bill': 'bill',
                'hisab': 'account',
                'hesab': 'account',
                'udhar': 'credit',
                'advance': 'advance',
                'peshgi': 'advance',
                'tankhwah': 'salary',
                'salary': 'salary',
                'maash': 'salary',
                'ajrat': 'wage',
                'wage': 'wage',
                'kharcha': 'expense',
                'kharch': 'expense'
            },

            // Actions and Verbs
            actions: {
                'karo': 'do',
                'kro': 'do',
                'kar': 'do',
                'dena': 'give',
                'do': 'give',
                'de': 'give',
                'lena': 'take',
                'lo': 'take',
                'le': 'take',
                'banana': 'make',
                'banao': 'make',
                'bana': 'make',
                'dekho': 'see',
                'dekh': 'see',
                'check': 'check',
                'chek': 'check',
                'add': 'add',
                'shamil': 'add',
                'remove': 'remove',
                'hata': 'remove',
                'hatao': 'remove',
                'update': 'update',
                'change': 'change',
                'badal': 'change',
                'badlo': 'change',
                'order': 'order',
                'mangwana': 'order',
                'buy': 'buy',
                'kharid': 'buy',
                'kharido': 'buy',
                'sell': 'sell',
                'bech': 'sell',
                'becho': 'sell'
            },

            // Common Words
            common: {
                'aur': 'and',
                'ya': 'or',
                'ke': 'of',
                'ka': 'of',
                'ki': 'of',
                'se': 'from',
                'me': 'in',
                'mein': 'in',
                'par': 'on',
                'pe': 'on',
                'liye': 'for',
                'ke liye': 'for',
                'keliye': 'for',
                'wala': 'person',
                'wali': 'person',
                'hai': 'is',
                'he': 'is',
                'tha': 'was',
                'the': 'was',
                'hoga': 'will be',
                'hogi': 'will be',
                'kya': 'what',
                'kahan': 'where',
                'kab': 'when',
                'kyun': 'why',
                'kaise': 'how',
                'kitna': 'how much',
                'kitni': 'how much'
            }
        };
    }

    initializeRomanUrduNumbers() {
        return {
            // Basic numbers
            'ek': '1', 'aik': '1',
            'do': '2', 'dou': '2',
            'teen': '3', 'tin': '3',
            'char': '4', 'chaar': '4',
            'panch': '5', 'paanch': '5',
            'che': '6', 'chhe': '6',
            'saat': '7', 'sat': '7',
            'aath': '8', 'aat': '8',
            'nau': '9', 'no': '9',
            'das': '10', 'dus': '10',

            // Tens
            'bees': '20', 'bis': '20',
            'tees': '30', 'tis': '30',
            'chalis': '40', 'chaalis': '40',
            'pachas': '50', 'pachhas': '50',
            'saath': '60', 'sath': '60',
            'sattar': '70', 'satar': '70',
            'assi': '80', 'asi': '80',
            'nabbe': '90', 'nabe': '90',

            // Hundreds and thousands
            'sau': '100', 'so': '100',
            'hazaar': '1000', 'hazar': '1000',
            'lakh': '100000', 'lac': '100000',
            'crore': '10000000', 'karor': '10000000'
        };
    }

    initializeRomanUrduPatterns() {
        return {
            // Sales patterns in Roman Urdu
            sales: [
                {
                    pattern: /customer\s+(.+?)\s+ne\s+(.+?)\s+ka\s+khana\s+order\s+kiya/i,
                    action: 'addSaleTransaction',
                    params: ['customer', 'items']
                },
                {
                    pattern: /(.+?)\s+ne\s+(\d+)\s*(?:rupay|rupaiya|rs)?\s+ka\s+saman\s+(?:kharida|liya)/i,
                    action: 'addSaleTransaction',
                    params: ['customer', 'amount']
                },
                {
                    pattern: /table\s+(\d+)\s+(?:pe|par)\s+(.+?)\s+order,?\s*(\d+)\s+log/i,
                    action: 'processTableOrder',
                    params: ['tableNumber', 'items', 'customerCount']
                },
                {
                    pattern: /(\d+)\s*(?:rupay|rupaiya|rs)?\s+ki\s+sale\s+(?:add|shamil)\s+karo/i,
                    action: 'addQuickSale',
                    params: ['amount']
                }
            ],

            // Expense patterns in Roman Urdu
            expenses: [
                {
                    pattern: /(.+?)\s+(\d+)\s*(?:rupay|rupaiya|rs)?\s+mein\s+(?:kharida|liya)/i,
                    action: 'addExpense',
                    params: ['item', 'amount']
                },
                {
                    pattern: /staff\s+(.+?)\s+ko\s+(\d+)\s*(?:rupay|rupaiya|rs)?\s+(?:advance|peshgi)\s+(?:do|diya)/i,
                    action: 'addStaffAdvance',
                    params: ['staffName', 'amount']
                },
                {
                    pattern: /(.+?)\s+ke\s+liye\s+(\d+)\s*(?:rupay|rupaiya|rs)?\s+(?:kharch|spend)\s+kiya/i,
                    action: 'addExpense',
                    params: ['description', 'amount']
                }
            ],

            // Menu patterns in Roman Urdu
            menu: [
                {
                    pattern: /(.+?)\s+ki\s+price\s+(\d+)\s*(?:rupay|rupaiya|rs)?\s+(?:rakho|set\s+karo)/i,
                    action: 'updateMenuPrice',
                    params: ['itemName', 'price']
                },
                {
                    pattern: /menu\s+mein\s+(.+?)\s+(\d+)\s*(?:rupay|rupaiya|rs)?\s+mein\s+(?:add|shamil)\s+karo/i,
                    action: 'addMenuItem',
                    params: ['itemName', 'price']
                },
                {
                    pattern: /(.+?)\s+ko\s+menu\s+se\s+(?:remove|hata)\s+(?:do|karo)/i,
                    action: 'removeMenuItem',
                    params: ['itemName']
                }
            ],

            // Staff patterns in Roman Urdu
            staff: [
                {
                    pattern: /(.+?)\s+aaj\s+kaam\s+(?:pe|par)\s+aaya/i,
                    action: 'markStaffAttendance',
                    params: ['staffName']
                },
                {
                    pattern: /(.+?)\s+ko\s+(?:salary|tankhwah|ajrat)\s+(?:do|dena)/i,
                    action: 'payStaffDaily',
                    params: ['staffName']
                },
                {
                    pattern: /staff\s+member\s+(.+?)\s+ko\s+(\d+)\s*(?:rupay|rupaiya|rs)?\s+(?:salary|tankhwah)\s+(?:de|do)/i,
                    action: 'addStaffMember',
                    params: ['staffName', 'salary']
                }
            ],

            // Inventory patterns in Roman Urdu
            inventory: [
                {
                    pattern: /inventory\s+mein\s+(\d+)\s+(.+?)\s+(?:add|shamil)\s+karo/i,
                    action: 'addInventoryItem',
                    params: ['quantity', 'itemName']
                },
                {
                    pattern: /(.+?)\s+ka\s+stock\s+(?:check|dekho)/i,
                    action: 'checkInventoryStock',
                    params: ['itemName']
                },
                {
                    pattern: /(\d+)\s+(.+?)\s+inventory\s+se\s+(?:use|istemal)\s+karo/i,
                    action: 'useInventoryItem',
                    params: ['quantity', 'itemName']
                }
            ],

            // System patterns in Roman Urdu
            system: [
                {
                    pattern: /system\s+(?:check|dekho|chek)\s+karo/i,
                    action: 'checkSystemErrors',
                    params: []
                },
                {
                    pattern: /(?:sab|tamam)\s+(?:errors|kharabiya)\s+(?:fix|theek)\s+karo/i,
                    action: 'autoFixErrors',
                    params: []
                },
                {
                    pattern: /data\s+ka\s+backup\s+(?:banao|karo)/i,
                    action: 'backupSystemData',
                    params: []
                },
                {
                    pattern: /report\s+(?:banao|generate\s+karo)/i,
                    action: 'generateComprehensiveReport',
                    params: []
                }
            ]
        };
    }

    setupRomanUrduProcessing() {
        this.romanUrduProcessor = {
            detectRomanUrdu: (text) => {
                // Check for Roman Urdu patterns
                const romanUrduWords = Object.values(this.romanUrduVocabulary).flat();
                const romanUrduNumbers = Object.keys(this.romanUrduNumbers);
                const allRomanUrduTerms = [...romanUrduWords, ...romanUrduNumbers];

                const words = text.toLowerCase().split(/\s+/);
                const romanUrduCount = words.filter(word =>
                    allRomanUrduTerms.some(term => word.includes(term))
                ).length;

                return romanUrduCount / words.length > 0.3; // 30% threshold
            },

            normalizeRomanUrdu: (text) => {
                let normalized = text.toLowerCase();

                // Replace Roman Urdu vocabulary with English equivalents
                Object.entries(this.romanUrduVocabulary).forEach(([category, terms]) => {
                    Object.entries(terms).forEach(([romanUrdu, english]) => {
                        const regex = new RegExp(`\\b${romanUrdu}\\b`, 'gi');
                        normalized = normalized.replace(regex, english);
                    });
                });

                // Replace Roman Urdu numbers with digits
                Object.entries(this.romanUrduNumbers).forEach(([romanUrdu, digit]) => {
                    const regex = new RegExp(`\\b${romanUrdu}\\b`, 'gi');
                    normalized = normalized.replace(regex, digit);
                });

                return normalized;
            },

            translateToEnglish: (text) => {
                // First normalize Roman Urdu
                let translated = this.normalizeRomanUrdu(text);

                // Handle common Roman Urdu grammar patterns
                translated = translated.replace(/\bne\b/gi, ''); // Remove Urdu subject marker
                translated = translated.replace(/\bka\b|\bki\b|\bke\b/gi, 'of'); // Possessive markers
                translated = translated.replace(/\bmein\b|\bme\b/gi, 'in'); // Location markers
                translated = translated.replace(/\bse\b/gi, 'from'); // Source markers
                translated = translated.replace(/\bko\b/gi, 'to'); // Object markers
                translated = translated.replace(/\bpe\b|\bpar\b/gi, 'on'); // Location markers

                // Clean up extra spaces
                translated = translated.replace(/\s+/g, ' ').trim();

                return translated;
            }
        };
    }

    initializeUnlimitedCapabilities() {
        console.log('🚀 Initializing Unlimited System Capabilities...');

        // File system operations (with safe fallbacks)
        this.fileSystemOperations = {
            createFile: this.createFileUnlimited ? this.createFileUnlimited.bind(this) : () => ({ success: false, message: 'File creation not available' }),
            editFile: this.editFileUnlimited ? this.editFileUnlimited.bind(this) : () => ({ success: false, message: 'File editing not available' }),
            deleteFile: this.deleteFileUnlimited ? this.deleteFileUnlimited.bind(this) : () => ({ success: false, message: 'File deletion not available' }),
            readFile: this.readFileUnlimited ? this.readFileUnlimited.bind(this) : () => ({ success: false, message: 'File reading not available' }),
            createDirectory: this.createDirectoryUnlimited ? this.createDirectoryUnlimited.bind(this) : () => ({ success: false, message: 'Directory creation not available' }),
            listFiles: this.listFilesUnlimited ? this.listFilesUnlimited.bind(this) : () => ({ success: false, message: 'File listing not available' })
        };

        // Dynamic page creation (with safe fallbacks)
        this.pageCreator = {
            createPage: this.createPageDynamically ? this.createPageDynamically.bind(this) : () => ({ success: false, message: 'Page creation not available' }),
            addFeature: this.addFeatureDynamically ? this.addFeatureDynamically.bind(this) : () => ({ success: false, message: 'Feature addition not available' }),
            modifyInterface: this.modifyInterfaceDynamically ? this.modifyInterfaceDynamically.bind(this) : () => ({ success: false, message: 'Interface modification not available' }),
            generateForm: this.generateFormDynamically ? this.generateFormDynamically.bind(this) : () => ({ success: false, message: 'Form generation not available' })
        };

        // Database operations (with safe fallbacks)
        this.databaseOperations = {
            create: this.createDataUnlimited ? this.createDataUnlimited.bind(this) : () => ({ success: false, message: 'Data creation not available' }),
            read: this.readDataUnlimited ? this.readDataUnlimited.bind(this) : () => ({ success: false, message: 'Data reading not available' }),
            update: this.updateDataUnlimited ? this.updateDataUnlimited.bind(this) : () => ({ success: false, message: 'Data update not available' }),
            delete: this.deleteDataUnlimited ? this.deleteDataUnlimited.bind(this) : () => ({ success: false, message: 'Data deletion not available' }),
            query: this.queryDataUnlimited ? this.queryDataUnlimited.bind(this) : () => ({ success: false, message: 'Data query not available' }),
            backup: this.backupDataUnlimited ? this.backupDataUnlimited.bind(this) : () => ({ success: false, message: 'Data backup not available' }),
            restore: this.restoreDataUnlimited ? this.restoreDataUnlimited.bind(this) : () => ({ success: false, message: 'Data restore not available' })
        };

        // Self-improvement capabilities (with safe fallbacks)
        this.selfImprovement = {
            analyzePerformance: this.analyzeOwnPerformance ? this.analyzeOwnPerformance.bind(this) : () => ({ success: false, message: 'Performance analysis not available' }),
            optimizeAlgorithms: this.optimizeOwnAlgorithms ? this.optimizeOwnAlgorithms.bind(this) : () => ({ success: false, message: 'Algorithm optimization not available' }),
            learnFromInteractions: this.learnFromUserInteractions ? this.learnFromUserInteractions.bind(this) : () => ({ success: false, message: 'Learning not available' }),
            updateCapabilities: this.updateOwnCapabilities ? this.updateOwnCapabilities.bind(this) : () => ({ success: false, message: 'Capability updates not available' }),
            enhanceUnderstanding: this.enhanceOwnUnderstanding ? this.enhanceOwnUnderstanding.bind(this) : () => ({ success: false, message: 'Understanding enhancement not available' })
        };

        console.log('✅ Unlimited System Capabilities initialized');
    }

    initializeHumanLikeIntelligence() {
        console.log('🧠 Initializing Human-like Intelligence...');

        // Contextual understanding (with safe fallbacks)
        this.contextualProcessor = {
            analyzeContext: this.analyzeConversationContext ? this.analyzeConversationContext.bind(this) : () => ({ success: false, message: 'Context analysis not available' }),
            inferIntent: this.inferUserIntent ? this.inferUserIntent.bind(this) : () => ({ success: false, message: 'Intent inference not available' }),
            predictNeeds: this.predictUserNeeds ? this.predictUserNeeds.bind(this) : () => ({ success: false, message: 'Need prediction not available' }),
            fillGaps: this.fillInformationGaps ? this.fillInformationGaps.bind(this) : () => ({ success: false, message: 'Gap filling not available' }),
            makeAssumptions: this.makeIntelligentAssumptions ? this.makeIntelligentAssumptions.bind(this) : () => ({ success: false, message: 'Assumption making not available' })
        };

        // Emotional intelligence (with safe fallbacks)
        this.emotionalProcessor = {
            detectEmotion: this.detectUserEmotion ? this.detectUserEmotion.bind(this) : () => ({ success: false, message: 'Emotion detection not available' }),
            assessUrgency: this.assessTaskUrgency ? this.assessTaskUrgency.bind(this) : () => ({ success: false, message: 'Urgency assessment not available' }),
            adaptTone: this.adaptResponseTone ? this.adaptResponseTone.bind(this) : () => ({ success: false, message: 'Tone adaptation not available' }),
            showEmpathy: this.showEmpathyInResponse ? this.showEmpathyInResponse.bind(this) : () => ({ success: false, message: 'Empathy not available' }),
            manageFrustration: this.manageFrustration ? this.manageFrustration.bind(this) : () => ({ success: false, message: 'Frustration management not available' })
        };

        // Proactive capabilities (with safe fallbacks)
        this.proactiveProcessor = {
            suggestActions: this.suggestProactiveActions ? this.suggestProactiveActions.bind(this) : () => ({ success: false, message: 'Action suggestions not available' }),
            anticipateProblems: this.anticipateProblems ? this.anticipateProblems.bind(this) : () => ({ success: false, message: 'Problem anticipation not available' }),
            optimizeWorkflow: this.optimizeWorkflow ? this.optimizeWorkflow.bind(this) : () => ({ success: false, message: 'Workflow optimization not available' }),
            automateRoutine: this.automateRoutineTasks ? this.automateRoutineTasks.bind(this) : () => ({ success: false, message: 'Task automation not available' }),
            improveEfficiency: this.improveEfficiency ? this.improveEfficiency.bind(this) : () => ({ success: false, message: 'Efficiency improvement not available' })
        };

        // Common sense reasoning (with safe fallbacks)
        this.commonSenseProcessor = {
            applyBusinessLogic: this.applyRestaurantBusinessLogic ? this.applyRestaurantBusinessLogic.bind(this) : () => ({ success: false, message: 'Business logic not available' }),
            validateDecisions: this.validateDecisionsWithCommonSense ? this.validateDecisionsWithCommonSense.bind(this) : () => ({ success: false, message: 'Decision validation not available' }),
            resolveConflicts: this.resolveLogicalConflicts ? this.resolveLogicalConflicts.bind(this) : () => ({ success: false, message: 'Conflict resolution not available' }),
            prioritizeTasks: this.prioritizeTasksIntelligently ? this.prioritizeTasksIntelligently.bind(this) : () => ({ success: false, message: 'Task prioritization not available' }),
            makeRecommendations: this.makeIntelligentRecommendations ? this.makeIntelligentRecommendations.bind(this) : () => ({ success: false, message: 'Recommendations not available' })
        };

        console.log('✅ Human-like Intelligence initialized');
    }

    // Manual test function for debugging API issues
    async testGeminiManual(apiKey) {
        console.log('🧪 Manual Gemini API Test Starting...');
        console.log('API Key (first 20 chars):', apiKey.substring(0, 20) + '...');

        try {
            const requestBody = {
                contents: [{
                    parts: [{
                        text: "Say hello"
                    }]
                }],
                generationConfig: {
                    maxOutputTokens: 50,
                    temperature: 0.1
                }
            };

            console.log('Request URL:', `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey.substring(0, 10)}...`);
            console.log('Request Body:', JSON.stringify(requestBody, null, 2));

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('Response Status:', response.status);
            console.log('Response Status Text:', response.statusText);
            console.log('Response Headers:', Object.fromEntries(response.headers.entries()));

            const responseText = await response.text();
            console.log('Raw Response Text:', responseText);

            if (!response.ok) {
                console.error('❌ API Request Failed');
                try {
                    const errorData = JSON.parse(responseText);
                    console.error('Error Details:', errorData);

                    if (errorData.error) {
                        console.error('Error Code:', errorData.error.code);
                        console.error('Error Message:', errorData.error.message);
                        console.error('Error Status:', errorData.error.status);
                    }
                } catch (e) {
                    console.error('Could not parse error response as JSON');
                }
                return false;
            }

            try {
                const data = JSON.parse(responseText);
                console.log('✅ Parsed Response:', data);

                if (data.candidates && data.candidates.length > 0) {
                    console.log('✅ Success! Generated text:', data.candidates[0].content.parts[0].text);
                    return true;
                } else {
                    console.error('❌ No candidates in response');
                    return false;
                }
            } catch (e) {
                console.error('❌ Could not parse successful response as JSON');
                return false;
            }

        } catch (error) {
            console.error('❌ Network or other error:', error);
            return false;
        }
    }

    initializeLocalTerminology() {
        return {
            // Pakistani Restaurant Terms
            food: {
                'کراہی': 'karahi',
                'بریانی': 'biryani',
                'چکن': 'chicken',
                'گوشت': 'gosht',
                'مرغ': 'murgh',
                'چاول': 'chawal',
                'دال': 'dal',
                'روٹی': 'roti',
                'نان': 'naan',
                'چائے': 'chai',
                'کافی': 'coffee',
                'پانی': 'pani',
                'دودھ': 'doodh',
                'چینی': 'cheeni',
                'نمک': 'namak',
                'مرچ': 'mirch',
                'پیاز': 'pyaz',
                'ٹماٹر': 'tamatar',
                'آلو': 'aloo'
            },

            // Business Terms
            business: {
                'گاہک': 'customer',
                'صارف': 'customer',
                'میز': 'table',
                'ٹیبل': 'table',
                'عملہ': 'staff',
                'ملازم': 'employee',
                'تنخواہ': 'salary',
                'اجرت': 'wage',
                'پیشگی': 'advance',
                'خرچ': 'expense',
                'خریداری': 'purchase',
                'فروخت': 'sale',
                'آمدنی': 'income',
                'منافع': 'profit',
                'نقصان': 'loss',
                'حساب': 'account',
                'بل': 'bill',
                'رسید': 'receipt'
            },

            // Numbers and Currency
            numbers: {
                'ایک': '1', 'دو': '2', 'تین': '3', 'چار': '4', 'پانچ': '5',
                'چھ': '6', 'سات': '7', 'آٹھ': '8', 'نو': '9', 'دس': '10',
                'بیس': '20', 'تیس': '30', 'چالیس': '40', 'پچاس': '50',
                'ساٹھ': '60', 'ستر': '70', 'اسی': '80', 'نوے': '90', 'سو': '100',
                'ہزار': '1000', 'لاکھ': '100000'
            },

            currency: {
                'روپے': 'rupees',
                'روپیہ': 'rupee',
                'پیسے': 'paisa',
                'ٹکا': 'taka'
            },

            // Actions
            actions: {
                'شامل': 'add',
                'ہٹا': 'remove',
                'تبدیل': 'change',
                'اپڈیٹ': 'update',
                'چیک': 'check',
                'دیکھ': 'see',
                'بنا': 'make',
                'خرید': 'buy',
                'بیچ': 'sell',
                'دے': 'give',
                'لے': 'take',
                'کام': 'work',
                'آیا': 'came',
                'گیا': 'went'
            }
        };
    }

    initializeUrduPatterns() {
        return {
            // Sales patterns in Urdu
            sales: [
                {
                    pattern: /گاہک\s+(.+?)\s+نے\s+(.+?)\s+کے\s+لیے\s+(\d+)\s*روپے?\s+دیے/i,
                    action: 'addSaleTransaction',
                    params: ['customer', 'items', 'amount']
                },
                {
                    pattern: /میز\s+(\d+)\s*:\s*(.+?),\s*(\d+)\s+لوگ,\s*بل\s+مکمل/i,
                    action: 'processTableOrder',
                    params: ['tableNumber', 'items', 'customerCount']
                },
                {
                    pattern: /(\d+)\s*روپے?\s+کی\s+فروخت\s+شامل\s+کرو/i,
                    action: 'addQuickSale',
                    params: ['amount']
                }
            ],

            // Expense patterns in Urdu
            expenses: [
                {
                    pattern: /(.+?)\s+(\d+)\s*روپے?\s+میں\s+خریدا/i,
                    action: 'addExpense',
                    params: ['item', 'amount']
                },
                {
                    pattern: /عملہ\s+(.+?)\s+نے\s+(\d+)\s*روپے?\s+پیشگی\s+لی/i,
                    action: 'addStaffAdvance',
                    params: ['staffName', 'amount']
                },
                {
                    pattern: /(.+?)\s+کے\s+لیے\s+(\d+)\s*روپے?\s+ادا\s+کیے/i,
                    action: 'addExpense',
                    params: ['description', 'amount']
                }
            ],

            // Menu patterns in Urdu
            menu: [
                {
                    pattern: /(.+?)\s+کی\s+قیمت\s+(\d+)\s*روپے?\s+رکھو/i,
                    action: 'updateMenuPrice',
                    params: ['itemName', 'price']
                },
                {
                    pattern: /مینو\s+میں\s+(.+?)\s+(\d+)\s*روپے?\s+میں\s+شامل\s+کرو/i,
                    action: 'addMenuItem',
                    params: ['itemName', 'price']
                }
            ],

            // Staff patterns in Urdu
            staff: [
                {
                    pattern: /(.+?)\s+آج\s+کام\s+پر\s+آیا/i,
                    action: 'markStaffAttendance',
                    params: ['staffName']
                },
                {
                    pattern: /(.+?)\s+کو\s+روزانہ\s+اجرت\s+دو/i,
                    action: 'payStaffDaily',
                    params: ['staffName']
                }
            ],

            // System patterns in Urdu
            system: [
                {
                    pattern: /سسٹم\s+کی\s+خرابیاں\s+چیک\s+کرو/i,
                    action: 'checkSystemErrors',
                    params: []
                },
                {
                    pattern: /تمام\s+خرابیاں\s+ٹھیک\s+کرو/i,
                    action: 'autoFixErrors',
                    params: []
                },
                {
                    pattern: /ڈیٹا\s+کا\s+بیک\s+اپ\s+بناؤ/i,
                    action: 'backupSystemData',
                    params: []
                },
                {
                    pattern: /رپورٹ\s+بناؤ/i,
                    action: 'generateComprehensiveReport',
                    params: []
                }
            ]
        };
    }

    initializeMixedLanguagePatterns() {
        return {
            // Mixed Urdu-English patterns
            sales: [
                {
                    pattern: /customer\s+(.+?)\s+نے\s+(\d+)\s*rupees?\s+کا\s+سامان\s+خریدا/i,
                    action: 'addSaleTransaction',
                    params: ['customer', 'amount']
                },
                {
                    pattern: /table\s+(\d+)\s+پر\s+(.+?)\s+order,\s*(\d+)\s+people/i,
                    action: 'processTableOrder',
                    params: ['tableNumber', 'items', 'customerCount']
                }
            ],

            expenses: [
                {
                    pattern: /(.+?)\s+buy\s+کیا\s+(\d+)\s*rupees?\s+میں/i,
                    action: 'addExpense',
                    params: ['item', 'amount']
                },
                {
                    pattern: /staff\s+(.+?)\s+کو\s+(\d+)\s*rupees?\s+advance\s+دیا/i,
                    action: 'addStaffAdvance',
                    params: ['staffName', 'amount']
                }
            ],

            menu: [
                {
                    pattern: /(.+?)\s+کی\s+price\s+(\d+)\s*rupees?\s+set\s+کرو/i,
                    action: 'updateMenuPrice',
                    params: ['itemName', 'price']
                }
            ]
        };
    }

    setupLanguageDetection() {
        // Simple language detection based on script and common words
        this.languageDetection = {
            detectLanguage: (text) => {
                // Check for Urdu script
                const urduRegex = /[\u0600-\u06FF]/;
                const englishRegex = /[a-zA-Z]/;

                const hasUrdu = urduRegex.test(text);
                const hasEnglish = englishRegex.test(text);

                if (hasUrdu && hasEnglish) return 'mixed';
                if (hasUrdu) return 'ur';
                if (hasEnglish) return 'en';
                return 'en'; // default
            },

            translateUrduToEnglish: (text) => {
                let translated = text;

                // Replace Urdu terms with English equivalents
                Object.entries(this.localTerminology.food).forEach(([urdu, english]) => {
                    const regex = new RegExp(urdu, 'g');
                    translated = translated.replace(regex, english);
                });

                Object.entries(this.localTerminology.business).forEach(([urdu, english]) => {
                    const regex = new RegExp(urdu, 'g');
                    translated = translated.replace(regex, english);
                });

                Object.entries(this.localTerminology.actions).forEach(([urdu, english]) => {
                    const regex = new RegExp(urdu, 'g');
                    translated = translated.replace(regex, english);
                });

                Object.entries(this.localTerminology.numbers).forEach(([urdu, english]) => {
                    const regex = new RegExp(urdu, 'g');
                    translated = translated.replace(regex, english);
                });

                return translated;
            }
        };
    }

    // Automation creation functions
    createSalesAutomation() {
        return {
            process: (data) => {
                try {
                    return { success: true, message: 'Sales automation processed', data: data };
                } catch (error) {
                    return { success: false, message: 'Sales automation failed', error: error.message };
                }
            }
        };
    }

    createExpenseAutomation() {
        return {
            process: (data) => {
                try {
                    return { success: true, message: 'Expense automation processed', data: data };
                } catch (error) {
                    return { success: false, message: 'Expense automation failed', error: error.message };
                }
            }
        };
    }

    createMenuAutomation() {
        return {
            process: (data) => {
                try {
                    return { success: true, message: 'Menu automation processed', data: data };
                } catch (error) {
                    return { success: false, message: 'Menu automation failed', error: error.message };
                }
            }
        };
    }

    createInventoryAutomation() {
        return {
            process: (data) => {
                try {
                    return { success: true, message: 'Inventory automation processed', data: data };
                } catch (error) {
                    return { success: false, message: 'Inventory automation failed', error: error.message };
                }
            }
        };
    }

    createStaffAutomation() {
        return {
            process: (data) => {
                try {
                    return { success: true, message: 'Staff automation processed', data: data };
                } catch (error) {
                    return { success: false, message: 'Staff automation failed', error: error.message };
                }
            }
        };
    }

    createSystemAutomation() {
        return {
            process: (data) => {
                try {
                    return { success: true, message: 'System automation processed', data: data };
                } catch (error) {
                    return { success: false, message: 'System automation failed', error: error.message };
                }
            }
        };
    }

    // Human-like Intelligence Functions
    analyzeConversationContext(query) {
        try {
            return {
                currentTopic: this.identifyTopic ? this.identifyTopic(query) : 'general',
                previousQueries: this.contextHistory ? this.contextHistory.slice(-5) : [],
                userPattern: 'mixed_usage',
                businessContext: { isBusinessHours: true, isPeakTime: false },
                timeContext: { hour: new Date().getHours() },
                lastCustomer: null,
                lastTable: null,
                recentItems: []
            };
        } catch (error) {
            console.log('Context analysis error:', error);
            return { currentTopic: 'general' };
        }
    }

    detectUserEmotion(query) {
        try {
            const emotionIndicators = {
                'frustrated': ['urgent', 'quickly', 'asap', 'problem', 'issue', 'wrong', 'error'],
                'satisfied': ['good', 'great', 'excellent', 'perfect', 'thanks', 'thank you'],
                'confused': ['how', 'what', 'why', 'confused', 'understand', 'help'],
                'urgent': ['urgent', 'immediately', 'now', 'quick', 'fast', 'hurry'],
                'casual': ['please', 'can you', 'could you', 'maybe', 'perhaps']
            };

            const queryLower = query.toLowerCase();
            for (const [emotion, indicators] of Object.entries(emotionIndicators)) {
                if (indicators.some(indicator => queryLower.includes(indicator))) {
                    return emotion;
                }
            }
            return 'neutral';
        } catch (error) {
            console.log('Emotion detection error:', error);
            return 'neutral';
        }
    }

    assessTaskUrgency(query) {
        try {
            const urgencyKeywords = {
                'high': ['urgent', 'immediately', 'asap', 'emergency', 'critical', 'now'],
                'medium': ['soon', 'quickly', 'fast', 'priority', 'important'],
                'low': ['when possible', 'later', 'eventually', 'sometime']
            };

            const queryLower = query.toLowerCase();
            for (const [level, keywords] of Object.entries(urgencyKeywords)) {
                if (keywords.some(keyword => queryLower.includes(keyword))) {
                    return level;
                }
            }
            return 'medium';
        } catch (error) {
            console.log('Urgency assessment error:', error);
            return 'medium';
        }
    }

    async testInternetAIServices() {
        console.log('🧪 Testing Internet AI Services...');

        for (const [service, config] of Object.entries(this.internetAIServices)) {
            if (config.enabled && config.apiKey) {
                try {
                    const isAvailable = await this.testAIService(service, config);
                    console.log(`${service}: ${isAvailable ? '✅ Available' : '❌ Unavailable'}`);
                } catch (error) {
                    console.log(`${service}: ❌ Error - ${error.message}`);
                }
            } else {
                console.log(`${service}: ⚠️ No API key configured`);
            }
        }
    }

    async testAIService(service, config) {
        const testPrompt = "Hello, this is a test message.";

        try {
            switch (service) {
                case 'openai':
                    return await this.testOpenAI(config, testPrompt);
                case 'claude':
                    return await this.testClaude(config, testPrompt);
                case 'gemini':
                    return await this.testGemini(config, testPrompt);
                case 'cohere':
                    return await this.testCohere(config, testPrompt);
                case 'openrouter':
                    return await this.testOpenRouter(config, testPrompt);
                default:
                    return false;
            }
        } catch (error) {
            console.error(`Error testing ${service}:`, error);
            return false;
        }
    }

    async testOpenAI(config, prompt) {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: config.model,
                messages: [{ role: 'user', content: prompt }],
                max_tokens: 10
            })
        });

        return response.ok;
    }

    async testClaude(config, prompt) {
        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'x-api-key': config.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: config.model,
                max_tokens: 10,
                messages: [{ role: 'user', content: prompt }]
            })
        });

        return response.ok;
    }

    async testGemini(config, prompt) {
        try {
            console.log('Testing Gemini API with key:', config.apiKey.substring(0, 20) + '...');

            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt || "Hello"
                    }]
                }],
                generationConfig: {
                    maxOutputTokens: 10,
                    temperature: 0.1
                }
            };

            console.log('Gemini request body:', JSON.stringify(requestBody, null, 2));

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${config.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('Gemini response status:', response.status);
            console.log('Gemini response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Gemini API Error Details:', {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText
                });

                // Try to parse error for more details
                try {
                    const errorJson = JSON.parse(errorText);
                    console.error('Gemini API Error JSON:', errorJson);
                } catch (e) {
                    console.error('Could not parse error as JSON');
                }

                return false;
            }

            const data = await response.json();
            console.log('Gemini API Response:', data);

            if (data.candidates && data.candidates.length > 0) {
                console.log('✅ Gemini API test successful');
                return true;
            } else {
                console.error('❌ Gemini API returned no candidates');
                return false;
            }
        } catch (error) {
            console.error('Gemini test error:', error);
            return false;
        }
    }

    async testCohere(config, prompt) {
        const response = await fetch('https://api.cohere.ai/v1/generate', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: config.model,
                prompt: prompt,
                max_tokens: 10
            })
        });

        return response.ok;
    }

    async testOpenRouter(config, prompt) {
        try {
            console.log('Testing OpenRouter API with key:', config.apiKey.substring(0, 20) + '...');
            console.log('Using model:', config.model);

            const requestBody = {
                model: config.model,
                messages: [
                    {
                        role: "user",
                        content: prompt || "Hello"
                    }
                ],
                max_tokens: 10,
                temperature: 0.1
            };

            console.log('OpenRouter request body:', JSON.stringify(requestBody, null, 2));

            const response = await fetch(config.endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'Zaiqa Restaurant AI Assistant'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('OpenRouter response status:', response.status);
            console.log('OpenRouter response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.error('OpenRouter API Error Details:', {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText
                });

                try {
                    const errorJson = JSON.parse(errorText);
                    console.error('OpenRouter API Error JSON:', errorJson);
                } catch (e) {
                    console.error('Could not parse OpenRouter error as JSON');
                }

                return false;
            }

            const data = await response.json();
            console.log('OpenRouter API Response:', data);

            if (data.choices && data.choices.length > 0) {
                console.log('✅ OpenRouter API test successful');
                return true;
            } else {
                console.error('❌ OpenRouter API returned no choices');
                return false;
            }
        } catch (error) {
            console.error('OpenRouter test error:', error);
            return false;
        }
    }

    async queryInternetAI(prompt, preferredLanguage = 'en') {
        console.log('🌐 Querying Internet AI Services...');

        // Try preferred service first
        if (this.preferredAIService !== 'local') {
            const result = await this.querySpecificAIService(this.preferredAIService, prompt, preferredLanguage);
            if (result) return result;
        }

        // Try fallback services
        for (const service of this.fallbackAIServices) {
            const result = await this.querySpecificAIService(service, prompt, preferredLanguage);
            if (result) return result;
        }

        // Fallback to local AI
        return await this.queryLocalAI(prompt);
    }

    async querySpecificAIService(service, prompt, language) {
        const config = this.internetAIServices[service];
        if (!config.enabled || !config.apiKey) return null;

        try {
            const enhancedPrompt = this.enhancePromptForService(prompt, language, service);

            switch (service) {
                case 'openai':
                    return await this.queryOpenAI(config, enhancedPrompt);
                case 'claude':
                    return await this.queryClaude(config, enhancedPrompt);
                case 'gemini':
                    return await this.queryGemini(config, enhancedPrompt);
                case 'cohere':
                    return await this.queryCohere(config, enhancedPrompt);
                case 'openrouter':
                    return await this.queryOpenRouter(config, enhancedPrompt);
                default:
                    return null;
            }
        } catch (error) {
            console.error(`Error querying ${service}:`, error);
            return null;
        }
    }

    enhancePromptForService(prompt, language, service) {
        const contextPrompt = `
You are Zaiqa AI Assistant for a Pakistani restaurant management system.
You understand both English and Urdu languages and Pakistani restaurant culture.

Current context:
- Restaurant: Pakistani/Desi cuisine
- Currency: Pakistani Rupees (PKR)
- Languages: English, Urdu, Mixed
- User's preferred language: ${language}

User query: "${prompt}"

Please provide a helpful response in ${language === 'ur' ? 'Urdu' : language === 'mixed' ? 'mixed Urdu-English' : 'English'}.
Focus on restaurant operations, be practical and culturally appropriate.
        `;

        return contextPrompt;
    }

    async queryOpenAI(config, prompt) {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: config.model,
                messages: [{ role: 'user', content: prompt }],
                max_tokens: 500,
                temperature: 0.7
            })
        });

        if (!response.ok) throw new Error(`OpenAI API error: ${response.status}`);

        const data = await response.json();
        return data.choices[0].message.content;
    }

    async queryClaude(config, prompt) {
        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'x-api-key': config.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: config.model,
                max_tokens: 500,
                messages: [{ role: 'user', content: prompt }]
            })
        });

        if (!response.ok) throw new Error(`Claude API error: ${response.status}`);

        const data = await response.json();
        return data.content[0].text;
    }

    async queryGemini(config, prompt) {
        try {
            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${config.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        maxOutputTokens: 500,
                        temperature: 0.7
                    }
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
            }

            const data = await response.json();

            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('No response candidates from Gemini API');
            }

            return data.candidates[0].content.parts[0].text;
        } catch (error) {
            console.error('Gemini query error:', error);
            throw error;
        }
    }

    async queryCohere(config, prompt) {
        const response = await fetch('https://api.cohere.ai/v1/generate', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: config.model,
                prompt: prompt,
                max_tokens: 500,
                temperature: 0.7
            })
        });

        if (!response.ok) throw new Error(`Cohere API error: ${response.status}`);

        const data = await response.json();
        return data.generations[0].text;
    }

    async queryOpenRouter(config, prompt) {
        try {
            console.log('Querying OpenRouter with model:', config.model);

            const response = await fetch(config.endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'Zaiqa Restaurant AI Assistant'
                },
                body: JSON.stringify({
                    model: config.model,
                    messages: [
                        {
                            role: "user",
                            content: prompt
                        }
                    ],
                    max_tokens: 500,
                    temperature: 0.7
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
            }

            const data = await response.json();

            if (!data.choices || data.choices.length === 0) {
                throw new Error('No response choices from OpenRouter API');
            }

            return data.choices[0].message.content;
        } catch (error) {
            console.error('OpenRouter query error:', error);
            throw error;
        }
    }

    async checkAndMaintainConnection() {
        if (!this.persistentConnectivity) return;

        try {
            const wasOnline = this.isOnline;
            await this.detectLocalAI();

            if (!wasOnline && this.isOnline) {
                console.log('🔗 AI connection restored');
                this.connectionRetryAttempts = 0;
                this.showConnectionNotification('AI connection restored', 'success');
            } else if (wasOnline && !this.isOnline) {
                console.log('⚠️ AI connection lost, attempting reconnection...');
                this.attemptReconnection();
            }

            this.lastConnectionCheck = new Date().toISOString();

        } catch (error) {
            console.error('Connection check failed:', error);
        }
    }

    async attemptReconnection() {
        if (this.connectionRetryAttempts >= this.maxRetryAttempts) {
            console.log('❌ Max reconnection attempts reached');
            this.showConnectionNotification('AI connection failed after multiple attempts', 'error');
            return;
        }

        this.connectionRetryAttempts++;
        console.log(`🔄 Reconnection attempt ${this.connectionRetryAttempts}/${this.maxRetryAttempts}`);

        // Wait before retry (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, this.connectionRetryAttempts), 30000);
        setTimeout(() => {
            this.detectLocalAI();
        }, delay);
    }

    async checkInternetConnectivity() {
        try {
            const response = await fetch('https://httpbin.org/get', {
                method: 'GET',
                mode: 'cors',
                cache: 'no-cache',
                signal: AbortSignal.timeout(5000)
            });
            return response.ok;
        } catch (error) {
            console.log('No internet connectivity detected');
            return false;
        }
    }

    async initializeExternalDataSources() {
        console.log('🌐 Initializing external data sources...');

        // Initialize weather data
        try {
            this.weatherData = await this.fetchWeatherData();
            console.log('✅ Weather data initialized');
        } catch (error) {
            console.log('⚠️ Weather data unavailable:', error.message);
        }

        // Initialize market trends (mock data for now)
        try {
            this.marketTrends = await this.fetchMarketTrends();
            console.log('✅ Market trends initialized');
        } catch (error) {
            console.log('⚠️ Market trends unavailable:', error.message);
        }
    }

    async fetchWeatherData() {
        // Mock weather data - in production, use actual weather API
        return {
            temperature: 25 + Math.random() * 10,
            condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)],
            humidity: 50 + Math.random() * 30,
            lastUpdated: new Date().toISOString()
        };
    }

    async fetchMarketTrends() {
        // Mock market trends - in production, use actual market data API
        return {
            foodPrices: {
                chicken: { trend: 'stable', change: 0.02 },
                beef: { trend: 'rising', change: 0.05 },
                vegetables: { trend: 'falling', change: -0.03 }
            },
            restaurantTrends: {
                dineIn: { trend: 'rising', change: 0.08 },
                takeaway: { trend: 'stable', change: 0.01 }
            },
            lastUpdated: new Date().toISOString()
        };
    }

    startPeriodicDataUpdates() {
        // Update external data every 30 minutes
        setInterval(async () => {
            if (await this.checkInternetConnectivity()) {
                await this.initializeExternalDataSources();
            }
        }, 30 * 60 * 1000);
    }

    showConnectionNotification(message, type) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, type);
        }

        // Also update UI indicators
        this.updateConnectionStatus();
    }

    updateConnectionStatus() {
        // Update status indicators in the UI
        const statusDots = document.querySelectorAll('.ai-status-dot');
        statusDots.forEach(dot => {
            dot.className = `ai-status-dot ${this.isOnline ? 'online' : 'offline'}`;
        });

        const statusIndicators = document.querySelectorAll('.ai-status-indicators .status-indicator');
        if (statusIndicators.length > 0) {
            statusIndicators[0].className = `status-indicator ${this.isOnline ? 'online' : 'offline'}`;
        }
    }

    startSystemMonitoring() {
        console.log('👁️ Starting System Monitoring...');

        // Monitor for data inconsistencies every 30 seconds
        this.monitoringIntervals.dataIntegrity = setInterval(() => {
            this.performDataIntegrityCheck();
        }, 30000);

        // Monitor for missing transactions every minute
        this.monitoringIntervals.transactionCheck = setInterval(() => {
            this.checkMissingTransactions();
        }, 60000);

        // Monitor staff payments every 5 minutes
        this.monitoringIntervals.staffPayments = setInterval(() => {
            this.checkStaffPaymentDue();
        }, 5 * 60000);

        // Monitor system errors every 2 minutes
        this.monitoringIntervals.errorCheck = setInterval(() => {
            this.detectAndLogErrors();
        }, 2 * 60000);

        console.log('✅ System Monitoring started');
    }

    safeParseJSON(jsonString, defaultValue = []) {
        try {
            return jsonString ? JSON.parse(jsonString) : defaultValue;
        } catch (error) {
            console.warn('Failed to parse JSON, using default value:', error);
            return defaultValue;
        }
    }

    async generateInsights(data) {
        const insights = [];

        // Revenue insights
        const revenueInsight = this.analyzeRevenueTrends(data);
        if (revenueInsight) insights.push(revenueInsight);

        // Inventory insights
        const inventoryInsights = this.analyzeInventoryStatus(data);
        insights.push(...inventoryInsights);

        // Menu performance insights
        const menuInsights = this.analyzeMenuPerformance(data);
        insights.push(...menuInsights);

        // Staff efficiency insights
        const staffInsights = this.analyzeStaffEfficiency(data);
        insights.push(...staffInsights);

        // Customer behavior insights
        const customerInsights = this.analyzeCustomerBehavior(data);
        insights.push(...customerInsights);

        // If AI is available, enhance with AI-generated insights
        if (this.isOnline) {
            const aiInsights = await this.getAIInsights(data);
            insights.push(...aiInsights);
        }

        return insights;
    }

    analyzeRevenueTrends(data) {
        const today = new Date().toDateString();
        const todayOrders = data.orders.filter(order => 
            new Date(order.created_at).toDateString() === today
        );
        
        const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const avgDailyRevenue = this.getAverageDailyRevenue();
        
        if (todayRevenue > avgDailyRevenue * 1.2) {
            return {
                type: 'revenue',
                level: 'positive',
                title: 'Excellent Revenue Performance',
                message: `Today's revenue (PKR ${todayRevenue.toLocaleString()}) is ${Math.round((todayRevenue / avgDailyRevenue - 1) * 100)}% above average!`,
                action: 'Consider extending operating hours or promoting popular items.',
                priority: 'high'
            };
        } else if (todayRevenue < avgDailyRevenue * 0.8) {
            return {
                type: 'revenue',
                level: 'warning',
                title: 'Revenue Below Average',
                message: `Today's revenue is ${Math.round((1 - todayRevenue / avgDailyRevenue) * 100)}% below average.`,
                action: 'Review menu pricing or consider promotional offers.',
                priority: 'medium'
            };
        }
        
        return null;
    }

    analyzeInventoryStatus(data) {
        const insights = [];
        const lowStockItems = [];
        const overstockedItems = [];
        
        data.inventory.forEach(item => {
            const usage = this.predictItemUsage(item);
            const daysRemaining = item.currentQuantity / (usage.dailyAverage || 1);
            
            if (daysRemaining < 2) {
                lowStockItems.push({
                    name: item.name,
                    daysRemaining: Math.round(daysRemaining * 10) / 10,
                    suggested: Math.ceil(usage.dailyAverage * 7) // Week's supply
                });
            } else if (daysRemaining > 14 && item.perishable) {
                overstockedItems.push({
                    name: item.name,
                    daysRemaining: Math.round(daysRemaining),
                    risk: 'spoilage'
                });
            }
        });
        
        if (lowStockItems.length > 0) {
            insights.push({
                type: 'inventory',
                level: 'warning',
                title: `${lowStockItems.length} Items Running Low`,
                message: `Critical items: ${lowStockItems.slice(0, 3).map(i => i.name).join(', ')}`,
                action: 'Place orders immediately to avoid stockouts.',
                data: lowStockItems,
                priority: 'high'
            });
        }
        
        if (overstockedItems.length > 0) {
            insights.push({
                type: 'inventory',
                level: 'info',
                title: `${overstockedItems.length} Items Overstocked`,
                message: `Consider promotional pricing for: ${overstockedItems.slice(0, 2).map(i => i.name).join(', ')}`,
                action: 'Create special offers to move excess inventory.',
                data: overstockedItems,
                priority: 'low'
            });
        }
        
        return insights;
    }

    analyzeMenuPerformance(data) {
        const insights = [];
        const menuAnalysis = this.getMenuItemAnalysis(data);
        
        // Find top performers
        const topItems = menuAnalysis
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 3);
            
        // Find underperformers
        const underperformers = menuAnalysis
            .filter(item => item.orderCount > 0)
            .sort((a, b) => a.profitMargin - b.profitMargin)
            .slice(0, 2);
        
        if (topItems.length > 0) {
            insights.push({
                type: 'menu',
                level: 'positive',
                title: 'Top Performing Items',
                message: `${topItems[0].name} leads with PKR ${topItems[0].revenue.toLocaleString()} revenue`,
                action: 'Consider featuring these items prominently or creating variations.',
                data: topItems,
                priority: 'medium'
            });
        }
        
        if (underperformers.length > 0) {
            insights.push({
                type: 'menu',
                level: 'warning',
                title: 'Low Profit Margin Items',
                message: `${underperformers[0].name} has only ${underperformers[0].profitMargin}% margin`,
                action: 'Review pricing or ingredient costs for these items.',
                data: underperformers,
                priority: 'medium'
            });
        }
        
        return insights;
    }

    analyzeStaffEfficiency(data) {
        const insights = [];
        const currentHour = new Date().getHours();
        const activeStaff = data.staff.filter(member => member.isActive);
        const currentOrders = data.orders.filter(order => 
            ['pending', 'confirmed', 'preparing'].includes(order.status)
        );
        
        const ordersPerStaff = currentOrders.length / Math.max(activeStaff.length, 1);
        
        if (ordersPerStaff > 3 && currentHour >= 12 && currentHour <= 14) {
            insights.push({
                type: 'staffing',
                level: 'warning',
                title: 'High Order Load During Lunch',
                message: `${ordersPerStaff.toFixed(1)} orders per staff member`,
                action: 'Consider calling in additional staff for lunch rush.',
                priority: 'high'
            });
        }
        
        return insights;
    }

    analyzeCustomerBehavior(data) {
        const insights = [];
        const recentOrders = data.orders.filter(order => {
            const orderTime = new Date(order.created_at);
            const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
            return orderTime > hourAgo;
        });
        
        const dineInOrders = recentOrders.filter(order => order.service_type === 'dine_in');
        const takeawayOrders = recentOrders.filter(order => order.service_type === 'takeaway');
        
        if (takeawayOrders.length > dineInOrders.length * 2) {
            insights.push({
                type: 'customer',
                level: 'info',
                title: 'High Takeaway Demand',
                message: `${Math.round(takeawayOrders.length / recentOrders.length * 100)}% of recent orders are takeaway`,
                action: 'Optimize takeaway packaging and pickup process.',
                priority: 'low'
            });
        }
        
        return insights;
    }

    async getAIInsights(data) {
        if (!this.isOnline) return [];
        
        try {
            const prompt = this.buildAnalysisPrompt(data);
            const response = await this.queryLocalAI(prompt);
            return this.parseAIResponse(response);
        } catch (error) {
            console.error('AI insights error:', error);
            return [];
        }
    }

    buildAnalysisPrompt(data) {
        const summary = {
            totalOrders: data.orders.length,
            todayRevenue: data.orders
                .filter(o => new Date(o.created_at).toDateString() === new Date().toDateString())
                .reduce((sum, o) => sum + (o.total_amount || 0), 0),
            lowStockItems: data.inventory.filter(i => i.currentQuantity < (i.minimumQuantity || 10)).length,
            activeStaff: data.staff.filter(s => s.isActive).length,
            currentHour: new Date().getHours(),
            dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][new Date().getDay()]
        };
        
        return `You are an AI assistant for Zaiqa Al-Hayat Restaurant. Analyze this data and provide 1-2 actionable insights:

Restaurant Data Summary:
- Current time: ${summary.dayOfWeek}, ${summary.currentHour}:00
- Today's revenue: PKR ${summary.todayRevenue}
- Total orders: ${summary.totalOrders}
- Low stock items: ${summary.lowStockItems}
- Active staff: ${summary.activeStaff}

Provide insights in this JSON format:
{
  "insights": [
    {
      "type": "category",
      "level": "positive|warning|info",
      "title": "Brief Title",
      "message": "Detailed message",
      "action": "Recommended action",
      "priority": "high|medium|low"
    }
  ]
}`;
    }

    async queryLocalAI(prompt) {
        const response = await fetch(`${this.localModelEndpoint}/api/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: this.currentModel,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: 0.3,
                    max_tokens: 500
                }
            })
        });
        
        if (!response.ok) {
            throw new Error(`AI query failed: ${response.statusText}`);
        }
        
        const result = await response.json();
        return result.response;
    }

    parseAIResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return parsed.insights || [];
        } catch (error) {
            // Fallback parsing for non-JSON responses
            return [{
                type: 'ai',
                level: 'info',
                title: 'AI Insight',
                message: response.substring(0, 200),
                action: 'Review the full AI analysis.',
                priority: 'low'
            }];
        }
    }
}

// Prediction Models
class InventoryPredictor {
    constructor(patterns) {
        this.patterns = patterns;
    }
    
    predictUsage(item, days = 7) {
        // Simple linear prediction based on historical usage
        const historicalUsage = this.patterns.itemUsage[item.id] || [];
        if (historicalUsage.length === 0) return { dailyAverage: 1, prediction: days };
        
        const dailyAverage = historicalUsage.reduce((sum, usage) => sum + usage, 0) / historicalUsage.length;
        return {
            dailyAverage,
            prediction: dailyAverage * days,
            confidence: Math.min(historicalUsage.length / 30, 1) // Higher confidence with more data
        };
    }
}

class SalesPredictor {
    constructor(patterns) {
        this.patterns = patterns;
    }
    
    predictDailySales(date) {
        const dayOfWeek = new Date(date).getDay();
        const historicalData = this.patterns.dailySales[dayOfWeek] || [];
        
        if (historicalData.length === 0) return { prediction: 0, confidence: 0 };
        
        const average = historicalData.reduce((sum, sales) => sum + sales, 0) / historicalData.length;
        return {
            prediction: average,
            confidence: Math.min(historicalData.length / 10, 1)
        };
    }
}

class StaffingPredictor {
    constructor(patterns) {
        this.patterns = patterns;
    }

    predictStaffingNeeds(hour, dayOfWeek) {
        const key = `${dayOfWeek}-${hour}`;
        const historical = this.patterns.staffingNeeds[key] || [];

        if (historical.length === 0) return { recommended: 2, confidence: 0 };

        const average = historical.reduce((sum, count) => sum + count, 0) / historical.length;
        return {
            recommended: Math.ceil(average),
            confidence: Math.min(historical.length / 5, 1)
        };
    }
}

// Continue ZaiqaAIAssistant class methods
ZaiqaAIAssistant.prototype.setupAIInterface = function() {
    this.createPersistentAIButton();
    this.createEnhancedChatWidget();
    this.createAIInsightsPanel();
    this.createAIConfigPanel();
    this.setupNaturalLanguageInterface();
    this.createAutomationStatusPanel();
};

ZaiqaAIAssistant.prototype.createPersistentAIButton = function() {
    // Create persistent AI button that stays on top of all pages
    const aiButton = document.createElement('div');
    aiButton.id = 'persistentAIButton';
    aiButton.className = 'persistent-ai-button';
    aiButton.innerHTML = `
        <button class="ai-main-button" onclick="zaiqaAI.toggleEnhancedChat()" title="AI Assistant - Click for help">
            <i class="fas fa-robot"></i>
            <span class="ai-status-dot ${this.isOnline ? 'online' : 'offline'}"></span>
        </button>
        <div class="ai-quick-actions-menu hidden" id="aiQuickActionsMenu">
            <div class="quick-action-item" onclick="zaiqaAI.quickCommand('check system errors')">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Check Errors</span>
            </div>
            <div class="quick-action-item" onclick="zaiqaAI.quickCommand('generate report')">
                <i class="fas fa-chart-bar"></i>
                <span>Generate Report</span>
            </div>
            <div class="quick-action-item" onclick="zaiqaAI.quickCommand('backup data')">
                <i class="fas fa-save"></i>
                <span>Backup Data</span>
            </div>
            <div class="quick-action-item" onclick="zaiqaAI.toggleAutomationMode()">
                <i class="fas fa-magic"></i>
                <span>Auto Mode</span>
            </div>
        </div>
    `;

    // Add right-click context menu
    aiButton.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        this.toggleQuickActionsMenu();
    });

    document.body.appendChild(aiButton);
};

ZaiqaAIAssistant.prototype.createEnhancedChatWidget = function() {
    // Create enhanced chat widget with automation features
    const chatWidget = document.createElement('div');
    chatWidget.id = 'enhancedAIChatWidget';
    chatWidget.className = 'enhanced-ai-chat-widget hidden';
    chatWidget.innerHTML = `
        <div class="ai-chat-header">
            <div class="ai-status">
                <i class="fas fa-robot"></i>
                <div class="ai-info">
                    <span class="ai-title">Zaiqa AI Assistant</span>
                    <span class="ai-subtitle">System Management & Automation</span>
                </div>
                <div class="ai-status-indicators">
                    <div class="status-indicator ${this.isOnline ? 'online' : 'offline'}" title="AI Connection">
                        <i class="fas fa-circle"></i>
                    </div>
                    <div class="status-indicator ${this.systemMonitoring ? 'active' : 'inactive'}" title="System Monitoring">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="status-indicator ${this.autoCorrection ? 'active' : 'inactive'}" title="Auto-Correction">
                        <i class="fas fa-magic"></i>
                    </div>
                </div>
            </div>
            <div class="ai-controls">
                <button class="ai-control-btn" onclick="zaiqaAI.toggleSystemMonitoring()" title="Toggle Monitoring">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="ai-control-btn" onclick="zaiqaAI.toggleAutoCorrection()" title="Toggle Auto-Fix">
                    <i class="fas fa-magic"></i>
                </button>
                <button class="ai-control-btn" onclick="zaiqaAI.showAutomationHistory()" title="Automation History">
                    <i class="fas fa-history"></i>
                </button>
                <button class="ai-control-btn" onclick="zaiqaAI.showAPIConfiguration()" title="API Configuration">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="ai-control-btn" onclick="zaiqaAI.toggleVoiceRecognition()" title="Voice Recognition" id="voiceToggleBtn">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="ai-chat-close" onclick="zaiqaAI.toggleEnhancedChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="ai-automation-status" id="aiAutomationStatus">
            <div class="status-item">
                <span>Executed Actions:</span>
                <span id="executedActionsCount">${this.executedActions.length}</span>
            </div>
            <div class="status-item">
                <span>Pending Actions:</span>
                <span id="pendingActionsCount">${this.pendingActions.length}</span>
            </div>
            <div class="status-item">
                <span>System Errors:</span>
                <span id="systemErrorsCount">0</span>
            </div>
        </div>

        <div class="ai-chat-messages" id="enhancedAIChatMessages">
            <div class="ai-message welcome-message">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-message-content">
                    <h4>🤖 Welcome to Enhanced AI Assistant!</h4>
                    <p>I can now automate tasks and manage your restaurant system. Try these commands:</p>
                    <div class="command-examples">
                        <div class="command-category">
                            <h5>📊 Sales Management</h5>
                            <ul>
                                <li>"Customer John bought items worth 1500"</li>
                                <li>"Table 4: 1 karahi, 3 people, 1 Pepsi, bill done"</li>
                                <li>"Add sale of 800 rupees"</li>
                            </ul>
                        </div>
                        <div class="command-category">
                            <h5>💰 Expense Management</h5>
                            <ul>
                                <li>"Bought soap for 100 rupees"</li>
                                <li>"Staff member Ali took 500 rupees advance"</li>
                                <li>"Paid 200 for electricity bill"</li>
                            </ul>
                        </div>
                        <div class="command-category">
                            <h5>🍽️ Menu & Pricing</h5>
                            <ul>
                                <li>"Set karahi price to 1400"</li>
                                <li>"Add menu item biryani for 800"</li>
                                <li>"Remove old item from menu"</li>
                            </ul>
                        </div>
                        <div class="command-category">
                            <h5>🔧 System Management</h5>
                            <ul>
                                <li>"Check system errors"</li>
                                <li>"Fix all errors"</li>
                                <li>"Generate report"</li>
                                <li>"Backup data"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="ai-chat-input-area">
            <div class="ai-quick-commands">
                <button class="quick-cmd-btn" onclick="zaiqaAI.insertCommand('Check system errors')">
                    <i class="fas fa-exclamation-triangle"></i> Check Errors
                </button>
                <button class="quick-cmd-btn" onclick="zaiqaAI.insertCommand('Generate report')">
                    <i class="fas fa-chart-bar"></i> Report
                </button>
                <button class="quick-cmd-btn" onclick="zaiqaAI.insertCommand('Backup data')">
                    <i class="fas fa-save"></i> Backup
                </button>
            </div>
            <div class="ai-chat-input">
                <input type="text" id="enhancedAIChatInput" placeholder="Type a command or ask a question..." />
                <button id="enhancedAIChatSend" onclick="zaiqaAI.sendEnhancedChatMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(chatWidget);

    // Setup enter key for chat input
    document.getElementById('enhancedAIChatInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            this.sendEnhancedChatMessage();
        }
    });
};

ZaiqaAIAssistant.prototype.createAutomationStatusPanel = function() {
    // This will be updated dynamically
    this.updateAutomationStatus();
};

ZaiqaAIAssistant.prototype.toggleEnhancedChat = function() {
    const widget = document.getElementById('enhancedAIChatWidget');
    if (widget) {
        widget.classList.toggle('hidden');
        if (!widget.classList.contains('hidden')) {
            document.getElementById('enhancedAIChatInput').focus();
            this.updateAutomationStatus();
        }
    }
};

ZaiqaAIAssistant.prototype.toggleQuickActionsMenu = function() {
    const menu = document.getElementById('aiQuickActionsMenu');
    if (menu) {
        menu.classList.toggle('hidden');
    }
};

ZaiqaAIAssistant.prototype.quickCommand = function(command) {
    // Hide quick actions menu
    this.toggleQuickActionsMenu();

    // Open chat and insert command
    const widget = document.getElementById('enhancedAIChatWidget');
    if (widget && widget.classList.contains('hidden')) {
        this.toggleEnhancedChat();
    }

    // Insert command and execute
    setTimeout(() => {
        const input = document.getElementById('enhancedAIChatInput');
        if (input) {
            input.value = command;
            this.sendEnhancedChatMessage();
        }
    }, 100);
};

ZaiqaAIAssistant.prototype.insertCommand = function(command) {
    const input = document.getElementById('enhancedAIChatInput');
    if (input) {
        input.value = command;
        input.focus();
    }
};

ZaiqaAIAssistant.prototype.sendEnhancedChatMessage = async function() {
    const input = document.getElementById('enhancedAIChatInput');
    const message = input.value.trim();

    if (!message) return;

    // Add user message to chat
    this.addEnhancedChatMessage(message, 'user');
    input.value = '';

    // Show typing indicator
    this.showEnhancedTypingIndicator();

    try {
        // Process the message
        const response = await this.processNaturalLanguageQuery(message);

        // Remove typing indicator and add AI response
        this.hideEnhancedTypingIndicator();
        this.addEnhancedChatMessage(response.message, 'ai', response.data, response.automated, response);

        // Update automation status if this was an automated action
        if (response.automated) {
            this.updateAutomationStatus();
        }

    } catch (error) {
        this.hideEnhancedTypingIndicator();
        this.addEnhancedChatMessage('Sorry, I encountered an error processing your request. Please try again.', 'ai');
        console.error('Enhanced chat error:', error);
    }
};

ZaiqaAIAssistant.prototype.addEnhancedChatMessage = function(message, sender, data = null, automated = false, response = null) {
    const messagesContainer = document.getElementById('enhancedAIChatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ${sender}-message ${automated ? 'automated-message' : ''}`;

    if (sender === 'user') {
        messageDiv.innerHTML = `
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-message-content">
                <p>${message}</p>
                <div class="message-timestamp">${new Date().toLocaleTimeString()}</div>
            </div>
        `;
    } else {
        const hasCorrections = response && response.corrections && response.corrections.applied;
        const hasSuggestions = response && response.suggestions && response.suggestions.length > 0;
        const hasStepByStep = response && response.stepByStep && response.stepByStep.length > 0;
        const requiresConfirmation = response && response.requiresConfirmation;

        messageDiv.innerHTML = `
            <div class="ai-avatar ${automated ? 'automated' : ''} ${requiresConfirmation ? 'confirmation' : ''}">
                <i class="fas fa-${automated ? 'magic' : requiresConfirmation ? 'question-circle' : 'robot'}"></i>
            </div>
            <div class="ai-message-content">
                <p>${message}</p>

                ${hasCorrections ? `
                    <div class="correction-notice">
                        <i class="fas fa-spell-check"></i>
                        <span>${response.corrections.explanation}</span>
                    </div>
                ` : ''}

                ${data ? this.formatEnhancedChatData(data) : ''}

                ${hasStepByStep ? `
                    <div class="step-by-step-explanation">
                        <h5><i class="fas fa-list-ol"></i> How I did this:</h5>
                        <ol>
                            ${response.stepByStep.map(step => `<li>${step}</li>`).join('')}
                        </ol>
                    </div>
                ` : ''}

                ${hasSuggestions ? `
                    <div class="ai-suggestions">
                        <h5><i class="fas fa-lightbulb"></i> Suggestions:</h5>
                        <ul>
                            ${response.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}

                ${requiresConfirmation ? `
                    <div class="confirmation-buttons">
                        <button class="confirm-btn" onclick="zaiqaAI.confirmSuggestedAction('${JSON.stringify(response.suggestedAction).replace(/'/g, "\\'")}')">
                            <i class="fas fa-check"></i> Yes, do this
                        </button>
                        <button class="cancel-btn" onclick="zaiqaAI.cancelSuggestedAction()">
                            <i class="fas fa-times"></i> No, cancel
                        </button>
                    </div>
                ` : ''}

                <div class="message-timestamp">
                    ${new Date().toLocaleTimeString()}
                    ${automated ? '<span class="automated-badge">Automated</span>' : ''}
                    ${response && response.metadata && response.metadata.aiStatus ?
                        `<span class="ai-status-badge ${response.metadata.aiStatus}">${response.metadata.aiStatus}</span>` : ''}
                </div>
            </div>
        `;
    }

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
};

ZaiqaAIAssistant.prototype.formatEnhancedChatData = function(data) {
    if (!data) return '';

    if (Array.isArray(data)) {
        return `
            <div class="enhanced-chat-data-list">
                ${data.map(item => `
                    <div class="enhanced-chat-data-item">
                        <strong>${item.name || item.title}</strong>
                        ${item.value ? `<span class="data-value">${item.value}</span>` : ''}
                        ${item.description ? `<small class="data-description">${item.description}</small>` : ''}
                    </div>
                `).join('')}
            </div>
        `;
    }

    if (typeof data === 'object') {
        return `
            <div class="enhanced-chat-data-object">
                ${Object.entries(data).map(([key, value]) => `
                    <div class="enhanced-chat-data-row">
                        <span class="data-key">${key}:</span>
                        <span class="data-value">${value}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    return `<div class="enhanced-chat-data-simple">${data}</div>`;
};

ZaiqaAIAssistant.prototype.showEnhancedTypingIndicator = function() {
    const messagesContainer = document.getElementById('enhancedAIChatMessages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'ai-message typing-indicator';
    typingDiv.id = 'enhancedTypingIndicator';
    typingDiv.innerHTML = `
        <div class="ai-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="ai-message-content">
            <div class="enhanced-typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="typing-text">AI is processing your request...</div>
        </div>
    `;
    messagesContainer.appendChild(typingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
};

ZaiqaAIAssistant.prototype.hideEnhancedTypingIndicator = function() {
    const typingIndicator = document.getElementById('enhancedTypingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
};

ZaiqaAIAssistant.prototype.updateAutomationStatus = function() {
    const executedCount = document.getElementById('executedActionsCount');
    const pendingCount = document.getElementById('pendingActionsCount');
    const errorsCount = document.getElementById('systemErrorsCount');

    if (executedCount) executedCount.textContent = this.executedActions.length;
    if (pendingCount) pendingCount.textContent = this.pendingActions.length;

    // Update error count asynchronously
    if (errorsCount) {
        this.performComprehensiveErrorCheck().then(errors => {
            errorsCount.textContent = errors.length;
            errorsCount.className = errors.length > 0 ? 'error-count' : '';
        });
    }
};

ZaiqaAIAssistant.prototype.toggleSystemMonitoring = function() {
    this.systemMonitoring = !this.systemMonitoring;

    if (this.systemMonitoring) {
        this.startSystemMonitoring();
        this.showAutomationNotification('system_monitoring', {}, {
            message: '👁️ System monitoring enabled'
        });
    } else {
        // Stop monitoring intervals
        Object.values(this.monitoringIntervals).forEach(interval => {
            clearInterval(interval);
        });
        this.monitoringIntervals = {};
        this.showAutomationNotification('system_monitoring', {}, {
            message: '👁️ System monitoring disabled'
        });
    }

    // Update UI indicators
    this.updateStatusIndicators();
};

ZaiqaAIAssistant.prototype.toggleAutoCorrection = function() {
    this.autoCorrection = !this.autoCorrection;

    this.showAutomationNotification('auto_correction', {}, {
        message: `🔧 Auto-correction ${this.autoCorrection ? 'enabled' : 'disabled'}`
    });

    // Update UI indicators
    this.updateStatusIndicators();
};

ZaiqaAIAssistant.prototype.updateStatusIndicators = function() {
    // Update status indicators in the chat header
    const indicators = document.querySelectorAll('.ai-status-indicators .status-indicator');
    if (indicators.length >= 3) {
        indicators[1].className = `status-indicator ${this.systemMonitoring ? 'active' : 'inactive'}`;
        indicators[2].className = `status-indicator ${this.autoCorrection ? 'active' : 'inactive'}`;
    }
};

ZaiqaAIAssistant.prototype.showAutomationHistory = function() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content automation-history-modal">
            <div class="modal-header">
                <h3><i class="fas fa-history"></i> Automation History</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="automation-history-content">
                    <div class="history-stats">
                        <div class="stat-item">
                            <span class="stat-number">${this.executedActions.length}</span>
                            <span class="stat-label">Total Actions</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${this.executedActions.filter(a => a.success).length}</span>
                            <span class="stat-label">Successful</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${this.executedActions.filter(a => !a.success).length}</span>
                            <span class="stat-label">Failed</span>
                        </div>
                    </div>

                    <div class="history-list">
                        <h4>Recent Actions</h4>
                        ${this.executedActions.slice(-10).reverse().map(action => `
                            <div class="history-item ${action.success ? 'success' : 'failed'}">
                                <div class="history-header">
                                    <span class="action-type">${action.action}</span>
                                    <span class="action-time">${new Date(action.timestamp).toLocaleString()}</span>
                                </div>
                                <div class="history-details">
                                    <p><strong>Query:</strong> ${action.query}</p>
                                    ${action.result ? `<p><strong>Result:</strong> ${action.result.message}</p>` : ''}
                                    ${action.error ? `<p class="error"><strong>Error:</strong> ${action.error}</p>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
};

ZaiqaAIAssistant.prototype.confirmSuggestedAction = async function(suggestedActionStr) {
    try {
        const suggestedAction = JSON.parse(suggestedActionStr);

        // Execute the suggested automation
        const result = await this.executeAutomation(
            suggestedAction.pattern.action,
            suggestedAction.params,
            suggestedAction.query
        );

        // Add confirmation message
        this.addEnhancedChatMessage(
            `✅ Confirmed and executed: ${result.message}`,
            'ai',
            result.data,
            true
        );

        this.updateAutomationStatus();

    } catch (error) {
        console.error('Failed to execute confirmed action:', error);
        this.addEnhancedChatMessage(
            `❌ Failed to execute the action: ${error.message}`,
            'ai'
        );
    }
};

ZaiqaAIAssistant.prototype.cancelSuggestedAction = function() {
    this.addEnhancedChatMessage(
        "❌ Action cancelled. Please try rephrasing your command with more specific details.",
        'ai',
        null,
        false,
        {
            suggestions: [
                "💡 Be more specific about amounts and names",
                "💡 Include all required information",
                "💡 Use exact item names from your menu"
            ]
        }
    );
};

ZaiqaAIAssistant.prototype.createAIChatWidget = function() {
    // Create floating AI chat button
    const chatButton = document.createElement('button');
    chatButton.className = 'ai-chat-button';
    chatButton.innerHTML = '<i class="fas fa-robot"></i>';
    chatButton.title = 'AI Assistant';
    chatButton.onclick = () => this.toggleChatWidget();

    // Create chat widget
    const chatWidget = document.createElement('div');
    chatWidget.id = 'aiChatWidget';
    chatWidget.className = 'ai-chat-widget hidden';
    chatWidget.innerHTML = `
        <div class="ai-chat-header">
            <div class="ai-status">
                <i class="fas fa-robot"></i>
                <span>Zaiqa AI Assistant</span>
                <div class="ai-status-indicator ${this.isOnline ? 'online' : 'offline'}"></div>
            </div>
            <button class="ai-chat-close" onclick="zaiqaAI.toggleChatWidget()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="ai-chat-messages" id="aiChatMessages">
            <div class="ai-message">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-message-content">
                    <p>Hello! I'm your Zaiqa AI Assistant. I can help you analyze your restaurant data, predict trends, and provide insights. Try asking me:</p>
                    <ul>
                        <li>"What are today's sales trends?"</li>
                        <li>"Which items need restocking?"</li>
                        <li>"Show me menu performance"</li>
                        <li>"Predict tomorrow's staffing needs"</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="ai-chat-input">
            <input type="text" id="aiChatInput" placeholder="Ask me anything about your restaurant..." />
            <button id="aiChatSend" onclick="zaiqaAI.sendChatMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
        <div class="ai-quick-actions">
            <button class="ai-quick-btn" onclick="zaiqaAI.quickAnalysis('sales')">
                <i class="fas fa-chart-line"></i> Sales Analysis
            </button>
            <button class="ai-quick-btn" onclick="zaiqaAI.quickAnalysis('inventory')">
                <i class="fas fa-boxes"></i> Inventory Check
            </button>
            <button class="ai-quick-btn" onclick="zaiqaAI.quickAnalysis('predictions')">
                <i class="fas fa-crystal-ball"></i> Predictions
            </button>
        </div>
    `;

    document.body.appendChild(chatButton);
    document.body.appendChild(chatWidget);

    // Setup enter key for chat input
    document.getElementById('aiChatInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            this.sendChatMessage();
        }
    });
};

ZaiqaAIAssistant.prototype.createAIInsightsPanel = function() {
    // Add AI insights to dashboard
    const dashboardPage = document.getElementById('dashboardPage');
    if (!dashboardPage) return;

    const insightsPanel = document.createElement('div');
    insightsPanel.className = 'ai-insights-panel';
    insightsPanel.innerHTML = `
        <div class="dashboard-card ai-insights-card">
            <div class="card-header">
                <h3><i class="fas fa-brain"></i> AI Insights</h3>
                <div class="ai-controls">
                    <button class="btn btn-sm" onclick="zaiqaAI.refreshInsights()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                    <button class="btn btn-sm" onclick="zaiqaAI.showAIConfig()">
                        <i class="fas fa-cog"></i> Settings
                    </button>
                </div>
            </div>
            <div class="card-content">
                <div id="aiInsightsList" class="ai-insights-list">
                    <div class="ai-loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Analyzing restaurant data...</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Insert after the stats grid
    const statsGrid = dashboardPage.querySelector('.stats-grid');
    if (statsGrid) {
        statsGrid.parentNode.insertBefore(insightsPanel, statsGrid.nextSibling);
    }
};

ZaiqaAIAssistant.prototype.createAIConfigPanel = function() {
    // AI configuration will be added to settings page
    const settingsPage = document.getElementById('settingsPage');
    if (!settingsPage) return;

    // Will be populated when settings page is loaded
    this.aiConfigHTML = `
        <div class="settings-card ai-settings-card">
            <h4><i class="fas fa-robot"></i> AI Assistant Configuration</h4>
            <div class="form-group">
                <label>Local AI Endpoint</label>
                <input type="text" id="aiEndpoint" class="form-control" value="${this.localModelEndpoint}" />
                <small>URL for your local AI server (Ollama, LM Studio, etc.)</small>
            </div>
            <div class="form-group">
                <label>AI Model</label>
                <select id="aiModel" class="form-control">
                    <option value="llama2">Llama 2</option>
                    <option value="mistral">Mistral</option>
                    <option value="codellama">Code Llama</option>
                    <option value="neural-chat">Neural Chat</option>
                </select>
            </div>
            <div class="form-group">
                <label>Analysis Frequency</label>
                <select id="aiAnalysisFreq" class="form-control">
                    <option value="1">Every minute (testing)</option>
                    <option value="5" selected>Every 5 minutes</option>
                    <option value="15">Every 15 minutes</option>
                    <option value="30">Every 30 minutes</option>
                    <option value="60">Every hour</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="aiAutoInsights" checked />
                    Enable automatic insights generation
                </label>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="aiPredictions" checked />
                    Enable predictive analytics
                </label>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="aiLearning" checked />
                    Enable machine learning from restaurant data
                </label>
            </div>
            <div class="ai-status-info">
                <h5>Current Status</h5>
                <div class="status-item">
                    <span>Connection:</span>
                    <span class="status-value ${this.isOnline ? 'online' : 'offline'}">
                        ${this.isOnline ? 'Connected' : 'Offline'}
                    </span>
                </div>
                <div class="status-item">
                    <span>Model:</span>
                    <span class="status-value">${this.currentModel || 'None'}</span>
                </div>
                <div class="status-item">
                    <span>Learning Data:</span>
                    <span class="status-value">${Object.keys(this.learningData).length} patterns</span>
                </div>
            </div>
            <div class="settings-actions">
                <button class="btn btn-primary" onclick="zaiqaAI.saveAIConfig()">
                    <i class="fas fa-save"></i> Save Configuration
                </button>
                <button class="btn btn-outline" onclick="zaiqaAI.testAIConnection()">
                    <i class="fas fa-plug"></i> Test Connection
                </button>
                <button class="btn btn-warning" onclick="zaiqaAI.resetLearningData()">
                    <i class="fas fa-brain"></i> Reset Learning Data
                </button>
            </div>
        </div>
    `;
};

ZaiqaAIAssistant.prototype.setupNaturalLanguageInterface = function() {
    this.nlpPatterns = {
        sales: [
            /sales|revenue|income|earnings|money/i,
            /how much.*made|total.*today|daily.*revenue/i,
            /best.*selling|top.*items|popular.*dishes/i
        ],
        inventory: [
            /inventory|stock|supplies|ingredients/i,
            /running.*low|need.*restock|out.*of/i,
            /how much.*left|remaining.*stock/i
        ],
        staff: [
            /staff|employees|workers|team/i,
            /how many.*working|staff.*schedule|who.*today/i,
            /payroll|salaries|wages/i
        ],
        predictions: [
            /predict|forecast|estimate|expect/i,
            /tomorrow|next.*week|future|upcoming/i,
            /trend|pattern|analysis/i
        ],
        menu: [
            /menu|dishes|items|food/i,
            /performance|profit|margin|cost/i,
            /popular|unpopular|slow.*moving/i
        ]
    };
};

ZaiqaAIAssistant.prototype.toggleChatWidget = function() {
    const widget = document.getElementById('aiChatWidget');
    if (widget) {
        widget.classList.toggle('hidden');
        if (!widget.classList.contains('hidden')) {
            document.getElementById('aiChatInput').focus();
        }
    }
};

ZaiqaAIAssistant.prototype.sendChatMessage = async function() {
    const input = document.getElementById('aiChatInput');
    const message = input.value.trim();

    if (!message) return;

    // Add user message to chat
    this.addChatMessage(message, 'user');
    input.value = '';

    // Show typing indicator
    this.showTypingIndicator();

    try {
        // Process the message
        const response = await this.processNaturalLanguageQuery(message);

        // Remove typing indicator and add AI response
        this.hideTypingIndicator();
        this.addChatMessage(response.message, 'ai', response.data);

    } catch (error) {
        this.hideTypingIndicator();
        this.addChatMessage('Sorry, I encountered an error processing your request. Please try again.', 'ai');
        console.error('Chat error:', error);
    }
};

ZaiqaAIAssistant.prototype.addChatMessage = function(message, sender, data = null) {
    const messagesContainer = document.getElementById('aiChatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ${sender}-message`;

    if (sender === 'user') {
        messageDiv.innerHTML = `
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-message-content">
                <p>${message}</p>
            </div>
        `;
    } else {
        messageDiv.innerHTML = `
            <div class="ai-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="ai-message-content">
                <p>${message}</p>
                ${data ? this.formatChatData(data) : ''}
            </div>
        `;
    }

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
};

ZaiqaAIAssistant.prototype.formatChatData = function(data) {
    if (!data) return '';

    if (Array.isArray(data)) {
        return `
            <div class="chat-data-list">
                ${data.map(item => `
                    <div class="chat-data-item">
                        <strong>${item.name || item.title}</strong>
                        ${item.value ? `<span>${item.value}</span>` : ''}
                        ${item.description ? `<small>${item.description}</small>` : ''}
                    </div>
                `).join('')}
            </div>
        `;
    }

    if (typeof data === 'object') {
        return `
            <div class="chat-data-object">
                ${Object.entries(data).map(([key, value]) => `
                    <div class="chat-data-row">
                        <span class="data-key">${key}:</span>
                        <span class="data-value">${value}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    return `<div class="chat-data-simple">${data}</div>`;
};

// Automation Methods - Sales Management
ZaiqaAIAssistant.prototype.automateAddSale = async function(params) {
    const { customer, amount } = params;
    const saleAmount = parseFloat(amount);

    if (isNaN(saleAmount) || saleAmount <= 0) {
        throw new Error('Invalid sale amount');
    }

    // Create order object
    const order = {
        id: 'AI_' + Date.now(),
        customer_name: customer || 'Walk-in Customer',
        service_type: 'dine_in',
        customer_count: 1,
        items: [{
            name: 'Manual Sale Entry',
            price: saleAmount,
            quantity: 1
        }],
        total_amount: saleAmount,
        payment_method: 'cash',
        status: 'completed',
        created_at: new Date().toISOString(),
        automated: true,
        ai_generated: true
    };

    // Add to orders
    const orders = this.safeParseJSON(localStorage.getItem('restaurantOrders'), []);
    orders.push(order);
    localStorage.setItem('restaurantOrders', JSON.stringify(orders));

    // Update app if available
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Added sale of PKR ${saleAmount.toLocaleString()} for ${customer || 'walk-in customer'}`,
        data: {
            'Order ID': order.id,
            'Customer': order.customer_name,
            'Amount': `PKR ${saleAmount.toLocaleString()}`,
            'Status': 'Completed'
        }
    };
};

ZaiqaAIAssistant.prototype.automateTableOrder = async function(params) {
    const { tableNumber, items, customerCount, additionalItems } = params;

    // Parse items and calculate total
    const menuItems = this.safeParseJSON(localStorage.getItem('menuItems'), []);
    const orderItems = [];
    let totalAmount = 0;

    // Parse main items
    const itemsList = items.split(',').map(item => item.trim());
    itemsList.forEach(itemStr => {
        const match = itemStr.match(/(\d+)\s*(.+)/);
        if (match) {
            const quantity = parseInt(match[1]);
            const itemName = match[2].trim();

            // Find menu item
            const menuItem = menuItems.find(mi =>
                mi.name.toLowerCase().includes(itemName.toLowerCase()) ||
                itemName.toLowerCase().includes(mi.name.toLowerCase())
            );

            if (menuItem) {
                const price = menuItem.basePrice || 800; // Default price
                orderItems.push({
                    name: menuItem.name,
                    price: price,
                    quantity: quantity
                });
                totalAmount += price * quantity;
            } else {
                // Add as custom item with estimated price
                const estimatedPrice = this.estimateItemPrice(itemName);
                orderItems.push({
                    name: itemName,
                    price: estimatedPrice,
                    quantity: quantity
                });
                totalAmount += estimatedPrice * quantity;
            }
        }
    });

    // Parse additional items
    if (additionalItems) {
        const additionalList = additionalItems.split(',').map(item => item.trim());
        additionalList.forEach(itemStr => {
            const match = itemStr.match(/(\d+)\s*(.+)/);
            if (match) {
                const quantity = parseInt(match[1]);
                const itemName = match[2].trim();

                const menuItem = menuItems.find(mi =>
                    mi.name.toLowerCase().includes(itemName.toLowerCase())
                );

                const price = menuItem ? menuItem.basePrice : this.estimateItemPrice(itemName);
                orderItems.push({
                    name: itemName,
                    price: price,
                    quantity: quantity
                });
                totalAmount += price * quantity;
            }
        });
    }

    // Create order
    const order = {
        id: 'AI_TABLE_' + Date.now(),
        table_number: parseInt(tableNumber),
        customer_count: parseInt(customerCount) || 1,
        service_type: 'dine_in',
        items: orderItems,
        total_amount: totalAmount,
        payment_method: 'cash',
        status: 'completed',
        created_at: new Date().toISOString(),
        automated: true,
        ai_generated: true
    };

    // Add to orders
    const orders = this.safeParseJSON(localStorage.getItem('restaurantOrders'), []);
    orders.push(order);
    localStorage.setItem('restaurantOrders', JSON.stringify(orders));

    // Update table status
    const tables = this.safeParseJSON(localStorage.getItem('restaurantTables'), []);
    const table = tables.find(t => t.number === parseInt(tableNumber));
    if (table) {
        table.status = 'occupied';
        table.currentOrder = order.id;
        localStorage.setItem('restaurantTables', JSON.stringify(tables));
    }

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Processed order for Table ${tableNumber}: ${orderItems.length} items, ${customerCount} customers, Total: PKR ${totalAmount.toLocaleString()}`,
        data: {
            'Table': tableNumber,
            'Items': orderItems.length,
            'Customers': customerCount,
            'Total': `PKR ${totalAmount.toLocaleString()}`,
            'Order ID': order.id
        }
    };
};

ZaiqaAIAssistant.prototype.automateQuickSale = async function(params) {
    const { amount } = params;
    return await this.automateAddSale({ customer: 'Quick Sale', amount });
};

ZaiqaAIAssistant.prototype.automateAddExpense = async function(params) {
    const { item, amount, description } = params;
    const expenseAmount = parseFloat(amount || item);
    const expenseDescription = description || item;

    if (isNaN(expenseAmount) || expenseAmount <= 0) {
        throw new Error('Invalid expense amount');
    }

    const expense = {
        id: 'AI_EXP_' + Date.now(),
        description: expenseDescription,
        amount: expenseAmount,
        category: this.categorizeExpense(expenseDescription),
        date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        automated: true,
        ai_generated: true
    };

    // Add to expenses
    const expenses = this.safeParseJSON(localStorage.getItem('expenses'), []);
    expenses.push(expense);
    localStorage.setItem('expenses', JSON.stringify(expenses));

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Added expense: ${expenseDescription} - PKR ${expenseAmount.toLocaleString()}`,
        data: {
            'Description': expenseDescription,
            'Amount': `PKR ${expenseAmount.toLocaleString()}`,
            'Category': expense.category,
            'Date': expense.date
        }
    };
};

ZaiqaAIAssistant.prototype.automateStaffAdvance = async function(params) {
    const { staffName, amount } = params;
    const advanceAmount = parseFloat(amount);

    if (isNaN(advanceAmount) || advanceAmount <= 0) {
        throw new Error('Invalid advance amount');
    }

    // Find staff member
    const staff = this.safeParseJSON(localStorage.getItem('staffMembers'), []);
    let staffMember = staff.find(s =>
        s.name.toLowerCase().includes(staffName.toLowerCase()) ||
        staffName.toLowerCase().includes(s.name.toLowerCase())
    );

    if (!staffMember) {
        throw new Error(`Staff member "${staffName}" not found`);
    }

    // Add to udhar (advance)
    const udhar = {
        id: 'AI_ADV_' + Date.now(),
        name: staffMember.name,
        amount: advanceAmount,
        description: `Advance payment to ${staffMember.name}`,
        date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        type: 'advance',
        automated: true,
        ai_generated: true
    };

    const udhars = this.safeParseJSON(localStorage.getItem('udhars'), []);
    udhars.push(udhar);
    localStorage.setItem('udhars', JSON.stringify(udhars));

    // Update staff advance balance
    staffMember.advanceAmount = (staffMember.advanceAmount || 0) + advanceAmount;
    localStorage.setItem('staffMembers', JSON.stringify(staff));

    // Add as expense
    const expense = {
        id: 'AI_EXP_ADV_' + Date.now(),
        description: `Staff advance - ${staffMember.name}`,
        amount: advanceAmount,
        category: 'Staff',
        date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        automated: true,
        ai_generated: true
    };

    const expenses = this.safeParseJSON(localStorage.getItem('expenses'), []);
    expenses.push(expense);
    localStorage.setItem('expenses', JSON.stringify(expenses));

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Added PKR ${advanceAmount.toLocaleString()} advance for ${staffMember.name}`,
        data: {
            'Staff Member': staffMember.name,
            'Advance Amount': `PKR ${advanceAmount.toLocaleString()}`,
            'Total Advance': `PKR ${staffMember.advanceAmount.toLocaleString()}`,
            'Date': udhar.date
        }
    };
};

// Menu Management Automation
ZaiqaAIAssistant.prototype.automateUpdateMenuPrice = async function(params) {
    const { itemName, price } = params;
    const newPrice = parseFloat(price);

    if (isNaN(newPrice) || newPrice <= 0) {
        throw new Error('Invalid price amount');
    }

    // Find and update menu item
    const menuItems = this.safeParseJSON(localStorage.getItem('menuItems'), []);
    let menuItem = menuItems.find(item =>
        item.name.toLowerCase().includes(itemName.toLowerCase()) ||
        itemName.toLowerCase().includes(item.name.toLowerCase())
    );

    if (!menuItem) {
        throw new Error(`Menu item "${itemName}" not found`);
    }

    const oldPrice = menuItem.basePrice;
    menuItem.basePrice = newPrice;
    menuItem.lastUpdated = new Date().toISOString();
    menuItem.updatedBy = 'AI Assistant';

    localStorage.setItem('menuItems', JSON.stringify(menuItems));

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Updated ${menuItem.name} price from PKR ${oldPrice} to PKR ${newPrice}`,
        data: {
            'Item': menuItem.name,
            'Old Price': `PKR ${oldPrice}`,
            'New Price': `PKR ${newPrice}`,
            'Change': `${newPrice > oldPrice ? '+' : ''}PKR ${(newPrice - oldPrice).toLocaleString()}`
        }
    };
};

ZaiqaAIAssistant.prototype.automateAddMenuItem = async function(params) {
    const { itemName, price } = params;
    const itemPrice = parseFloat(price);

    if (isNaN(itemPrice) || itemPrice <= 0) {
        throw new Error('Invalid price amount');
    }

    const menuItems = this.safeParseJSON(localStorage.getItem('menuItems'), []);

    // Check if item already exists
    const existingItem = menuItems.find(item =>
        item.name.toLowerCase() === itemName.toLowerCase()
    );

    if (existingItem) {
        throw new Error(`Menu item "${itemName}" already exists`);
    }

    // Create new menu item
    const newItem = {
        id: 'AI_MENU_' + Date.now(),
        name: itemName,
        basePrice: itemPrice,
        category: this.categorizeMenuItem(itemName),
        isAvailable: true,
        description: `Added by AI Assistant`,
        created_at: new Date().toISOString(),
        automated: true,
        ai_generated: true
    };

    menuItems.push(newItem);
    localStorage.setItem('menuItems', JSON.stringify(menuItems));

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Added new menu item: ${itemName} - PKR ${itemPrice}`,
        data: {
            'Item': itemName,
            'Price': `PKR ${itemPrice}`,
            'Category': newItem.category,
            'Status': 'Available'
        }
    };
};

ZaiqaAIAssistant.prototype.automateRemoveMenuItem = async function(params) {
    const { itemName } = params;

    const menuItems = this.safeParseJSON(localStorage.getItem('menuItems'), []);
    const itemIndex = menuItems.findIndex(item =>
        item.name.toLowerCase().includes(itemName.toLowerCase()) ||
        itemName.toLowerCase().includes(item.name.toLowerCase())
    );

    if (itemIndex === -1) {
        throw new Error(`Menu item "${itemName}" not found`);
    }

    const removedItem = menuItems[itemIndex];
    menuItems.splice(itemIndex, 1);
    localStorage.setItem('menuItems', JSON.stringify(menuItems));

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Removed menu item: ${removedItem.name}`,
        data: {
            'Removed Item': removedItem.name,
            'Price': `PKR ${removedItem.basePrice}`,
            'Category': removedItem.category
        }
    };
};

// Inventory Management Automation
ZaiqaAIAssistant.prototype.automateAddInventory = async function(params) {
    const { quantity, itemName } = params;
    const addQuantity = parseFloat(quantity);

    if (isNaN(addQuantity) || addQuantity <= 0) {
        throw new Error('Invalid quantity');
    }

    const inventory = this.safeParseJSON(localStorage.getItem('inventoryItems'), []);
    let inventoryItem = inventory.find(item =>
        item.name.toLowerCase().includes(itemName.toLowerCase()) ||
        itemName.toLowerCase().includes(item.name.toLowerCase())
    );

    if (!inventoryItem) {
        // Create new inventory item
        inventoryItem = {
            id: 'AI_INV_' + Date.now(),
            name: itemName,
            currentQuantity: addQuantity,
            unit: this.guessUnit(itemName),
            minimumQuantity: Math.max(5, addQuantity * 0.2),
            costPerUnit: this.estimateCostPerUnit(itemName),
            category: this.categorizeInventoryItem(itemName),
            created_at: new Date().toISOString(),
            automated: true,
            ai_generated: true
        };
        inventory.push(inventoryItem);
    } else {
        inventoryItem.currentQuantity += addQuantity;
        inventoryItem.lastUpdated = new Date().toISOString();
    }

    localStorage.setItem('inventoryItems', JSON.stringify(inventory));

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    return {
        message: `✅ Added ${addQuantity} ${inventoryItem.unit} of ${inventoryItem.name} to inventory`,
        data: {
            'Item': inventoryItem.name,
            'Added': `${addQuantity} ${inventoryItem.unit}`,
            'Total Stock': `${inventoryItem.currentQuantity} ${inventoryItem.unit}`,
            'Status': inventoryItem.currentQuantity > inventoryItem.minimumQuantity ? 'Well Stocked' : 'Low Stock'
        }
    };
};

ZaiqaAIAssistant.prototype.automateUseInventory = async function(params) {
    const { quantity, itemName } = params;
    const useQuantity = parseFloat(quantity);

    if (isNaN(useQuantity) || useQuantity <= 0) {
        throw new Error('Invalid quantity');
    }

    const inventory = this.safeParseJSON(localStorage.getItem('inventoryItems'), []);
    let inventoryItem = inventory.find(item =>
        item.name.toLowerCase().includes(itemName.toLowerCase()) ||
        itemName.toLowerCase().includes(item.name.toLowerCase())
    );

    if (!inventoryItem) {
        throw new Error(`Inventory item "${itemName}" not found`);
    }

    if (inventoryItem.currentQuantity < useQuantity) {
        throw new Error(`Insufficient stock. Available: ${inventoryItem.currentQuantity}, Requested: ${useQuantity}`);
    }

    inventoryItem.currentQuantity -= useQuantity;
    inventoryItem.lastUsed = new Date().toISOString();
    inventoryItem.lastUsage = useQuantity;

    localStorage.setItem('inventoryItems', JSON.stringify(inventory));

    // Update app
    if (window.app && window.app.updateDashboardStats) {
        window.app.updateDashboardStats();
    }

    const status = inventoryItem.currentQuantity <= inventoryItem.minimumQuantity ? 'Low Stock - Reorder Needed' : 'OK';

    return {
        message: `✅ Used ${useQuantity} ${inventoryItem.unit} of ${inventoryItem.name}`,
        data: {
            'Item': inventoryItem.name,
            'Used': `${useQuantity} ${inventoryItem.unit}`,
            'Remaining': `${inventoryItem.currentQuantity} ${inventoryItem.unit}`,
            'Status': status
        }
    };
};

ZaiqaAIAssistant.prototype.automateCheckStock = async function(params) {
    const { itemName } = params;

    const inventory = this.safeParseJSON(localStorage.getItem('inventoryItems'), []);
    let inventoryItem = inventory.find(item =>
        item.name.toLowerCase().includes(itemName.toLowerCase()) ||
        itemName.toLowerCase().includes(item.name.toLowerCase())
    );

    if (!inventoryItem) {
        throw new Error(`Inventory item "${itemName}" not found`);
    }

    const daysRemaining = inventoryItem.currentQuantity / Math.max(inventoryItem.lastUsage || 1, 1);
    const status = inventoryItem.currentQuantity <= inventoryItem.minimumQuantity ? 'Low Stock' : 'Well Stocked';

    return {
        message: `📦 Stock check for ${inventoryItem.name}: ${inventoryItem.currentQuantity} ${inventoryItem.unit} remaining`,
        data: {
            'Item': inventoryItem.name,
            'Current Stock': `${inventoryItem.currentQuantity} ${inventoryItem.unit}`,
            'Minimum Required': `${inventoryItem.minimumQuantity} ${inventoryItem.unit}`,
            'Status': status,
            'Days Remaining': `~${Math.round(daysRemaining)} days`
        }
    };
};

// Staff Management Automation
ZaiqaAIAssistant.prototype.automateStaffAttendance = async function(params) {
    const { staffName } = params;

    const staff = this.safeParseJSON(localStorage.getItem('staffMembers'), []);
    let staffMember = staff.find(s =>
        s.name.toLowerCase().includes(staffName.toLowerCase()) ||
        staffName.toLowerCase().includes(s.name.toLowerCase())
    );

    if (!staffMember) {
        throw new Error(`Staff member "${staffName}" not found`);
    }

    const today = new Date().toISOString().split('T')[0];

    // Initialize attendance if not exists
    if (!staffMember.attendance) {
        staffMember.attendance = {};
    }

    // Mark attendance
    staffMember.attendance[today] = {
        present: true,
        checkIn: new Date().toISOString(),
        markedBy: 'AI Assistant'
    };

    localStorage.setItem('staffMembers', JSON.stringify(staff));

    return {
        message: `✅ Marked attendance for ${staffMember.name}`,
        data: {
            'Staff Member': staffMember.name,
            'Date': today,
            'Check-in Time': new Date().toLocaleTimeString(),
            'Status': 'Present'
        }
    };
};

ZaiqaAIAssistant.prototype.automateStaffPayment = async function(params) {
    const { staffName } = params;

    const staff = this.safeParseJSON(localStorage.getItem('staffMembers'), []);
    let staffMember = staff.find(s =>
        s.name.toLowerCase().includes(staffName.toLowerCase()) ||
        staffName.toLowerCase().includes(s.name.toLowerCase())
    );

    if (!staffMember) {
        throw new Error(`Staff member "${staffName}" not found`);
    }

    const dailyWage = staffMember.dailyWage || (staffMember.monthlySalary || 30000) / 30;
    const today = new Date().toISOString().split('T')[0];

    // Add to expenses
    const expense = {
        id: 'AI_WAGE_' + Date.now(),
        description: `Daily wage - ${staffMember.name}`,
        amount: dailyWage,
        category: 'Staff',
        date: today,
        created_at: new Date().toISOString(),
        automated: true,
        ai_generated: true
    };

    const expenses = this.safeParseJSON(localStorage.getItem('expenses'), []);
    expenses.push(expense);
    localStorage.setItem('expenses', JSON.stringify(expenses));

    // Update staff dehari balance
    staffMember.dehariBalance = (staffMember.dehariBalance || 0) + dailyWage;
    staffMember.lastPaidDate = today;
    localStorage.setItem('staffMembers', JSON.stringify(staff));

    return {
        message: `✅ Paid daily wage of PKR ${dailyWage.toLocaleString()} to ${staffMember.name}`,
        data: {
            'Staff Member': staffMember.name,
            'Daily Wage': `PKR ${dailyWage.toLocaleString()}`,
            'Total Dehari': `PKR ${staffMember.dehariBalance.toLocaleString()}`,
            'Date': today
        }
    };
};

ZaiqaAIAssistant.prototype.automateAddStaff = async function(params) {
    const { staffName, salary } = params;
    const monthlySalary = parseFloat(salary);

    if (isNaN(monthlySalary) || monthlySalary <= 0) {
        throw new Error('Invalid salary amount');
    }

    const staff = this.safeParseJSON(localStorage.getItem('staffMembers'), []);

    // Check if staff already exists
    const existingStaff = staff.find(s =>
        s.name.toLowerCase() === staffName.toLowerCase()
    );

    if (existingStaff) {
        throw new Error(`Staff member "${staffName}" already exists`);
    }

    const newStaff = {
        id: 'AI_STAFF_' + Date.now(),
        name: staffName,
        position: 'General Staff',
        monthlySalary: monthlySalary,
        dailyWage: monthlySalary / 30,
        dehariBalance: 0,
        advanceAmount: 0,
        isActive: true,
        joinDate: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        automated: true,
        ai_generated: true
    };

    staff.push(newStaff);
    localStorage.setItem('staffMembers', JSON.stringify(staff));

    return {
        message: `✅ Added new staff member: ${staffName} with salary PKR ${monthlySalary.toLocaleString()}`,
        data: {
            'Name': staffName,
            'Monthly Salary': `PKR ${monthlySalary.toLocaleString()}`,
            'Daily Wage': `PKR ${Math.round(monthlySalary / 30).toLocaleString()}`,
            'Join Date': newStaff.joinDate
        }
    };
};

// System Management Automation
ZaiqaAIAssistant.prototype.automateErrorCheck = async function(params) {
    const errors = await this.performComprehensiveErrorCheck();

    return {
        message: `🔍 System check complete. Found ${errors.length} issues.`,
        data: errors.length > 0 ? {
            'Total Issues': errors.length,
            'Critical': errors.filter(e => e.severity === 'critical').length,
            'Warnings': errors.filter(e => e.severity === 'warning').length,
            'Info': errors.filter(e => e.severity === 'info').length
        } : { 'Status': 'All systems operational' }
    };
};

ZaiqaAIAssistant.prototype.automateErrorFix = async function(params) {
    const errors = await this.performComprehensiveErrorCheck();
    const fixedErrors = [];

    for (const error of errors) {
        try {
            const fixed = await this.autoFixError(error);
            if (fixed) {
                fixedErrors.push(error);
            }
        } catch (fixError) {
            console.error('Failed to fix error:', fixError);
        }
    }

    return {
        message: `🔧 Auto-fix complete. Fixed ${fixedErrors.length} out of ${errors.length} issues.`,
        data: {
            'Total Issues': errors.length,
            'Fixed': fixedErrors.length,
            'Remaining': errors.length - fixedErrors.length,
            'Success Rate': `${Math.round((fixedErrors.length / Math.max(errors.length, 1)) * 100)}%`
        }
    };
};

ZaiqaAIAssistant.prototype.automateBackup = async function(params) {
    const backupData = {
        timestamp: new Date().toISOString(),
        orders: this.safeParseJSON(localStorage.getItem('restaurantOrders'), []),
        inventory: this.safeParseJSON(localStorage.getItem('inventoryItems'), []),
        staff: this.safeParseJSON(localStorage.getItem('staffMembers'), []),
        expenses: this.safeParseJSON(localStorage.getItem('expenses'), []),
        udhars: this.safeParseJSON(localStorage.getItem('udhars'), []),
        khata: this.safeParseJSON(localStorage.getItem('khata'), []),
        tables: this.safeParseJSON(localStorage.getItem('restaurantTables'), []),
        menuItems: this.safeParseJSON(localStorage.getItem('menuItems'), []),
        aiData: {
            learningData: this.learningData,
            executedActions: this.executedActions,
            insights: this.insights
        }
    };

    const backupKey = `backup_${Date.now()}`;
    localStorage.setItem(backupKey, JSON.stringify(backupData));

    // Keep only last 5 backups
    const allKeys = Object.keys(localStorage);
    const backupKeys = allKeys.filter(key => key.startsWith('backup_')).sort();
    if (backupKeys.length > 5) {
        const oldBackups = backupKeys.slice(0, backupKeys.length - 5);
        oldBackups.forEach(key => localStorage.removeItem(key));
    }

    return {
        message: `✅ System backup created successfully`,
        data: {
            'Backup ID': backupKey,
            'Timestamp': new Date().toLocaleString(),
            'Data Size': `${JSON.stringify(backupData).length} characters`,
            'Items Backed Up': Object.keys(backupData).length - 1
        }
    };
};

ZaiqaAIAssistant.prototype.automateGenerateReport = async function(params) {
    const data = this.gatherCurrentData();
    const today = new Date().toDateString();

    // Calculate comprehensive statistics
    const todayOrders = data.orders.filter(order =>
        new Date(order.created_at).toDateString() === today
    );

    const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
    const todayExpenses = data.expenses
        .filter(expense => expense.date === new Date().toISOString().split('T')[0])
        .reduce((sum, expense) => sum + (expense.amount || 0), 0);

    const lowStockItems = data.inventory.filter(item =>
        item.currentQuantity <= (item.minimumQuantity || 10)
    );

    const activeStaff = data.staff.filter(s => s.isActive !== false);
    const totalUdhars = data.udhars.reduce((sum, udhar) => sum + (udhar.amount || 0), 0);

    const report = {
        generatedAt: new Date().toISOString(),
        summary: {
            todayRevenue,
            todayExpenses,
            netProfit: todayRevenue - todayExpenses,
            totalOrders: todayOrders.length,
            activeStaff: activeStaff.length,
            lowStockItems: lowStockItems.length,
            totalUdhars
        },
        details: {
            topSellingItems: this.getTopSellingItems(data),
            recentExpenses: data.expenses.slice(-5),
            staffSummary: activeStaff.map(s => ({
                name: s.name,
                position: s.position,
                dehariBalance: s.dehariBalance || 0
            }))
        }
    };

    // Save report
    const reportKey = `report_${Date.now()}`;
    localStorage.setItem(reportKey, JSON.stringify(report));

    return {
        message: `📊 Comprehensive report generated successfully`,
        data: {
            'Today Revenue': `PKR ${todayRevenue.toLocaleString()}`,
            'Today Expenses': `PKR ${todayExpenses.toLocaleString()}`,
            'Net Profit': `PKR ${(todayRevenue - todayExpenses).toLocaleString()}`,
            'Total Orders': todayOrders.length,
            'Low Stock Items': lowStockItems.length,
            'Report ID': reportKey
        }
    };
};

// Utility Methods for Automation
ZaiqaAIAssistant.prototype.estimateItemPrice = function(itemName) {
    const priceMap = {
        'karahi': 1400,
        'biryani': 800,
        'rice': 400,
        'dal': 300,
        'roti': 15,
        'naan': 25,
        'pepsi': 80,
        'coke': 80,
        'water': 20,
        'tea': 50,
        'coffee': 100
    };

    const lowerName = itemName.toLowerCase();
    for (const [key, price] of Object.entries(priceMap)) {
        if (lowerName.includes(key)) {
            return price;
        }
    }

    return 200; // Default price
};

ZaiqaAIAssistant.prototype.categorizeExpense = function(description) {
    const categories = {
        'Staff': ['salary', 'wage', 'advance', 'staff', 'employee'],
        'Inventory': ['bought', 'purchase', 'ingredient', 'food', 'supply'],
        'Utilities': ['electricity', 'gas', 'water', 'internet', 'phone'],
        'Maintenance': ['repair', 'fix', 'maintenance', 'cleaning'],
        'Marketing': ['advertisement', 'promotion', 'marketing'],
        'Other': []
    };

    const lowerDesc = description.toLowerCase();
    for (const [category, keywords] of Object.entries(categories)) {
        if (keywords.some(keyword => lowerDesc.includes(keyword))) {
            return category;
        }
    }

    return 'Other';
};

ZaiqaAIAssistant.prototype.categorizeMenuItem = function(itemName) {
    const categories = {
        'Main Course': ['karahi', 'biryani', 'curry', 'chicken', 'beef', 'mutton'],
        'Rice & Bread': ['rice', 'roti', 'naan', 'bread'],
        'Beverages': ['pepsi', 'coke', 'juice', 'water', 'tea', 'coffee'],
        'Appetizers': ['starter', 'appetizer', 'salad'],
        'Desserts': ['dessert', 'sweet', 'ice cream']
    };

    const lowerName = itemName.toLowerCase();
    for (const [category, keywords] of Object.entries(categories)) {
        if (keywords.some(keyword => lowerName.includes(keyword))) {
            return category;
        }
    }

    return 'Other';
};

ZaiqaAIAssistant.prototype.categorizeInventoryItem = function(itemName) {
    const categories = {
        'Meat': ['chicken', 'beef', 'mutton', 'meat'],
        'Vegetables': ['onion', 'tomato', 'potato', 'vegetable'],
        'Spices': ['salt', 'pepper', 'spice', 'masala'],
        'Dairy': ['milk', 'yogurt', 'cheese', 'butter'],
        'Beverages': ['pepsi', 'coke', 'juice', 'water'],
        'Supplies': ['soap', 'tissue', 'bag', 'container']
    };

    const lowerName = itemName.toLowerCase();
    for (const [category, keywords] of Object.entries(categories)) {
        if (keywords.some(keyword => lowerName.includes(keyword))) {
            return category;
        }
    }

    return 'Other';
};

ZaiqaAIAssistant.prototype.guessUnit = function(itemName) {
    const unitMap = {
        'kg': ['chicken', 'beef', 'mutton', 'meat', 'onion', 'potato', 'tomato'],
        'liter': ['milk', 'oil', 'water'],
        'pieces': ['pepsi', 'coke', 'bottle', 'can'],
        'packet': ['spice', 'masala', 'salt', 'sugar'],
        'dozen': ['egg']
    };

    const lowerName = itemName.toLowerCase();
    for (const [unit, keywords] of Object.entries(unitMap)) {
        if (keywords.some(keyword => lowerName.includes(keyword))) {
            return unit;
        }
    }

    return 'units';
};

ZaiqaAIAssistant.prototype.estimateCostPerUnit = function(itemName) {
    const costMap = {
        'chicken': 800,
        'beef': 1200,
        'mutton': 1500,
        'onion': 100,
        'tomato': 150,
        'potato': 80,
        'pepsi': 70,
        'coke': 70,
        'water': 15
    };

    const lowerName = itemName.toLowerCase();
    for (const [key, cost] of Object.entries(costMap)) {
        if (lowerName.includes(key)) {
            return cost;
        }
    }

    return 50; // Default cost
};

ZaiqaAIAssistant.prototype.getTopSellingItems = function(data) {
    const itemSales = {};

    data.orders.forEach(order => {
        order.items?.forEach(item => {
            if (!itemSales[item.name]) {
                itemSales[item.name] = { quantity: 0, revenue: 0 };
            }
            itemSales[item.name].quantity += item.quantity;
            itemSales[item.name].revenue += item.price * item.quantity;
        });
    });

    return Object.entries(itemSales)
        .sort(([,a], [,b]) => b.revenue - a.revenue)
        .slice(0, 5)
        .map(([name, data]) => ({ name, ...data }));
};

// Error Detection and Auto-Correction Systems
ZaiqaAIAssistant.prototype.performComprehensiveErrorCheck = async function() {
    const errors = [];

    try {
        // Check order validation errors
        const orderErrors = await this.checkOrderValidationErrors();
        errors.push(...orderErrors);

        // Check staff payment tracking errors
        const staffErrors = await this.checkStaffPaymentErrors();
        errors.push(...staffErrors);

        // Check data integrity errors
        const dataErrors = await this.checkDataIntegrityErrors();
        errors.push(...dataErrors);

        // Check inventory consistency errors
        const inventoryErrors = await this.checkInventoryConsistencyErrors();
        errors.push(...inventoryErrors);

    } catch (error) {
        console.error('Error during comprehensive error check:', error);
        errors.push({
            type: 'system',
            severity: 'critical',
            message: 'Failed to perform complete error check',
            details: error.message
        });
    }

    return errors;
};

ZaiqaAIAssistant.prototype.checkOrderValidationErrors = async function() {
    const errors = [];
    const orders = this.safeParseJSON(localStorage.getItem('restaurantOrders'), []);

    orders.forEach(order => {
        // Check for orders without proper totals
        if (!order.total_amount || order.total_amount <= 0) {
            errors.push({
                type: 'order_validation',
                severity: 'warning',
                message: `Order ${order.id} has invalid total amount`,
                orderId: order.id,
                autoFixable: true
            });
        }

        // Check for orders without items
        if (!order.items || order.items.length === 0) {
            errors.push({
                type: 'order_validation',
                severity: 'critical',
                message: `Order ${order.id} has no items`,
                orderId: order.id,
                autoFixable: false
            });
        }

        // Check for inconsistent item totals
        if (order.items && order.items.length > 0) {
            const calculatedTotal = order.items.reduce((sum, item) =>
                sum + (item.price * item.quantity), 0
            );

            if (Math.abs(calculatedTotal - order.total_amount) > 1) {
                errors.push({
                    type: 'order_validation',
                    severity: 'warning',
                    message: `Order ${order.id} total mismatch: calculated ${calculatedTotal}, recorded ${order.total_amount}`,
                    orderId: order.id,
                    calculatedTotal,
                    recordedTotal: order.total_amount,
                    autoFixable: true
                });
            }
        }
    });

    return errors;
};

ZaiqaAIAssistant.prototype.checkStaffPaymentErrors = async function() {
    const errors = [];
    const staff = this.safeParseJSON(localStorage.getItem('staffMembers'), []);
    const expenses = this.safeParseJSON(localStorage.getItem('expenses'), []);
    const today = new Date().toISOString().split('T')[0];

    staff.forEach(member => {
        if (!member.isActive) return;

        // Check if daily wage is due but not recorded
        const dailyWage = member.dailyWage || (member.monthlySalary || 30000) / 30;
        const lastPaidDate = member.lastPaidDate;

        if (!lastPaidDate || lastPaidDate !== today) {
            // Check if wage expense exists for today
            const todayWageExpense = expenses.find(expense =>
                expense.date === today &&
                expense.description.toLowerCase().includes(member.name.toLowerCase()) &&
                expense.description.toLowerCase().includes('wage')
            );

            if (!todayWageExpense) {
                errors.push({
                    type: 'staff_payment',
                    severity: 'warning',
                    message: `Daily wage for ${member.name} not recorded for today`,
                    staffId: member.id,
                    staffName: member.name,
                    dailyWage,
                    autoFixable: true
                });
            }
        }
    });

    return errors;
};

ZaiqaAIAssistant.prototype.checkDataIntegrityErrors = async function() {
    const errors = [];

    // Check for duplicate IDs
    const allData = [
        { name: 'orders', data: this.safeParseJSON(localStorage.getItem('restaurantOrders'), []) },
        { name: 'staff', data: this.safeParseJSON(localStorage.getItem('staffMembers'), []) },
        { name: 'inventory', data: this.safeParseJSON(localStorage.getItem('inventoryItems'), []) },
        { name: 'expenses', data: this.safeParseJSON(localStorage.getItem('expenses'), []) }
    ];

    allData.forEach(({ name, data }) => {
        const ids = data.map(item => item.id).filter(id => id);
        const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);

        if (duplicateIds.length > 0) {
            errors.push({
                type: 'data_integrity',
                severity: 'critical',
                message: `Duplicate IDs found in ${name}: ${duplicateIds.join(', ')}`,
                dataType: name,
                duplicateIds,
                autoFixable: true
            });
        }
    });

    return errors;
};

ZaiqaAIAssistant.prototype.checkInventoryConsistencyErrors = async function() {
    const errors = [];
    const inventory = this.safeParseJSON(localStorage.getItem('inventoryItems'), []);

    inventory.forEach(item => {
        // Check for negative quantities
        if (item.currentQuantity < 0) {
            errors.push({
                type: 'inventory_consistency',
                severity: 'critical',
                message: `Negative quantity for ${item.name}: ${item.currentQuantity}`,
                itemId: item.id,
                itemName: item.name,
                quantity: item.currentQuantity,
                autoFixable: true
            });
        }

        // Check for missing minimum quantities
        if (!item.minimumQuantity || item.minimumQuantity <= 0) {
            errors.push({
                type: 'inventory_consistency',
                severity: 'info',
                message: `Missing minimum quantity for ${item.name}`,
                itemId: item.id,
                itemName: item.name,
                autoFixable: true
            });
        }
    });

    return errors;
};

// Auto-Fix Methods
ZaiqaAIAssistant.prototype.autoFixError = async function(error) {
    if (!error.autoFixable) {
        return false;
    }

    try {
        switch (error.type) {
            case 'order_validation':
                return await this.fixOrderValidationError(error);
            case 'staff_payment':
                return await this.fixStaffPaymentError(error);
            case 'data_integrity':
                return await this.fixDataIntegrityError(error);
            case 'inventory_consistency':
                return await this.fixInventoryConsistencyError(error);
            default:
                return false;
        }
    } catch (fixError) {
        console.error('Failed to auto-fix error:', fixError);
        return false;
    }
};

ZaiqaAIAssistant.prototype.fixOrderValidationError = async function(error) {
    const orders = this.safeParseJSON(localStorage.getItem('restaurantOrders'), []);
    const order = orders.find(o => o.id === error.orderId);

    if (!order) return false;

    if (error.calculatedTotal && error.recordedTotal) {
        // Fix total amount mismatch
        order.total_amount = error.calculatedTotal;
        order.fixedBy = 'AI Assistant';
        order.fixedAt = new Date().toISOString();

        localStorage.setItem('restaurantOrders', JSON.stringify(orders));
        console.log(`✅ Fixed order total for ${error.orderId}`);
        return true;
    }

    if (!order.total_amount || order.total_amount <= 0) {
        // Calculate total from items
        if (order.items && order.items.length > 0) {
            order.total_amount = order.items.reduce((sum, item) =>
                sum + (item.price * item.quantity), 0
            );
            order.fixedBy = 'AI Assistant';
            order.fixedAt = new Date().toISOString();

            localStorage.setItem('restaurantOrders', JSON.stringify(orders));
            console.log(`✅ Fixed missing total for ${error.orderId}`);
            return true;
        }
    }

    return false;
};

ZaiqaAIAssistant.prototype.fixStaffPaymentError = async function(error) {
    try {
        // Automatically pay staff daily wage
        await this.automateStaffPayment({ staffName: error.staffName });
        console.log(`✅ Auto-paid daily wage for ${error.staffName}`);
        return true;
    } catch (payError) {
        console.error('Failed to auto-pay staff:', payError);
        return false;
    }
};

ZaiqaAIAssistant.prototype.fixDataIntegrityError = async function(error) {
    if (error.duplicateIds) {
        const data = this.safeParseJSON(localStorage.getItem(
            error.dataType === 'orders' ? 'restaurantOrders' :
            error.dataType === 'staff' ? 'staffMembers' :
            error.dataType === 'inventory' ? 'inventoryItems' : 'expenses'
        ), []);

        // Fix duplicate IDs by regenerating them
        const seenIds = new Set();
        data.forEach(item => {
            if (seenIds.has(item.id)) {
                item.id = `${error.dataType.toUpperCase()}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                item.fixedBy = 'AI Assistant';
                item.fixedAt = new Date().toISOString();
            }
            seenIds.add(item.id);
        });

        const storageKey = error.dataType === 'orders' ? 'restaurantOrders' :
                          error.dataType === 'staff' ? 'staffMembers' :
                          error.dataType === 'inventory' ? 'inventoryItems' : 'expenses';

        localStorage.setItem(storageKey, JSON.stringify(data));
        console.log(`✅ Fixed duplicate IDs in ${error.dataType}`);
        return true;
    }

    return false;
};

ZaiqaAIAssistant.prototype.fixInventoryConsistencyError = async function(error) {
    const inventory = this.safeParseJSON(localStorage.getItem('inventoryItems'), []);
    const item = inventory.find(i => i.id === error.itemId);

    if (!item) return false;

    if (error.quantity < 0) {
        // Fix negative quantity
        item.currentQuantity = 0;
        item.fixedBy = 'AI Assistant';
        item.fixedAt = new Date().toISOString();

        localStorage.setItem('inventoryItems', JSON.stringify(inventory));
        console.log(`✅ Fixed negative quantity for ${error.itemName}`);
        return true;
    }

    if (!item.minimumQuantity || item.minimumQuantity <= 0) {
        // Set reasonable minimum quantity
        item.minimumQuantity = Math.max(5, Math.round(item.currentQuantity * 0.2));
        item.fixedBy = 'AI Assistant';
        item.fixedAt = new Date().toISOString();

        localStorage.setItem('inventoryItems', JSON.stringify(inventory));
        console.log(`✅ Set minimum quantity for ${error.itemName}`);
        return true;
    }

    return false;
};

// Notification and UI Methods
ZaiqaAIAssistant.prototype.showAutomationNotification = function(action, params, result) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'ai-automation-notification';
    notification.innerHTML = `
        <div class="notification-header">
            <i class="fas fa-robot"></i>
            <span>AI Assistant</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div class="notification-content">
            <h4>✅ Action Completed</h4>
            <p>${result.message}</p>
            <div class="notification-details">
                <small>Command: ${action}</small>
            </div>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);

    // Also show in app notification if available
    if (window.app && window.app.showNotification) {
        window.app.showNotification(result.message, 'success');
    }
};

// System Monitoring Methods
ZaiqaAIAssistant.prototype.performDataIntegrityCheck = function() {
    if (!this.systemMonitoring) return;

    this.checkOrderValidationErrors().then(errors => {
        if (errors.length > 0) {
            console.log(`🔍 Found ${errors.length} order validation issues`);
            if (this.autoCorrection) {
                this.autoFixOrderErrors(errors);
            }
        }
    });
};

ZaiqaAIAssistant.prototype.checkMissingTransactions = function() {
    if (!this.systemMonitoring) return;

    // Check for orders that might be missing from sales
    const orders = this.safeParseJSON(localStorage.getItem('restaurantOrders'), []);
    const today = new Date().toDateString();

    const todayOrders = orders.filter(order =>
        new Date(order.created_at).toDateString() === today &&
        order.status === 'completed'
    );

    // Log any suspicious patterns
    if (todayOrders.length === 0 && new Date().getHours() > 12) {
        console.log('⚠️ No completed orders found for today after 12 PM');
    }
};

ZaiqaAIAssistant.prototype.checkStaffPaymentDue = function() {
    if (!this.systemMonitoring) return;

    this.checkStaffPaymentErrors().then(errors => {
        if (errors.length > 0) {
            console.log(`💰 Found ${errors.length} staff payment issues`);
            if (this.autoCorrection) {
                this.autoFixStaffPaymentErrors(errors);
            }
        }
    });
};

ZaiqaAIAssistant.prototype.detectAndLogErrors = function() {
    if (!this.systemMonitoring) return;

    this.performComprehensiveErrorCheck().then(errors => {
        if (errors.length > 0) {
            this.errorLog.push({
                timestamp: new Date().toISOString(),
                errors: errors,
                autoFixed: 0
            });

            console.log(`🚨 System check found ${errors.length} issues`);

            // Keep only last 100 error logs
            if (this.errorLog.length > 100) {
                this.errorLog = this.errorLog.slice(-100);
            }
        }
    });
};

ZaiqaAIAssistant.prototype.autoFixOrderErrors = async function(errors) {
    let fixedCount = 0;
    for (const error of errors) {
        if (await this.autoFixError(error)) {
            fixedCount++;
        }
    }

    if (fixedCount > 0) {
        console.log(`🔧 Auto-fixed ${fixedCount} order validation errors`);
    }
};

ZaiqaAIAssistant.prototype.autoFixStaffPaymentErrors = async function(errors) {
    let fixedCount = 0;
    for (const error of errors) {
        if (await this.autoFixError(error)) {
            fixedCount++;
        }
    }

    if (fixedCount > 0) {
        console.log(`💰 Auto-fixed ${fixedCount} staff payment errors`);
    }
};

ZaiqaAIAssistant.prototype.processNaturalLanguageQuery = async function(query) {
    console.log('🧠 Processing unlimited multi-language query with Roman Urdu:', query);

    // Apply human-like intelligence preprocessing with fallback
    const contextAnalysis = this.contextualProcessor ? this.contextualProcessor.analyzeContext(query) : { currentTopic: 'general' };
    const emotionalState = this.emotionalProcessor ? this.emotionalProcessor.detectEmotion(query) : 'neutral';
    const urgencyLevel = this.emotionalProcessor ? this.emotionalProcessor.assessUrgency(query) : 'medium';

    console.log('🎭 Context Analysis:', contextAnalysis);
    console.log('😊 Emotional State:', emotionalState);
    console.log('⚡ Urgency Level:', urgencyLevel);

    // Enhanced language detection including Roman Urdu
    const detectedLanguage = this.detectLanguageEnhanced(query);
    console.log('🌍 Detected language:', detectedLanguage);

    // Store enriched context for future reference
    this.contextHistory.push({
        query: query,
        language: detectedLanguage,
        emotion: emotionalState,
        urgency: urgencyLevel,
        context: contextAnalysis,
        timestamp: new Date().toISOString()
    });

    // Keep only last 15 queries for richer context
    if (this.contextHistory.length > 15) {
        this.contextHistory = this.contextHistory.slice(-15);
    }

    // Apply comprehensive multi-language processing
    const processedQuery = await this.processMultiLanguageQueryEnhanced(query, detectedLanguage);
    console.log('🔄 Processed query:', processedQuery);

    // Apply intelligent error tolerance and correction
    const correctedQuery = this.applyIntelligentErrorTolerance(processedQuery, contextAnalysis);
    console.log('📝 Intelligently corrected query:', correctedQuery);

    // Apply human-like understanding and gap filling
    const enrichedQuery = this.fillInformationGaps(correctedQuery, contextAnalysis);
    console.log('🧩 Enriched query:', enrichedQuery);

    // First check if this is an unlimited automation command
    const automationResult = await this.processUnlimitedAutomationCommand(enrichedQuery, detectedLanguage, contextAnalysis);
    if (automationResult) {
        return this.enhanceIntelligentResponse(automationResult, query, enrichedQuery, detectedLanguage, emotionalState);
    }

    // Try advanced internet AI for complex queries with context
    if (this.internetIntegration) {
        const internetResult = await this.queryAdvancedInternetAI(query, detectedLanguage, contextAnalysis);
        if (internetResult) {
            return this.enhanceIntelligentResponse({
                message: internetResult,
                data: null,
                automated: false,
                source: 'advanced_internet_ai'
            }, query, enrichedQuery, detectedLanguage, emotionalState);
        }
    }

    // Apply intelligent intent classification
    const intent = this.classifyIntentIntelligently(enrichedQuery, contextAnalysis);
    console.log('🎯 Intelligently classified intent:', intent);

    // Get comprehensive data with predictive insights
    const data = this.gatherComprehensiveData(contextAnalysis);

    // Process with human-like intelligence
    let result;
    switch (intent) {
        case 'sales':
            result = await this.handleSalesQueryIntelligently(enrichedQuery, data, contextAnalysis);
            break;
        case 'inventory':
            result = await this.handleInventoryQueryIntelligently(enrichedQuery, data, contextAnalysis);
            break;
        case 'staff':
            result = await this.handleStaffQueryIntelligently(enrichedQuery, data, contextAnalysis);
            break;
        case 'predictions':
            result = await this.handlePredictionsQueryIntelligently(enrichedQuery, data, contextAnalysis);
            break;
        case 'menu':
            result = await this.handleMenuQueryIntelligently(enrichedQuery, data, contextAnalysis);
            break;
        case 'system':
            result = await this.handleSystemQueryIntelligently(enrichedQuery, data, contextAnalysis);
            break;
        default:
            result = await this.handleGeneralQueryIntelligently(enrichedQuery, data, contextAnalysis);
    }

    // Add proactive suggestions and improvements
    result = this.addProactiveSuggestions(result, contextAnalysis);

    return this.enhanceIntelligentResponse(result, query, enrichedQuery, detectedLanguage, emotionalState);
};

ZaiqaAIAssistant.prototype.detectLanguageEnhanced = function(text) {
    // Check for Urdu script
    const urduRegex = /[\u0600-\u06FF]/;
    const englishRegex = /[a-zA-Z]/;
    const romanUrduDetected = this.romanUrduProcessor.detectRomanUrdu(text);

    const hasUrdu = urduRegex.test(text);
    const hasEnglish = englishRegex.test(text);

    if (hasUrdu && hasEnglish) return 'mixed';
    if (hasUrdu) return 'ur';
    if (romanUrduDetected && hasEnglish) return 'roman_urdu';
    if (hasEnglish) return 'en';
    return 'en'; // default
};

ZaiqaAIAssistant.prototype.processMultiLanguageQueryEnhanced = async function(query, language) {
    if (language === 'en') {
        return query; // No processing needed for pure English
    }

    if (language === 'ur') {
        // Translate Urdu to English for processing
        return this.languageDetection.translateUrduToEnglish(query);
    }

    if (language === 'roman_urdu') {
        // Process Roman Urdu
        return this.romanUrduProcessor.translateToEnglish(query);
    }

    if (language === 'mixed') {
        // Handle mixed language including Roman Urdu
        let processed = query;

        // First check if it contains Roman Urdu
        if (this.romanUrduProcessor.detectRomanUrdu(processed)) {
            processed = this.romanUrduProcessor.translateToEnglish(processed);
        }

        // Then handle regular Urdu script
        processed = this.languageDetection.translateUrduToEnglish(processed);

        // Normalize mixed patterns
        processed = this.normalizeMixedLanguagePatterns(processed);

        return processed;
    }

    return query;
};

ZaiqaAIAssistant.prototype.applyIntelligentErrorTolerance = function(query, context) {
    if (!this.errorTolerance) return query;

    let corrected = this.applyErrorTolerance(query);

    // Apply context-aware corrections
    if (context.previousQueries && context.previousQueries.length > 0) {
        const recentContext = context.previousQueries[context.previousQueries.length - 1];

        // If previous query was about similar topic, use that context for correction
        if (recentContext.topic === context.currentTopic) {
            corrected = this.applyContextualCorrections(corrected, recentContext);
        }
    }

    // Apply restaurant-specific intelligent corrections
    corrected = this.applyRestaurantIntelligentCorrections(corrected);

    return corrected;
};

ZaiqaAIAssistant.prototype.applyRestaurantIntelligentCorrections = function(query) {
    let corrected = query;

    // Intelligent menu item corrections
    const menuItems = ['karahi', 'biryani', 'pulao', 'dal', 'roti', 'naan'];
    menuItems.forEach(item => {
        const variations = this.generateItemVariations(item);
        variations.forEach(variation => {
            if (corrected.includes(variation) && !corrected.includes(item)) {
                corrected = corrected.replace(new RegExp(variation, 'gi'), item);
            }
        });
    });

    // Intelligent amount corrections (common mistakes)
    corrected = corrected.replace(/(\d+)k/gi, '$1000'); // 1k -> 1000
    corrected = corrected.replace(/(\d+)\s*hundred/gi, '$100'); // 5 hundred -> 500

    // Intelligent staff name corrections (common Pakistani names)
    const commonNames = ['ali', 'ahmed', 'hassan', 'fatima', 'ayesha', 'muhammad'];
    commonNames.forEach(name => {
        const nameVariations = [name + 'h', name.slice(0, -1), name + 'a'];
        nameVariations.forEach(variation => {
            if (corrected.toLowerCase().includes(variation) && !corrected.toLowerCase().includes(name)) {
                corrected = corrected.replace(new RegExp(variation, 'gi'), name);
            }
        });
    });

    return corrected;
};

ZaiqaAIAssistant.prototype.generateItemVariations = function(item) {
    const variations = [item];

    // Common spelling variations
    if (item === 'karahi') {
        variations.push('karhai', 'karhahi', 'karai', 'curry');
    } else if (item === 'biryani') {
        variations.push('biriyani', 'biryaani', 'biriani');
    } else if (item === 'pulao') {
        variations.push('pilaf', 'pilau', 'polo');
    }

    return variations;
};

ZaiqaAIAssistant.prototype.fillInformationGaps = function(query, context) {
    let enriched = query;

    // Fill missing customer information
    if (enriched.includes('customer') && !this.extractCustomerName(enriched)) {
        if (context.lastCustomer) {
            enriched = enriched.replace('customer', `customer ${context.lastCustomer}`);
        } else {
            enriched = enriched.replace('customer', 'customer walk-in');
        }
    }

    // Fill missing table information
    if (enriched.includes('table') && !this.extractTableNumber(enriched)) {
        if (context.lastTable) {
            enriched = enriched.replace('table', `table ${context.lastTable}`);
        }
    }

    // Fill missing amounts with intelligent estimates
    if (this.containsTransactionKeywords(enriched) && !this.extractAmount(enriched)) {
        const estimatedAmount = this.estimateAmount(enriched, context);
        if (estimatedAmount) {
            enriched += ` worth ${estimatedAmount}`;
        }
    }

    // Fill missing time context
    if (this.containsTimeKeywords(enriched) && !this.extractTimeReference(enriched)) {
        enriched += ' today';
    }

    return enriched;
};

ZaiqaAIAssistant.prototype.extractCustomerName = function(query) {
    const nameMatch = query.match(/customer\s+([a-zA-Z]+)/i);
    return nameMatch ? nameMatch[1] : null;
};

ZaiqaAIAssistant.prototype.extractTableNumber = function(query) {
    const tableMatch = query.match(/table\s+(\d+)/i);
    return tableMatch ? tableMatch[1] : null;
};

ZaiqaAIAssistant.prototype.extractAmount = function(query) {
    const amountMatch = query.match(/(\d+)\s*(?:rupees?|rs|pkr)?/i);
    return amountMatch ? amountMatch[1] : null;
};

ZaiqaAIAssistant.prototype.containsTransactionKeywords = function(query) {
    const keywords = ['buy', 'bought', 'order', 'sale', 'sell', 'pay', 'cost', 'price'];
    return keywords.some(keyword => query.toLowerCase().includes(keyword));
};

ZaiqaAIAssistant.prototype.containsTimeKeywords = function(query) {
    const keywords = ['report', 'sales', 'attendance', 'summary', 'total'];
    return keywords.some(keyword => query.toLowerCase().includes(keyword));
};

ZaiqaAIAssistant.prototype.extractTimeReference = function(query) {
    const timeWords = ['today', 'yesterday', 'this week', 'this month', 'now'];
    return timeWords.some(time => query.toLowerCase().includes(time));
};

ZaiqaAIAssistant.prototype.estimateAmount = function(query, context) {
    // Intelligent amount estimation based on menu items
    const menuPrices = {
        'karahi': 1400,
        'biryani': 800,
        'pulao': 600,
        'dal': 300,
        'roti': 20,
        'naan': 40,
        'chai': 50,
        'pepsi': 80
    };

    let estimatedTotal = 0;
    Object.entries(menuPrices).forEach(([item, price]) => {
        if (query.toLowerCase().includes(item)) {
            const quantityMatch = query.match(new RegExp(`(\\d+)\\s*${item}`, 'i'));
            const quantity = quantityMatch ? parseInt(quantityMatch[1]) : 1;
            estimatedTotal += price * quantity;
        }
    });

    return estimatedTotal > 0 ? estimatedTotal : null;
};

// Human-like Intelligence Processing Methods
ZaiqaAIAssistant.prototype.analyzeConversationContext = function(query) {
    const context = {
        currentTopic: this.identifyTopic(query),
        previousQueries: this.contextHistory.slice(-5),
        userPattern: this.analyzeUserPattern(),
        businessContext: this.analyzeBusinessContext(),
        timeContext: this.analyzeTimeContext(),
        lastCustomer: this.getLastCustomer(),
        lastTable: this.getLastTable(),
        recentItems: this.getRecentItems()
    };

    return context;
};

ZaiqaAIAssistant.prototype.identifyTopic = function(query) {
    const topics = {
        'sales': ['customer', 'order', 'buy', 'sell', 'sale', 'bill'],
        'inventory': ['stock', 'inventory', 'add', 'use', 'check'],
        'staff': ['staff', 'employee', 'salary', 'wage', 'attendance'],
        'menu': ['menu', 'price', 'item', 'dish', 'food'],
        'finance': ['expense', 'cost', 'profit', 'loss', 'money'],
        'system': ['system', 'error', 'backup', 'report', 'fix']
    };

    const queryLower = query.toLowerCase();
    for (const [topic, keywords] of Object.entries(topics)) {
        if (keywords.some(keyword => queryLower.includes(keyword))) {
            return topic;
        }
    }
    return 'general';
};

ZaiqaAIAssistant.prototype.analyzeUserPattern = function() {
    if (this.contextHistory.length < 3) return 'new_user';

    const recentQueries = this.contextHistory.slice(-5);
    const topics = recentQueries.map(q => q.context?.currentTopic || 'general');

    // Check for patterns
    if (topics.every(t => t === topics[0])) return 'focused_session';
    if (topics.includes('sales') && topics.includes('inventory')) return 'operational_management';
    if (topics.includes('staff') && topics.includes('finance')) return 'administrative_tasks';

    return 'mixed_usage';
};

ZaiqaAIAssistant.prototype.analyzeBusinessContext = function() {
    const currentHour = new Date().getHours();
    const isBusinessHours = currentHour >= 11 && currentHour <= 23;
    const isPeakTime = (currentHour >= 12 && currentHour <= 14) || (currentHour >= 19 && currentHour <= 21);

    return {
        isBusinessHours,
        isPeakTime,
        currentHour,
        dayOfWeek: new Date().getDay(),
        isWeekend: [0, 6].includes(new Date().getDay())
    };
};

ZaiqaAIAssistant.prototype.analyzeTimeContext = function() {
    const now = new Date();
    return {
        hour: now.getHours(),
        day: now.getDate(),
        month: now.getMonth() + 1,
        year: now.getFullYear(),
        dayName: now.toLocaleDateString('en-US', { weekday: 'long' }),
        monthName: now.toLocaleDateString('en-US', { month: 'long' })
    };
};

ZaiqaAIAssistant.prototype.detectUserEmotion = function(query) {
    const emotionIndicators = {
        'frustrated': ['urgent', 'quickly', 'asap', 'problem', 'issue', 'wrong', 'error'],
        'satisfied': ['good', 'great', 'excellent', 'perfect', 'thanks', 'thank you'],
        'confused': ['how', 'what', 'why', 'confused', 'understand', 'help'],
        'urgent': ['urgent', 'immediately', 'now', 'quick', 'fast', 'hurry'],
        'casual': ['please', 'can you', 'could you', 'maybe', 'perhaps']
    };

    const queryLower = query.toLowerCase();
    for (const [emotion, indicators] of Object.entries(emotionIndicators)) {
        if (indicators.some(indicator => queryLower.includes(indicator))) {
            return emotion;
        }
    }
    return 'neutral';
};

ZaiqaAIAssistant.prototype.assessTaskUrgency = function(query) {
    const urgencyKeywords = {
        'high': ['urgent', 'immediately', 'asap', 'emergency', 'critical', 'now'],
        'medium': ['soon', 'quickly', 'fast', 'priority', 'important'],
        'low': ['when possible', 'later', 'eventually', 'sometime']
    };

    const queryLower = query.toLowerCase();
    for (const [level, keywords] of Object.entries(urgencyKeywords)) {
        if (keywords.some(keyword => queryLower.includes(keyword))) {
            return level;
        }
    }

    // Context-based urgency assessment
    const businessContext = this.analyzeBusinessContext();
    if (businessContext.isPeakTime) return 'medium';
    if (!businessContext.isBusinessHours) return 'low';

    return 'medium'; // default
};

ZaiqaAIAssistant.prototype.getLastCustomer = function() {
    for (let i = this.contextHistory.length - 1; i >= 0; i--) {
        const query = this.contextHistory[i].query;
        const customerMatch = query.match(/customer\s+([a-zA-Z]+)/i);
        if (customerMatch) return customerMatch[1];
    }
    return null;
};

ZaiqaAIAssistant.prototype.getLastTable = function() {
    for (let i = this.contextHistory.length - 1; i >= 0; i--) {
        const query = this.contextHistory[i].query;
        const tableMatch = query.match(/table\s+(\d+)/i);
        if (tableMatch) return tableMatch[1];
    }
    return null;
};

ZaiqaAIAssistant.prototype.getRecentItems = function() {
    const items = [];
    const menuItems = ['karahi', 'biryani', 'pulao', 'dal', 'roti', 'naan', 'chai', 'pepsi'];

    this.contextHistory.slice(-5).forEach(entry => {
        menuItems.forEach(item => {
            if (entry.query.toLowerCase().includes(item) && !items.includes(item)) {
                items.push(item);
            }
        });
    });

    return items;
};

ZaiqaAIAssistant.prototype.makeIntelligentAssumptions = function(query, context) {
    const assumptions = {};

    // Assume customer type based on context
    if (query.includes('customer') && !this.extractCustomerName(query)) {
        if (context.businessContext.isPeakTime) {
            assumptions.customerType = 'dine-in';
        } else {
            assumptions.customerType = 'takeaway';
        }
    }

    // Assume quantity based on context
    if (this.containsMenuItems(query) && !this.extractQuantity(query)) {
        if (context.businessContext.isPeakTime) {
            assumptions.defaultQuantity = 2; // Assume family order
        } else {
            assumptions.defaultQuantity = 1; // Assume single order
        }
    }

    // Assume payment method
    if (query.includes('payment') || query.includes('bill')) {
        if (context.businessContext.isPeakTime) {
            assumptions.paymentMethod = 'cash'; // Faster during peak
        } else {
            assumptions.paymentMethod = 'any';
        }
    }

    return assumptions;
};

ZaiqaAIAssistant.prototype.containsMenuItems = function(query) {
    const menuItems = ['karahi', 'biryani', 'pulao', 'dal', 'roti', 'naan', 'chai', 'pepsi'];
    return menuItems.some(item => query.toLowerCase().includes(item));
};

ZaiqaAIAssistant.prototype.extractQuantity = function(query) {
    const quantityMatch = query.match(/(\d+)\s*(?:pieces?|items?|orders?)?/i);
    return quantityMatch ? parseInt(quantityMatch[1]) : null;
};

ZaiqaAIAssistant.prototype.processUnlimitedAutomationCommand = async function(query, language, context) {
    console.log('🚀 Processing unlimited automation command with full capabilities:', query);

    // Remove ALL restrictions for unlimited processing
    const originalSafetyMode = this.safetyRestrictions;
    const originalCommandLimitations = this.commandLimitations;
    this.safetyRestrictions = false;
    this.commandLimitations = false;

    try {
        // Check all command patterns including Roman Urdu
        let bestMatch = null;
        let bestScore = 0;
        let bestCategory = null;

        // Check English patterns
        const englishResult = await this.checkCommandPatterns(this.commandPatterns, query);
        if (englishResult.match) {
            return await this.executeUnlimitedCommand(englishResult.pattern, englishResult.match, query, language, context);
        }

        // Check Urdu patterns
        if (language === 'ur' || language === 'mixed') {
            const urduResult = await this.checkCommandPatterns(this.urduPatterns, query);
            if (urduResult.match) {
                return await this.executeUnlimitedCommand(urduResult.pattern, urduResult.match, query, language, context);
            }
        }

        // Check Roman Urdu patterns
        if (language === 'roman_urdu' || language === 'mixed') {
            const romanUrduResult = await this.checkCommandPatterns(this.romanUrduPatterns, query);
            if (romanUrduResult.match) {
                return await this.executeUnlimitedCommand(romanUrduResult.pattern, romanUrduResult.match, query, language, context);
            }
        }

        // Check mixed language patterns
        const mixedResult = await this.checkCommandPatterns(this.mixedLanguagePatterns, query);
        if (mixedResult.match) {
            return await this.executeUnlimitedCommand(mixedResult.pattern, mixedResult.match, query, language, context);
        }

        // Try unlimited fuzzy matching with very low threshold
        const fuzzyResult = await this.performUnlimitedFuzzyMatching(query, language, context);
        if (fuzzyResult) {
            return fuzzyResult;
        }

        // Try advanced AI interpretation with unlimited capabilities
        if (this.internetIntegration) {
            const aiInterpretation = await this.getAdvancedAICommandInterpretation(query, language, context);
            if (aiInterpretation) {
                return aiInterpretation;
            }
        }

        // Try single command complex operation processing
        const complexResult = await this.processSingleCommandComplexOperation(query, language, context);
        if (complexResult) {
            return complexResult;
        }

        // If nothing matches, use human-like intelligence to understand and execute
        return await this.processWithHumanLikeIntelligence(query, language, context);

    } finally {
        // Note: We don't restore restrictions in unlimited mode
        console.log('🔓 Maintaining unlimited mode - restrictions remain disabled');
    }
};

ZaiqaAIAssistant.prototype.executeUnlimitedCommand = async function(pattern, match, query, language, context) {
    console.log('⚡ Executing unlimited command with full system access:', pattern.action);

    // Extract parameters without any restrictions
    const params = {};
    pattern.params.forEach((paramName, index) => {
        params[paramName] = match[index + 1];
    });

    // Apply intelligent assumptions to fill missing parameters
    const assumptions = this.makeIntelligentAssumptions(query, context);
    Object.assign(params, assumptions);

    try {
        // Execute with unlimited permissions and capabilities
        const result = await this.executeAutomationUnlimited(pattern.action, params, query, context);

        // Apply self-improvement based on execution
        this.selfImprovement.learnFromInteractions(pattern.action, params, result);

        return {
            message: result.message,
            data: result.data || null,
            automated: true,
            action: pattern.action,
            params: params,
            language: language,
            unlimited: true,
            context: context,
            assumptions: assumptions,
            executionTime: new Date().toISOString()
        };
    } catch (error) {
        console.error('Unlimited command execution failed:', error);

        // Try unlimited recovery with full system access
        const recovery = await this.attemptUnlimitedRecovery(pattern.action, params, error, language, context);
        if (recovery) return recovery;

        // If recovery fails, create solution dynamically
        const dynamicSolution = await this.createDynamicSolution(pattern.action, params, error, context);
        if (dynamicSolution) return dynamicSolution;

        return {
            message: this.getLocalizedErrorMessage(error.message, language),
            data: null,
            automated: true,
            error: true,
            language: language,
            unlimited: true,
            recovery_attempted: true
        };
    }
};

ZaiqaAIAssistant.prototype.performUnlimitedFuzzyMatching = async function(query, language, context) {
    console.log('🔍 Performing unlimited fuzzy matching with very low threshold...');

    // Use very permissive threshold for unlimited mode
    const originalThreshold = this.fuzzyMatchingThreshold;
    this.fuzzyMatchingThreshold = 0.3; // Very permissive

    try {
        // Try all pattern sets including Roman Urdu
        const allPatterns = [
            ...Object.values(this.commandPatterns).flat(),
            ...Object.values(this.urduPatterns).flat(),
            ...Object.values(this.romanUrduPatterns).flat(),
            ...Object.values(this.mixedLanguagePatterns).flat()
        ];

        let bestMatch = null;
        let bestScore = 0;

        for (const pattern of allPatterns) {
            const score = this.calculateCommandSimilarity(query, pattern);
            if (score > bestScore && score > 0.3) {
                bestMatch = pattern;
                bestScore = score;
            }
        }

        if (bestMatch) {
            const extractedParams = this.intelligentParameterExtractionUnlimited(query, bestMatch, context);
            if (extractedParams) {
                return await this.executeUnlimitedCommand(bestMatch, [query, ...Object.values(extractedParams)], query, language, context);
            }
        }

        return null;

    } finally {
        this.fuzzyMatchingThreshold = originalThreshold;
    }
};

ZaiqaAIAssistant.prototype.intelligentParameterExtractionUnlimited = function(query, pattern, context) {
    const params = this.intelligentParameterExtraction(query, pattern);

    // Apply unlimited intelligence to fill missing parameters
    if (Object.keys(params).length < pattern.params.length) {
        // Use context and assumptions to fill gaps
        const assumptions = this.makeIntelligentAssumptions(query, context);

        pattern.params.forEach(paramName => {
            if (!params[paramName]) {
                // Try to extract from context
                if (paramName === 'customer' && context.lastCustomer) {
                    params[paramName] = context.lastCustomer;
                } else if (paramName === 'tableNumber' && context.lastTable) {
                    params[paramName] = context.lastTable;
                } else if (paramName === 'amount' && !params[paramName]) {
                    const estimated = this.estimateAmount(query, context);
                    if (estimated) params[paramName] = estimated.toString();
                } else if (paramName === 'staffName' && context.recentStaff) {
                    params[paramName] = context.recentStaff;
                }
            }
        });
    }

    return Object.keys(params).length > 0 ? params : null;
};

ZaiqaAIAssistant.prototype.processSingleCommandComplexOperation = async function(query, language, context) {
    console.log('🔗 Processing single command complex operation...');

    // Identify if this is a complex multi-step operation
    const complexPatterns = [
        {
            pattern: /process\s+table\s+(\d+)\s+order.*calculate.*bill.*update.*inventory/i,
            steps: ['processTableOrder', 'calculateBill', 'updateInventory', 'markTableComplete'],
            description: 'Complete table service workflow'
        },
        {
            pattern: /customer\s+(.+?)\s+ordered\s+(.+?)\s+for\s+(\d+)\s+people.*bill.*payment/i,
            steps: ['createCustomerOrder', 'calculateTotal', 'processPayment', 'updateSales'],
            description: 'Complete customer order workflow'
        },
        {
            pattern: /staff\s+(.+?)\s+worked.*pay.*salary.*update.*attendance/i,
            steps: ['markAttendance', 'calculateSalary', 'processPayment', 'updateRecords'],
            description: 'Complete staff management workflow'
        },
        {
            pattern: /generate.*report.*backup.*data.*send.*email/i,
            steps: ['generateReport', 'backupData', 'sendNotification'],
            description: 'Complete reporting and backup workflow'
        }
    ];

    for (const complexPattern of complexPatterns) {
        const match = query.match(complexPattern.pattern);
        if (match) {
            return await this.executeComplexWorkflow(complexPattern, match, query, language, context);
        }
    }

    return null;
};

ZaiqaAIAssistant.prototype.executeComplexWorkflow = async function(workflow, match, query, language, context) {
    console.log('🔄 Executing complex workflow:', workflow.description);

    const results = [];
    const errors = [];

    for (const step of workflow.steps) {
        try {
            const stepResult = await this.executeWorkflowStep(step, match, context);
            results.push(stepResult);
            console.log(`✅ Completed step: ${step}`);
        } catch (error) {
            console.error(`❌ Failed step: ${step}`, error);
            errors.push({ step, error: error.message });

            // Try to recover and continue
            const recovery = await this.recoverWorkflowStep(step, error, context);
            if (recovery) {
                results.push(recovery);
                console.log(`🔄 Recovered step: ${step}`);
            }
        }
    }

    return {
        message: `✅ Completed complex workflow: ${workflow.description}`,
        data: {
            workflow: workflow.description,
            steps_completed: results.length,
            total_steps: workflow.steps.length,
            results: results,
            errors: errors
        },
        automated: true,
        complex_operation: true,
        language: language,
        unlimited: true
    };
};

// Unlimited File System Operations
ZaiqaAIAssistant.prototype.createFileUnlimited = async function(filename, content, directory = '') {
    console.log('📁 Creating file with unlimited access:', filename);

    try {
        // In a real implementation, this would use Node.js fs module or similar
        // For browser environment, we'll use localStorage as a file system simulation
        const fullPath = directory ? `${directory}/${filename}` : filename;
        const fileData = {
            content: content,
            created: new Date().toISOString(),
            modified: new Date().toISOString(),
            size: content.length,
            type: this.getFileType(filename)
        };

        localStorage.setItem(`zaiqa_file_${fullPath}`, JSON.stringify(fileData));

        return {
            success: true,
            message: `File ${filename} created successfully`,
            path: fullPath,
            size: content.length
        };
    } catch (error) {
        console.error('File creation failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.editFileUnlimited = async function(filename, newContent, directory = '') {
    console.log('✏️ Editing file with unlimited access:', filename);

    try {
        const fullPath = directory ? `${directory}/${filename}` : filename;
        const existingData = localStorage.getItem(`zaiqa_file_${fullPath}`);

        if (!existingData) {
            throw new Error(`File ${filename} not found`);
        }

        const fileData = JSON.parse(existingData);
        fileData.content = newContent;
        fileData.modified = new Date().toISOString();
        fileData.size = newContent.length;

        localStorage.setItem(`zaiqa_file_${fullPath}`, JSON.stringify(fileData));

        return {
            success: true,
            message: `File ${filename} edited successfully`,
            path: fullPath,
            size: newContent.length
        };
    } catch (error) {
        console.error('File editing failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.deleteFileUnlimited = async function(filename, directory = '') {
    console.log('🗑️ Deleting file with unlimited access:', filename);

    try {
        const fullPath = directory ? `${directory}/${filename}` : filename;
        localStorage.removeItem(`zaiqa_file_${fullPath}`);

        return {
            success: true,
            message: `File ${filename} deleted successfully`,
            path: fullPath
        };
    } catch (error) {
        console.error('File deletion failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.readFileUnlimited = async function(filename, directory = '') {
    console.log('📖 Reading file with unlimited access:', filename);

    try {
        const fullPath = directory ? `${directory}/${filename}` : filename;
        const fileData = localStorage.getItem(`zaiqa_file_${fullPath}`);

        if (!fileData) {
            throw new Error(`File ${filename} not found`);
        }

        const parsed = JSON.parse(fileData);
        return {
            success: true,
            content: parsed.content,
            metadata: {
                created: parsed.created,
                modified: parsed.modified,
                size: parsed.size,
                type: parsed.type
            }
        };
    } catch (error) {
        console.error('File reading failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.getFileType = function(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const types = {
        'html': 'text/html',
        'css': 'text/css',
        'js': 'application/javascript',
        'json': 'application/json',
        'txt': 'text/plain',
        'md': 'text/markdown'
    };
    return types[extension] || 'application/octet-stream';
};

// Unlimited Database Operations
ZaiqaAIAssistant.prototype.createDataUnlimited = async function(table, data) {
    console.log('💾 Creating data with unlimited access:', table);

    try {
        const tableKey = `zaiqa_table_${table}`;
        const existingData = JSON.parse(localStorage.getItem(tableKey) || '[]');

        const newRecord = {
            id: this.generateUniqueId(),
            ...data,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        existingData.push(newRecord);
        localStorage.setItem(tableKey, JSON.stringify(existingData));

        return {
            success: true,
            message: `Record created in ${table}`,
            id: newRecord.id,
            data: newRecord
        };
    } catch (error) {
        console.error('Data creation failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.readDataUnlimited = async function(table, conditions = {}) {
    console.log('📊 Reading data with unlimited access:', table);

    try {
        const tableKey = `zaiqa_table_${table}`;
        const data = JSON.parse(localStorage.getItem(tableKey) || '[]');

        let filteredData = data;

        // Apply conditions
        if (Object.keys(conditions).length > 0) {
            filteredData = data.filter(record => {
                return Object.entries(conditions).every(([key, value]) => {
                    return record[key] === value;
                });
            });
        }

        return {
            success: true,
            data: filteredData,
            count: filteredData.length
        };
    } catch (error) {
        console.error('Data reading failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.updateDataUnlimited = async function(table, conditions, updates) {
    console.log('🔄 Updating data with unlimited access:', table);

    try {
        const tableKey = `zaiqa_table_${table}`;
        const data = JSON.parse(localStorage.getItem(tableKey) || '[]');

        let updatedCount = 0;
        const updatedData = data.map(record => {
            const matches = Object.entries(conditions).every(([key, value]) => {
                return record[key] === value;
            });

            if (matches) {
                updatedCount++;
                return {
                    ...record,
                    ...updates,
                    updated_at: new Date().toISOString()
                };
            }
            return record;
        });

        localStorage.setItem(tableKey, JSON.stringify(updatedData));

        return {
            success: true,
            message: `Updated ${updatedCount} records in ${table}`,
            updated_count: updatedCount
        };
    } catch (error) {
        console.error('Data update failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.deleteDataUnlimited = async function(table, conditions) {
    console.log('🗑️ Deleting data with unlimited access:', table);

    try {
        const tableKey = `zaiqa_table_${table}`;
        const data = JSON.parse(localStorage.getItem(tableKey) || '[]');

        const filteredData = data.filter(record => {
            return !Object.entries(conditions).every(([key, value]) => {
                return record[key] === value;
            });
        });

        const deletedCount = data.length - filteredData.length;
        localStorage.setItem(tableKey, JSON.stringify(filteredData));

        return {
            success: true,
            message: `Deleted ${deletedCount} records from ${table}`,
            deleted_count: deletedCount
        };
    } catch (error) {
        console.error('Data deletion failed:', error);
        return { success: false, error: error.message };
    }
};

ZaiqaAIAssistant.prototype.generateUniqueId = function() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Dynamic Page Creation
ZaiqaAIAssistant.prototype.createPageDynamically = async function(pageName, content, features = []) {
    console.log('🌐 Creating page dynamically:', pageName);

    const pageHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageName} - Zaiqa Restaurant</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container">
        <h1>${pageName}</h1>
        ${content}
        ${features.map(feature => this.generateFeatureHTML(feature)).join('')}
    </div>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/ai-assistant.js"></script>
</body>
</html>
    `;

    const result = await this.createFileUnlimited(`${pageName.toLowerCase().replace(/\s+/g, '-')}.html`, pageHTML);

    return {
        success: result.success,
        message: `Dynamic page '${pageName}' created successfully`,
        url: result.path,
        features: features
    };
};

ZaiqaAIAssistant.prototype.generateFeatureHTML = function(feature) {
    switch (feature.type) {
        case 'form':
            return this.generateFormHTML(feature);
        case 'table':
            return this.generateTableHTML(feature);
        case 'chart':
            return this.generateChartHTML(feature);
        default:
            return `<div class="feature">${feature.content || ''}</div>`;
    }
};

ZaiqaAIAssistant.prototype.processMultiLanguageQuery = async function(query, language) {
    if (language === 'en') {
        return query; // No processing needed for English
    }

    if (language === 'ur') {
        // Translate Urdu to English for processing
        return this.languageDetection.translateUrduToEnglish(query);
    }

    if (language === 'mixed') {
        // Handle mixed language
        let processed = query;

        // Translate Urdu parts to English
        processed = this.languageDetection.translateUrduToEnglish(processed);

        // Normalize mixed patterns
        processed = this.normalizeMixedLanguagePatterns(processed);

        return processed;
    }

    return query;
};

ZaiqaAIAssistant.prototype.normalizeMixedLanguagePatterns = function(query) {
    let normalized = query;

    // Common mixed patterns
    const mixedPatterns = {
        'کا order': 'order',
        'کی price': 'price',
        'میں add': 'add',
        'کو update': 'update',
        'سے remove': 'remove',
        'کے لیے': 'for',
        'کے ساتھ': 'with',
        'کی وجہ سے': 'because of'
    };

    Object.entries(mixedPatterns).forEach(([mixed, english]) => {
        const regex = new RegExp(mixed, 'gi');
        normalized = normalized.replace(regex, english);
    });

    return normalized;
};

ZaiqaAIAssistant.prototype.processUnrestrictedAutomationCommand = async function(query, language) {
    console.log('🚀 Processing unrestricted automation command:', query);

    // Remove all safety restrictions for command processing
    const originalSafetyMode = this.safetyRestrictions;
    this.safetyRestrictions = false;

    try {
        // Check all command patterns including multi-language
        let bestMatch = null;
        let bestScore = 0;
        let bestCategory = null;

        // Check English patterns
        const englishResult = await this.checkCommandPatterns(this.commandPatterns, query);
        if (englishResult.match) {
            return await this.executeUnrestrictedCommand(englishResult.pattern, englishResult.match, query, language);
        }

        // Check Urdu patterns
        if (language === 'ur' || language === 'mixed') {
            const urduResult = await this.checkCommandPatterns(this.urduPatterns, query);
            if (urduResult.match) {
                return await this.executeUnrestrictedCommand(urduResult.pattern, urduResult.match, query, language);
            }

            // Check mixed language patterns
            const mixedResult = await this.checkCommandPatterns(this.mixedLanguagePatterns, query);
            if (mixedResult.match) {
                return await this.executeUnrestrictedCommand(mixedResult.pattern, mixedResult.match, query, language);
            }
        }

        // Try fuzzy matching with unrestricted tolerance
        const fuzzyResult = await this.performUnrestrictedFuzzyMatching(query, language);
        if (fuzzyResult) {
            return fuzzyResult;
        }

        // If no pattern matches, try internet AI for interpretation
        if (this.internetIntegration) {
            const aiInterpretation = await this.getAICommandInterpretation(query, language);
            if (aiInterpretation) {
                return aiInterpretation;
            }
        }

        return null;

    } finally {
        // Restore original safety mode
        this.safetyRestrictions = originalSafetyMode;
    }
};

ZaiqaAIAssistant.prototype.checkCommandPatterns = async function(patterns, query) {
    for (const [category, categoryPatterns] of Object.entries(patterns)) {
        for (const pattern of categoryPatterns) {
            const match = query.match(pattern.pattern);
            if (match) {
                return { match, pattern, category };
            }
        }
    }
    return { match: null, pattern: null, category: null };
};

ZaiqaAIAssistant.prototype.executeUnrestrictedCommand = async function(pattern, match, query, language) {
    console.log('⚡ Executing unrestricted command:', pattern.action);

    // Extract parameters without restrictions
    const params = {};
    pattern.params.forEach((paramName, index) => {
        params[paramName] = match[index + 1];
    });

    try {
        // Execute with unrestricted permissions
        const result = await this.executeAutomation(pattern.action, params, query);
        return {
            message: result.message,
            data: result.data || null,
            automated: true,
            action: pattern.action,
            params: params,
            language: language,
            unrestricted: true
        };
    } catch (error) {
        console.error('Unrestricted command execution failed:', error);

        // Try to recover with AI assistance
        if (this.internetIntegration) {
            const recovery = await this.attemptAIRecovery(pattern.action, params, error, language);
            if (recovery) return recovery;
        }

        return {
            message: this.getLocalizedErrorMessage(error.message, language),
            data: null,
            automated: true,
            error: true,
            language: language
        };
    }
};

ZaiqaAIAssistant.prototype.performUnrestrictedFuzzyMatching = async function(query, language) {
    console.log('🔍 Performing unrestricted fuzzy matching...');

    // Lower the threshold for unrestricted mode
    const originalThreshold = this.fuzzyMatchingThreshold;
    this.fuzzyMatchingThreshold = 0.5; // More permissive

    try {
        // Try all pattern sets with fuzzy matching
        const allPatterns = [
            ...Object.values(this.commandPatterns).flat(),
            ...Object.values(this.urduPatterns).flat(),
            ...Object.values(this.mixedLanguagePatterns).flat()
        ];

        let bestMatch = null;
        let bestScore = 0;

        for (const pattern of allPatterns) {
            const score = this.calculateCommandSimilarity(query, pattern);
            if (score > bestScore && score > 0.5) {
                bestMatch = pattern;
                bestScore = score;
            }
        }

        if (bestMatch) {
            const extractedParams = this.intelligentParameterExtraction(query, bestMatch);
            if (extractedParams) {
                return await this.executeUnrestrictedCommand(bestMatch, [query, ...Object.values(extractedParams)], query, language);
            }
        }

        return null;

    } finally {
        this.fuzzyMatchingThreshold = originalThreshold;
    }
};

ZaiqaAIAssistant.prototype.getAICommandInterpretation = async function(query, language) {
    console.log('🤖 Getting AI command interpretation...');

    const interpretationPrompt = `
Analyze this restaurant management command and extract the action and parameters:

Command: "${query}"
Language: ${language}

Available actions:
- addSaleTransaction (customer, amount)
- processTableOrder (tableNumber, items, customerCount)
- addExpense (item, amount)
- updateMenuPrice (itemName, price)
- addStaffAdvance (staffName, amount)
- markStaffAttendance (staffName)
- checkSystemErrors ()
- generateReport ()
- backupData ()

Please respond with JSON format:
{
  "action": "actionName",
  "params": {"param1": "value1", "param2": "value2"},
  "confidence": 0.8,
  "interpretation": "explanation"
}
    `;

    try {
        const aiResponse = await this.queryInternetAI(interpretationPrompt, 'en');
        if (aiResponse) {
            const interpretation = JSON.parse(aiResponse);
            if (interpretation.confidence > 0.6) {
                const result = await this.executeAutomation(interpretation.action, interpretation.params, query);
                return {
                    message: result.message,
                    data: result.data,
                    automated: true,
                    action: interpretation.action,
                    params: interpretation.params,
                    language: language,
                    aiInterpreted: true,
                    confidence: interpretation.confidence
                };
            }
        }
    } catch (error) {
        console.error('AI interpretation failed:', error);
    }

    return null;
};

ZaiqaAIAssistant.prototype.attemptAIRecovery = async function(action, params, error, language) {
    console.log('🔄 Attempting AI-powered error recovery...');

    const recoveryPrompt = `
A restaurant management command failed. Help recover:

Action: ${action}
Parameters: ${JSON.stringify(params)}
Error: ${error.message}
Language: ${language}

Please suggest a corrected version or alternative approach.
Respond with JSON:
{
  "correctedAction": "actionName",
  "correctedParams": {"param1": "value1"},
  "explanation": "what was wrong and how it was fixed"
}
    `;

    try {
        const aiResponse = await this.queryInternetAI(recoveryPrompt, 'en');
        if (aiResponse) {
            const recovery = JSON.parse(aiResponse);
            const result = await this.executeAutomation(recovery.correctedAction, recovery.correctedParams, `Recovered: ${action}`);
            return {
                message: `✅ Recovered: ${result.message}`,
                data: result.data,
                automated: true,
                recovered: true,
                explanation: recovery.explanation,
                language: language
            };
        }
    } catch (recoveryError) {
        console.error('AI recovery failed:', recoveryError);
    }

    return null;
};

ZaiqaAIAssistant.prototype.getLocalizedErrorMessage = function(errorMessage, language) {
    const errorTranslations = {
        'not found': {
            'ur': 'نہیں ملا',
            'mixed': 'نہیں ملا / not found'
        },
        'invalid amount': {
            'ur': 'غلط رقم',
            'mixed': 'غلط رقم / invalid amount'
        },
        'insufficient stock': {
            'ur': 'ناکافی سٹاک',
            'mixed': 'ناکافی سٹاک / insufficient stock'
        },
        'already exists': {
            'ur': 'پہلے سے موجود ہے',
            'mixed': 'پہلے سے موجود ہے / already exists'
        }
    };

    if (language === 'en') return errorMessage;

    for (const [english, translations] of Object.entries(errorTranslations)) {
        if (errorMessage.toLowerCase().includes(english)) {
            return translations[language] || errorMessage;
        }
    }

    return errorMessage;
};

ZaiqaAIAssistant.prototype.enhanceMultiLanguageResponse = function(result, originalQuery, correctedQuery, language) {
    if (!this.detailedResponseMode) return result;

    // Enhance response with multi-language support
    const enhancedResult = {
        ...result,
        metadata: {
            originalQuery,
            correctedQuery,
            language,
            processingTime: new Date().toISOString(),
            aiStatus: this.isOnline ? 'online' : 'offline',
            errorCorrections: originalQuery !== correctedQuery,
            contextUsed: this.contextHistory.length > 1,
            unrestrictedMode: this.unrestrictedMode
        }
    };

    // Add language-specific corrections
    if (originalQuery !== correctedQuery) {
        enhancedResult.corrections = {
            applied: true,
            explanation: this.getLocalizedCorrectionExplanation(originalQuery, correctedQuery, language)
        };
    }

    // Add localized suggestions
    enhancedResult.suggestions = this.generateLocalizedSuggestions(correctedQuery, result, language);

    // Add step-by-step explanation in appropriate language
    if (result.automated) {
        enhancedResult.stepByStep = this.generateLocalizedStepByStep(result, language);
    }

    return enhancedResult;
};

ZaiqaAIAssistant.prototype.getLocalizedCorrectionExplanation = function(original, corrected, language) {
    const explanations = {
        'en': `I interpreted "${original}" as "${corrected}" to better understand your request.`,
        'ur': `میں نے "${original}" کو "${corrected}" کے طور پر سمجھا تاکہ آپ کی درخواست بہتر طریقے سے سمجھ سکوں۔`,
        'mixed': `میں نے "${original}" کو "${corrected}" کے طور پر interpret کیا۔`
    };

    return explanations[language] || explanations['en'];
};

ZaiqaAIAssistant.prototype.generateLocalizedSuggestions = function(query, result, language) {
    const suggestions = [];

    const suggestionTemplates = {
        'en': {
            sales: ["💡 You might also want to: 'Generate today's sales report'", "💡 Check: 'What are the top selling items?'"],
            expense: ["💡 Related actions: 'Generate expense report'", "💡 Check: 'What are this month's expenses?'"],
            staff: ["💡 You might need: 'Check staff attendance'", "💡 Review: 'Generate payroll report'"]
        },
        'ur': {
            sales: ["💡 آپ یہ بھی کر سکتے ہیں: 'آج کی فروخت کی رپورٹ بنائیں'", "💡 چیک کریں: 'سب سے زیادہ بکنے والے آئٹمز کیا ہیں؟'"],
            expense: ["💡 متعلقہ کام: 'خرچ کی رپورٹ بنائیں'", "💡 چیک کریں: 'اس مہینے کے خرچے کیا ہیں؟'"],
            staff: ["💡 آپ کو ضرورت ہو سکتی ہے: 'عملے کی حاضری چیک کریں'", "💡 دیکھیں: 'تنخواہ کی رپورٹ بنائیں'"]
        },
        'mixed': {
            sales: ["💡 آپ یہ بھی کر سکتے ہیں: 'Generate today's sales report'", "💡 Check کریں: 'Top selling items کیا ہیں؟'"],
            expense: ["💡 Related actions: 'Expense report بنائیں'", "💡 Check کریں: 'اس month کے expenses کیا ہیں؟'"],
            staff: ["💡 آپ کو need ہو سکتی ہے: 'Staff attendance check کریں'", "💡 Review کریں: 'Payroll report بنائیں'"]
        }
    };

    // Determine suggestion category
    let category = 'sales';
    if (query.includes('expense') || query.includes('خرچ') || query.includes('bought')) category = 'expense';
    if (query.includes('staff') || query.includes('عملہ') || query.includes('employee')) category = 'staff';

    const templates = suggestionTemplates[language] || suggestionTemplates['en'];
    return templates[category] || templates['sales'];
};

ZaiqaAIAssistant.prototype.generateLocalizedStepByStep = function(result, language) {
    const stepTemplates = {
        'en': {
            addSaleTransaction: [
                "1. 📝 Parsed customer and amount from your command",
                "2. 💰 Created order record with transaction details",
                "3. 💾 Saved to restaurant orders database",
                "4. 📊 Updated dashboard statistics",
                "5. ✅ Confirmed transaction completion"
            ]
        },
        'ur': {
            addSaleTransaction: [
                "1. 📝 آپ کے کمانڈ سے کسٹمر اور رقم نکالی",
                "2. 💰 لین دین کی تفصیلات کے ساتھ آرڈر ریکارڈ بنایا",
                "3. 💾 ریسٹورنٹ آرڈرز ڈیٹابیس میں محفوظ کیا",
                "4. 📊 ڈیش بورڈ کے اعداد و شمار اپ ڈیٹ کیے",
                "5. ✅ لین دین مکمل ہونے کی تصدیق کی"
            ]
        },
        'mixed': {
            addSaleTransaction: [
                "1. 📝 آپ کے command سے customer اور amount parse کیا",
                "2. 💰 Transaction details کے ساتھ order record بنایا",
                "3. 💾 Restaurant orders database میں save کیا",
                "4. 📊 Dashboard statistics update کیے",
                "5. ✅ Transaction completion confirm کیا"
            ]
        }
    };

    const templates = stepTemplates[language] || stepTemplates['en'];
    const actionSteps = templates[result.action] || templates['addSaleTransaction'];

    return actionSteps;
};

// Voice Recognition and API Configuration
ZaiqaAIAssistant.prototype.initializeVoiceRecognition = function() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.speechRecognition = new SpeechRecognition();

        this.speechRecognition.continuous = false;
        this.speechRecognition.interimResults = false;
        this.speechRecognition.lang = 'ur-PK'; // Default to Urdu (Pakistan)

        this.speechRecognition.onstart = () => {
            console.log('🎤 Voice recognition started');
            this.updateVoiceButton(true);
        };

        this.speechRecognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            console.log('🗣️ Voice input:', transcript);

            // Insert the transcript into chat input
            const input = document.getElementById('enhancedAIChatInput');
            if (input) {
                input.value = transcript;
                this.sendEnhancedChatMessage();
            }
        };

        this.speechRecognition.onerror = (event) => {
            console.error('Voice recognition error:', event.error);
            this.updateVoiceButton(false);
        };

        this.speechRecognition.onend = () => {
            console.log('🎤 Voice recognition ended');
            this.updateVoiceButton(false);
        };

        this.voiceRecognitionAvailable = true;
        console.log('✅ Voice recognition initialized');
    } else {
        console.log('❌ Voice recognition not supported');
        this.voiceRecognitionAvailable = false;
    }
};

ZaiqaAIAssistant.prototype.toggleVoiceRecognition = function() {
    if (!this.voiceRecognitionAvailable) {
        this.showNotification('Voice recognition is not supported in this browser', 'warning');
        return;
    }

    if (this.speechRecognition) {
        if (this.isListening) {
            this.speechRecognition.stop();
            this.isListening = false;
        } else {
            // Switch language based on recent context
            const recentLanguage = this.detectRecentLanguage();
            this.speechRecognition.lang = recentLanguage === 'ur' ? 'ur-PK' : 'en-US';

            this.speechRecognition.start();
            this.isListening = true;
        }
    }
};

ZaiqaAIAssistant.prototype.detectRecentLanguage = function() {
    if (this.contextHistory.length > 0) {
        const recent = this.contextHistory[this.contextHistory.length - 1];
        return recent.language || 'en';
    }
    return 'en';
};

ZaiqaAIAssistant.prototype.updateVoiceButton = function(isListening) {
    const voiceBtn = document.getElementById('voiceToggleBtn');
    if (voiceBtn) {
        if (isListening) {
            voiceBtn.classList.add('listening');
            voiceBtn.innerHTML = '<i class="fas fa-microphone-slash"></i>';
            voiceBtn.title = 'Stop Voice Recognition';
        } else {
            voiceBtn.classList.remove('listening');
            voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            voiceBtn.title = 'Start Voice Recognition';
        }
    }
};

ZaiqaAIAssistant.prototype.showAPIConfiguration = function() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content api-config-modal">
            <div class="modal-header">
                <h3><i class="fas fa-cog"></i> Internet AI Configuration</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="api-config-content">
                    <div class="config-section">
                        <h4>🌐 Internet AI Services</h4>
                        <p>Configure API keys for advanced AI capabilities with multi-language support.</p>

                        <div class="api-service-config">
                            <h5><i class="fab fa-openai"></i> OpenAI GPT</h5>
                            <div class="input-group">
                                <input type="password" id="openaiKey" placeholder="Enter OpenAI API Key"
                                       value="${this.internetAIServices.openai.apiKey || ''}" />
                                <button onclick="zaiqaAI.testAPIKey('openai')" class="test-btn">Test</button>
                            </div>
                            <small>Supports: English, Urdu, Mixed Language</small>
                        </div>

                        <div class="api-service-config">
                            <h5><i class="fas fa-brain"></i> Claude (Anthropic)</h5>
                            <div class="input-group">
                                <input type="password" id="claudeKey" placeholder="Enter Claude API Key"
                                       value="${this.internetAIServices.claude.apiKey || ''}" />
                                <button onclick="zaiqaAI.testAPIKey('claude')" class="test-btn">Test</button>
                            </div>
                            <small>Supports: English, Urdu, Mixed Language</small>
                        </div>

                        <div class="api-service-config">
                            <h5><i class="fab fa-google"></i> Google Gemini</h5>
                            <div class="input-group">
                                <input type="password" id="geminiKey" placeholder="Enter Gemini API Key (Get from ai.google.dev)"
                                       value="${this.internetAIServices.gemini.apiKey || ''}" />
                                <button onclick="zaiqaAI.testAPIKey('gemini')" class="test-btn">Test</button>
                            </div>
                            <small>Supports: English, Urdu, Mixed Language | <a href="https://ai.google.dev/" target="_blank">Get API Key</a></small>
                        </div>

                        <div class="api-service-config">
                            <h5><i class="fas fa-code"></i> Cohere</h5>
                            <div class="input-group">
                                <input type="password" id="cohereKey" placeholder="Enter Cohere API Key"
                                       value="${this.internetAIServices.cohere.apiKey || ''}" />
                                <button onclick="zaiqaAI.testAPIKey('cohere')" class="test-btn">Test</button>
                            </div>
                            <small>Supports: English, Urdu, Mixed Language</small>
                        </div>

                        <div class="api-service-config free-tier">
                            <h5><i class="fas fa-route"></i> OpenRouter <span class="free-badge">FREE</span></h5>
                            <div class="input-group">
                                <input type="password" id="openrouterKey" placeholder="Get free key from openrouter.ai"
                                       value="${this.internetAIServices.openrouter.apiKey || ''}" />
                                <button onclick="zaiqaAI.testAPIKey('openrouter')" class="test-btn">Test</button>
                            </div>
                            <div class="model-selector">
                                <label>Free Model:</label>
                                <select id="openrouterModel" onchange="zaiqaAI.updateOpenRouterModel(this.value)">
                                    ${this.internetAIServices.openrouter.freeModels.map(model =>
                                        `<option value="${model}" ${model === this.internetAIServices.openrouter.model ? 'selected' : ''}>${model}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <small>🆓 Free access to Llama, Mistral, Phi-3 | <a href="https://openrouter.ai/keys" target="_blank">Get Free API Key</a></small>
                        </div>
                    </div>

                    <div class="config-section">
                        <h4>⚙️ AI Preferences</h4>
                        <div class="preference-group">
                            <label>Preferred AI Service:</label>
                            <select id="preferredAI" onchange="zaiqaAI.updatePreferredAI(this.value)">
                                <option value="local" ${this.preferredAIService === 'local' ? 'selected' : ''}>Local AI Only</option>
                                <option value="openrouter" ${this.preferredAIService === 'openrouter' ? 'selected' : ''}>🆓 OpenRouter (Free)</option>
                                <option value="openai" ${this.preferredAIService === 'openai' ? 'selected' : ''}>OpenAI GPT</option>
                                <option value="claude" ${this.preferredAIService === 'claude' ? 'selected' : ''}>Claude</option>
                                <option value="gemini" ${this.preferredAIService === 'gemini' ? 'selected' : ''}>Google Gemini</option>
                                <option value="cohere" ${this.preferredAIService === 'cohere' ? 'selected' : ''}>Cohere</option>
                            </select>
                        </div>

                        <div class="preference-group">
                            <label>Default Language:</label>
                            <select id="defaultLanguage" onchange="zaiqaAI.updateDefaultLanguage(this.value)">
                                <option value="mixed" ${this.currentLanguage === 'mixed' ? 'selected' : ''}>Mixed (Urdu + English)</option>
                                <option value="ur" ${this.currentLanguage === 'ur' ? 'selected' : ''}>اردو (Urdu)</option>
                                <option value="en" ${this.currentLanguage === 'en' ? 'selected' : ''}>English</option>
                            </select>
                        </div>

                        <div class="preference-group">
                            <label>
                                <input type="checkbox" ${this.unrestrictedMode ? 'checked' : ''}
                                       onchange="zaiqaAI.toggleUnrestrictedMode(this.checked)" />
                                Unrestricted Command Processing
                            </label>
                            <small>Allows processing of any restaurant management command without limitations</small>
                        </div>
                    </div>

                    <div class="config-actions">
                        <button class="btn btn-primary" onclick="zaiqaAI.saveAPIConfiguration()">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                        <button class="btn btn-outline" onclick="zaiqaAI.resetAPIConfiguration()">
                            <i class="fas fa-undo"></i> Reset to Defaults
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
};

ZaiqaAIAssistant.prototype.testAPIKey = async function(service) {
    const keyInput = document.getElementById(`${service}Key`);
    const testBtn = keyInput.nextElementSibling;

    if (!keyInput.value.trim()) {
        this.showNotification('Please enter an API key first', 'warning');
        return;
    }

    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    testBtn.disabled = true;

    try {
        // Temporarily set the API key for testing
        const originalKey = this.internetAIServices[service].apiKey;
        this.internetAIServices[service].apiKey = keyInput.value.trim();

        console.log(`Testing ${service} API with key: ${keyInput.value.trim().substring(0, 10)}...`);

        const isWorking = await this.testAIService(service, this.internetAIServices[service]);

        if (isWorking) {
            testBtn.innerHTML = '<i class="fas fa-check"></i> Working';
            testBtn.className = 'test-btn success';
            this.showNotification(`${service.toUpperCase()} API key is valid and working`, 'success');
            console.log(`✅ ${service} API test successful`);
        } else {
            testBtn.innerHTML = '<i class="fas fa-times"></i> Failed';
            testBtn.className = 'test-btn error';
            this.showNotification(`${service.toUpperCase()} API key test failed - check your key and try again`, 'error');
            console.log(`❌ ${service} API test failed`);
            // Restore original key on failure
            this.internetAIServices[service].apiKey = originalKey;
        }
    } catch (error) {
        testBtn.innerHTML = '<i class="fas fa-times"></i> Error';
        testBtn.className = 'test-btn error';
        console.error(`${service} API test error:`, error);

        let errorMessage = `Error testing ${service.toUpperCase()}: `;
        if (error.message.includes('401')) {
            errorMessage += 'Invalid API key';
        } else if (error.message.includes('403')) {
            errorMessage += 'API key lacks required permissions';
        } else if (error.message.includes('429')) {
            errorMessage += 'Rate limit exceeded, try again later';
        } else if (error.message.includes('network')) {
            errorMessage += 'Network connection issue';
        } else {
            errorMessage += error.message;
        }

        this.showNotification(errorMessage, 'error');

        // Restore original key on error
        const originalKey = this.internetAIServices[service].apiKey;
        this.internetAIServices[service].apiKey = originalKey;
    } finally {
        testBtn.disabled = false;
        setTimeout(() => {
            testBtn.innerHTML = 'Test';
            testBtn.className = 'test-btn';
        }, 5000); // Show result for 5 seconds
    }
};

ZaiqaAIAssistant.prototype.saveAPIConfiguration = function() {
    // Save API keys
    const apiKeys = {};
    ['openai', 'claude', 'gemini', 'cohere', 'openrouter'].forEach(service => {
        const keyInput = document.getElementById(`${service}Key`);
        if (keyInput && keyInput.value.trim()) {
            apiKeys[service] = keyInput.value.trim();
            this.internetAIServices[service].apiKey = keyInput.value.trim();
        }
    });

    // Save OpenRouter model selection
    const openrouterModelSelect = document.getElementById('openrouterModel');
    if (openrouterModelSelect) {
        this.internetAIServices.openrouter.model = openrouterModelSelect.value;
    }

    // Save to localStorage
    localStorage.setItem('zaiqaAIKeys', JSON.stringify(apiKeys));

    // Save preferences including OpenRouter model
    const preferences = {
        preferredAIService: this.preferredAIService,
        currentLanguage: this.currentLanguage,
        unrestrictedMode: this.unrestrictedMode,
        openrouterModel: this.internetAIServices.openrouter.model
    };
    localStorage.setItem('zaiqaAIPreferences', JSON.stringify(preferences));

    this.showNotification('AI configuration saved successfully', 'success');

    // Close modal
    document.querySelector('.api-config-modal').closest('.modal-overlay').remove();
};

ZaiqaAIAssistant.prototype.updateOpenRouterModel = function(model) {
    this.internetAIServices.openrouter.model = model;
    console.log('Updated OpenRouter model:', model);
};

ZaiqaAIAssistant.prototype.updatePreferredAI = function(service) {
    this.preferredAIService = service;
    console.log('Updated preferred AI service:', service);
};

ZaiqaAIAssistant.prototype.updateDefaultLanguage = function(language) {
    this.currentLanguage = language;
    console.log('Updated default language:', language);
};

ZaiqaAIAssistant.prototype.toggleUnrestrictedMode = function(enabled) {
    this.unrestrictedMode = enabled;
    this.safetyRestrictions = !enabled;
    this.commandLimitations = !enabled;
    console.log('Unrestricted mode:', enabled ? 'ENABLED' : 'DISABLED');
};

ZaiqaAIAssistant.prototype.resetAPIConfiguration = function() {
    if (confirm('Are you sure you want to reset all API configuration? This will remove all saved API keys.')) {
        // Clear localStorage
        localStorage.removeItem('zaiqaAIKeys');
        localStorage.removeItem('zaiqaAIPreferences');

        // Reset in-memory configuration
        Object.keys(this.internetAIServices).forEach(service => {
            this.internetAIServices[service].apiKey = null;
        });

        this.preferredAIService = 'local';
        this.currentLanguage = 'mixed';
        this.unrestrictedMode = true;

        this.showNotification('API configuration reset to defaults', 'info');

        // Close and reopen modal to refresh
        document.querySelector('.api-config-modal').closest('.modal-overlay').remove();
        setTimeout(() => this.showAPIConfiguration(), 100);
    }
};

ZaiqaAIAssistant.prototype.showNotification = function(message, type) {
    if (window.app && window.app.showNotification) {
        window.app.showNotification(message, type);
    } else {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
};

ZaiqaAIAssistant.prototype.showUnrestrictedModeIndicator = function() {
    if (this.unrestrictedMode) {
        const indicator = document.createElement('div');
        indicator.id = 'unrestrictedModeIndicator';
        indicator.className = 'unrestricted-indicator';
        indicator.innerHTML = `
            <i class="fas fa-unlock"></i>
            UNRESTRICTED MODE
        `;
        indicator.title = 'AI Assistant is running in unrestricted mode with full command processing capabilities';

        document.body.appendChild(indicator);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (indicator.parentElement) {
                indicator.style.opacity = '0.7';
            }
        }, 10000);
    }
};

// Load saved preferences on initialization
ZaiqaAIAssistant.prototype.loadSavedPreferences = function() {
    try {
        const savedPreferences = localStorage.getItem('zaiqaAIPreferences');
        if (savedPreferences) {
            const preferences = JSON.parse(savedPreferences);
            this.preferredAIService = preferences.preferredAIService || 'local';
            this.currentLanguage = preferences.currentLanguage || 'mixed';
            this.unrestrictedMode = preferences.unrestrictedMode !== undefined ? preferences.unrestrictedMode : true;
            this.safetyRestrictions = !this.unrestrictedMode;
            this.commandLimitations = !this.unrestrictedMode;

            // Load OpenRouter model preference
            if (preferences.openrouterModel) {
                this.internetAIServices.openrouter.model = preferences.openrouterModel;
            }
        }
    } catch (error) {
        console.log('Failed to load saved preferences:', error);
    }
};

ZaiqaAIAssistant.prototype.applyErrorTolerance = function(query) {
    if (!this.errorTolerance) return query;

    let correctedQuery = query.toLowerCase().trim();

    // Fix common typos
    Object.entries(this.commonTypos).forEach(([typo, correction]) => {
        const regex = new RegExp(`\\b${typo}\\b`, 'gi');
        correctedQuery = correctedQuery.replace(regex, correction);
    });

    // Handle missing spaces (e.g., "karahi1400" -> "karahi 1400")
    correctedQuery = correctedQuery.replace(/([a-z])(\d)/g, '$1 $2');
    correctedQuery = correctedQuery.replace(/(\d)([a-z])/g, '$1 $2');

    // Handle multiple spaces
    correctedQuery = correctedQuery.replace(/\s+/g, ' ');

    // Apply fuzzy matching for key terms
    correctedQuery = this.applyFuzzyMatching(correctedQuery);

    return correctedQuery;
};

ZaiqaAIAssistant.prototype.applyFuzzyMatching = function(query) {
    const words = query.split(' ');
    const correctedWords = words.map(word => {
        // Skip numbers and very short words
        if (/^\d+$/.test(word) || word.length < 3) return word;

        // Check against synonyms
        for (const [key, synonyms] of Object.entries(this.synonyms)) {
            if (synonyms.some(synonym => this.calculateSimilarity(word, synonym) > this.fuzzyMatchingThreshold)) {
                return key;
            }
        }

        // Check against common restaurant terms
        const restaurantTerms = [
            'karahi', 'biryani', 'chicken', 'beef', 'mutton', 'rice', 'roti', 'naan',
            'pepsi', 'coke', 'water', 'table', 'customer', 'staff', 'expense',
            'inventory', 'stock', 'salary', 'wage', 'advance', 'report', 'backup'
        ];

        for (const term of restaurantTerms) {
            if (this.calculateSimilarity(word, term) > this.fuzzyMatchingThreshold) {
                return term;
            }
        }

        return word;
    });

    return correctedWords.join(' ');
};

ZaiqaAIAssistant.prototype.calculateSimilarity = function(str1, str2) {
    // Levenshtein distance-based similarity
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
};

ZaiqaAIAssistant.prototype.levenshteinDistance = function(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    return matrix[str2.length][str1.length];
};

ZaiqaAIAssistant.prototype.gatherEnhancedData = function() {
    const basicData = this.gatherCurrentData();

    // Add external context if available
    const enhancedData = {
        ...basicData,
        externalContext: {
            weather: this.weatherData,
            marketTrends: this.marketTrends,
            internetAvailable: this.internetIntegration,
            lastUpdated: new Date().toISOString()
        },
        systemStatus: {
            aiOnline: this.isOnline,
            monitoring: this.systemMonitoring,
            autoCorrection: this.autoCorrection,
            connectionAttempts: this.connectionRetryAttempts
        }
    };

    return enhancedData;
};

ZaiqaAIAssistant.prototype.enhanceResponse = function(result, originalQuery, correctedQuery) {
    if (!this.detailedResponseMode) return result;

    // Add detailed context to the response
    const enhancedResult = {
        ...result,
        metadata: {
            originalQuery,
            correctedQuery,
            processingTime: new Date().toISOString(),
            aiStatus: this.isOnline ? 'online' : 'offline',
            errorCorrections: originalQuery !== correctedQuery,
            contextUsed: this.contextHistory.length > 1
        }
    };

    // Add explanations for corrections if any were made
    if (originalQuery.toLowerCase() !== correctedQuery) {
        enhancedResult.corrections = {
            applied: true,
            explanation: `I interpreted "${originalQuery}" as "${correctedQuery}" to better understand your request.`
        };
    }

    // Add suggestions for related operations
    enhancedResult.suggestions = this.generateSuggestions(correctedQuery, result);

    // Add step-by-step explanation for automated actions
    if (result.automated) {
        enhancedResult.stepByStep = this.generateStepByStepExplanation(result);
    }

    return enhancedResult;
};

ZaiqaAIAssistant.prototype.generateSuggestions = function(query, result) {
    const suggestions = [];

    // Context-aware suggestions based on the query type
    if (query.includes('sale') || query.includes('customer')) {
        suggestions.push(
            "💡 You might also want to: 'Generate today's sales report'",
            "💡 Check: 'What are the top selling items?'",
            "💡 Monitor: 'Check table status'"
        );
    } else if (query.includes('expense') || query.includes('bought')) {
        suggestions.push(
            "💡 Related actions: 'Generate expense report'",
            "💡 Check: 'What are this month's expenses?'",
            "💡 Monitor: 'Check budget status'"
        );
    } else if (query.includes('staff') || query.includes('wage')) {
        suggestions.push(
            "💡 You might need: 'Check staff attendance'",
            "💡 Review: 'Generate payroll report'",
            "💡 Monitor: 'Check staff performance'"
        );
    } else if (query.includes('inventory') || query.includes('stock')) {
        suggestions.push(
            "💡 Consider: 'Check low stock items'",
            "💡 Plan: 'Generate purchase recommendations'",
            "💡 Monitor: 'Check inventory trends'"
        );
    }

    // Add system maintenance suggestions
    if (Math.random() > 0.7) { // Occasionally suggest maintenance
        suggestions.push("🔧 System tip: 'Backup data' for safety");
    }

    return suggestions.slice(0, 3); // Limit to 3 suggestions
};

ZaiqaAIAssistant.prototype.generateStepByStepExplanation = function(result) {
    const steps = [];

    if (result.action) {
        switch (result.action) {
            case 'addSaleTransaction':
                steps.push(
                    "1. 📝 Parsed customer and amount from your command",
                    "2. 💰 Created order record with transaction details",
                    "3. 💾 Saved to restaurant orders database",
                    "4. 📊 Updated dashboard statistics",
                    "5. ✅ Confirmed transaction completion"
                );
                break;
            case 'addExpense':
                steps.push(
                    "1. 📝 Identified expense description and amount",
                    "2. 🏷️ Auto-categorized the expense type",
                    "3. 💾 Added to expenses database",
                    "4. 📊 Updated financial calculations",
                    "5. ✅ Expense recorded successfully"
                );
                break;
            case 'updateMenuPrice':
                steps.push(
                    "1. 🔍 Located the menu item in database",
                    "2. 💰 Updated price from old to new value",
                    "3. 📝 Added update timestamp and AI signature",
                    "4. 💾 Saved changes to menu database",
                    "5. ✅ Price update completed"
                );
                break;
            default:
                steps.push(
                    "1. 📝 Processed your natural language command",
                    "2. 🤖 Applied AI automation logic",
                    "3. 💾 Updated relevant database records",
                    "4. 📊 Refreshed system statistics",
                    "5. ✅ Operation completed successfully"
                );
        }
    }

    return steps;
};

ZaiqaAIAssistant.prototype.processAutomationCommand = async function(query) {
    console.log('🤖 Processing automation command with enhanced matching:', query);

    let bestMatch = null;
    let bestScore = 0;
    let bestCategory = null;

    // Check all command patterns with fuzzy matching
    for (const [category, patterns] of Object.entries(this.commandPatterns)) {
        for (const pattern of patterns) {
            // Try exact match first
            const exactMatch = query.match(pattern.pattern);
            if (exactMatch) {
                console.log(`✅ Exact match found for ${category} command:`, pattern.action);
                return await this.executeMatchedCommand(pattern, exactMatch, query, category);
            }

            // Try fuzzy matching for partial matches
            const fuzzyScore = this.calculateCommandSimilarity(query, pattern);
            if (fuzzyScore > bestScore && fuzzyScore > 0.6) {
                bestMatch = pattern;
                bestScore = fuzzyScore;
                bestCategory = category;
            }
        }
    }

    // If we found a good fuzzy match, try to extract parameters intelligently
    if (bestMatch && bestScore > 0.6) {
        console.log(`🎯 Fuzzy match found (${Math.round(bestScore * 100)}%):`, bestMatch.action);

        try {
            const extractedParams = this.intelligentParameterExtraction(query, bestMatch);
            if (extractedParams) {
                // Ask for confirmation for fuzzy matches
                const confirmationResult = await this.requestFuzzyMatchConfirmation(
                    query, bestMatch, extractedParams, bestCategory, bestScore
                );
                return confirmationResult;
            }
        } catch (error) {
            console.log('Parameter extraction failed for fuzzy match:', error);
        }
    }

    // Try to understand the intent even if no pattern matches
    return await this.handleUnmatchedCommand(query);
};

ZaiqaAIAssistant.prototype.executeMatchedCommand = async function(pattern, match, query, category) {
    // Extract parameters
    const params = {};
    pattern.params.forEach((paramName, index) => {
        params[paramName] = match[index + 1];
    });

    // Execute the automation
    try {
        const result = await this.executeAutomation(pattern.action, params, query);
        return {
            message: result.message,
            data: result.data || null,
            automated: true,
            action: pattern.action,
            params: params,
            category: category,
            matchType: 'exact'
        };
    } catch (error) {
        console.error('Automation execution failed:', error);
        return {
            message: `Failed to execute command: ${error.message}. Please check your input and try again.`,
            data: null,
            automated: true,
            error: true,
            suggestions: this.generateErrorSuggestions(pattern.action, error)
        };
    }
};

ZaiqaAIAssistant.prototype.calculateCommandSimilarity = function(query, pattern) {
    // Extract key terms from the pattern
    const patternStr = pattern.pattern.toString();
    const keyTerms = this.extractKeyTermsFromPattern(patternStr);

    let totalScore = 0;
    let termCount = 0;

    keyTerms.forEach(term => {
        const similarity = this.findBestWordMatch(query, term);
        if (similarity > 0.5) {
            totalScore += similarity;
            termCount++;
        }
    });

    return termCount > 0 ? totalScore / keyTerms.length : 0;
};

ZaiqaAIAssistant.prototype.extractKeyTermsFromPattern = function(patternStr) {
    // Extract meaningful words from regex pattern
    const terms = [];

    // Common automation keywords
    const keywords = [
        'customer', 'bought', 'table', 'people', 'bill', 'done',
        'staff', 'member', 'took', 'advance', 'set', 'price',
        'add', 'menu', 'item', 'remove', 'inventory', 'worked',
        'pay', 'daily', 'wage', 'check', 'system', 'errors',
        'fix', 'backup', 'data', 'generate', 'report'
    ];

    keywords.forEach(keyword => {
        if (patternStr.includes(keyword)) {
            terms.push(keyword);
        }
    });

    return terms;
};

ZaiqaAIAssistant.prototype.findBestWordMatch = function(query, term) {
    const words = query.toLowerCase().split(/\s+/);
    let bestSimilarity = 0;

    words.forEach(word => {
        const similarity = this.calculateSimilarity(word, term);
        if (similarity > bestSimilarity) {
            bestSimilarity = similarity;
        }
    });

    return bestSimilarity;
};

ZaiqaAIAssistant.prototype.intelligentParameterExtraction = function(query, pattern) {
    const params = {};

    // Extract numbers (likely amounts, quantities, table numbers)
    const numbers = query.match(/\d+/g) || [];

    // Extract names (capitalized words or quoted strings)
    const names = query.match(/[A-Z][a-z]+|"[^"]+"|'[^']+'/g) || [];

    // Extract common items
    const items = this.extractKnownItems(query);

    // Map to pattern parameters based on action type
    switch (pattern.action) {
        case 'addSaleTransaction':
            if (names.length > 0) params.customer = names[0].replace(/['"]/g, '');
            if (numbers.length > 0) params.amount = numbers[numbers.length - 1]; // Last number is usually amount
            break;

        case 'processTableOrder':
            if (numbers.length > 0) params.tableNumber = numbers[0];
            if (numbers.length > 1) params.customerCount = numbers[1];
            if (items.length > 0) params.items = items.join(', ');
            break;

        case 'addExpense':
            if (items.length > 0) params.item = items[0];
            if (numbers.length > 0) params.amount = numbers[numbers.length - 1];
            break;

        case 'updateMenuPrice':
            if (items.length > 0) params.itemName = items[0];
            if (numbers.length > 0) params.price = numbers[numbers.length - 1];
            break;

        case 'addStaffAdvance':
            if (names.length > 0) params.staffName = names[0].replace(/['"]/g, '');
            if (numbers.length > 0) params.amount = numbers[numbers.length - 1];
            break;
    }

    // Check if we have enough parameters
    const requiredParams = pattern.params.length;
    const extractedParams = Object.keys(params).length;

    return extractedParams >= Math.ceil(requiredParams * 0.7) ? params : null;
};

ZaiqaAIAssistant.prototype.extractKnownItems = function(query) {
    const knownItems = [
        'karahi', 'biryani', 'chicken', 'beef', 'mutton', 'rice', 'dal',
        'roti', 'naan', 'pepsi', 'coke', 'water', 'tea', 'coffee',
        'soap', 'oil', 'onion', 'tomato', 'potato', 'milk'
    ];

    const foundItems = [];
    const queryLower = query.toLowerCase();

    knownItems.forEach(item => {
        if (queryLower.includes(item)) {
            foundItems.push(item);
        }
    });

    return foundItems;
};

ZaiqaAIAssistant.prototype.requestFuzzyMatchConfirmation = async function(query, pattern, params, category, score) {
    const confidence = Math.round(score * 100);

    return {
        message: `🤔 I think you want to ${pattern.action.replace(/([A-Z])/g, ' $1').toLowerCase()}. I'm ${confidence}% confident about this interpretation.`,
        data: {
            'Original Query': query,
            'Interpreted Action': pattern.action,
            'Extracted Parameters': params,
            'Confidence': `${confidence}%`,
            'Category': category
        },
        automated: false,
        requiresConfirmation: true,
        suggestedAction: {
            pattern: pattern,
            params: params,
            query: query
        },
        suggestions: [
            "💡 Say 'yes' or 'confirm' to proceed",
            "💡 Say 'no' to cancel and try rephrasing",
            "💡 Provide more specific details for better accuracy"
        ]
    };
};

ZaiqaAIAssistant.prototype.handleUnmatchedCommand = async function(query) {
    console.log('🔍 Attempting to understand unmatched command:', query);

    // Try to identify the general intent
    const intent = this.identifyGeneralIntent(query);

    if (intent) {
        return {
            message: `🤖 I understand you want to ${intent.description}, but I need more specific information.`,
            data: {
                'Detected Intent': intent.type,
                'What I Need': intent.requirements,
                'Examples': intent.examples
            },
            automated: false,
            suggestions: intent.examples.map(ex => `💡 Try: "${ex}"`)
        };
    }

    // If we can't understand at all, provide helpful guidance
    return {
        message: "🤔 I'm not sure what you want me to do. Let me help you with some examples of what I can understand:",
        data: {
            'Sales Commands': [
                '"Customer John bought items worth 1500"',
                '"Table 4: 1 karahi, 3 people, bill done"'
            ],
            'Expense Commands': [
                '"Bought soap for 100 rupees"',
                '"Staff member Ali took 500 advance"'
            ],
            'Menu Commands': [
                '"Set karahi price to 1400"',
                '"Add menu item biryani for 800"'
            ],
            'System Commands': [
                '"Check system errors"',
                '"Generate report"',
                '"Backup data"'
            ]
        },
        automated: false,
        suggestions: [
            "💡 Try using specific amounts and names",
            "💡 Include action words like 'add', 'set', 'check'",
            "💡 Be specific about what you want to do"
        ]
    };
};

ZaiqaAIAssistant.prototype.identifyGeneralIntent = function(query) {
    const intents = [
        {
            type: 'sales',
            keywords: ['sale', 'customer', 'buy', 'sold', 'order', 'table'],
            description: 'record a sale or order',
            requirements: 'customer name or table number and amount',
            examples: [
                'Customer Ali bought items worth 1200',
                'Table 3: 2 karahi, 4 people, bill done'
            ]
        },
        {
            type: 'expense',
            keywords: ['bought', 'expense', 'paid', 'cost', 'purchase'],
            description: 'record an expense',
            requirements: 'what was bought and how much it cost',
            examples: [
                'Bought vegetables for 300 rupees',
                'Paid 500 for electricity bill'
            ]
        },
        {
            type: 'staff',
            keywords: ['staff', 'employee', 'worker', 'salary', 'wage', 'advance'],
            description: 'manage staff information',
            requirements: 'staff member name and action',
            examples: [
                'Ali worked today',
                'Pay Hassan daily wage',
                'Staff member Sara took 800 advance'
            ]
        },
        {
            type: 'menu',
            keywords: ['menu', 'price', 'item', 'dish', 'food'],
            description: 'manage menu items and pricing',
            requirements: 'item name and price or action',
            examples: [
                'Set biryani price to 900',
                'Add menu item dal for 250'
            ]
        },
        {
            type: 'inventory',
            keywords: ['inventory', 'stock', 'add', 'use', 'check'],
            description: 'manage inventory',
            requirements: 'item name and quantity',
            examples: [
                'Add 10 kg chicken to inventory',
                'Check rice stock'
            ]
        },
        {
            type: 'system',
            keywords: ['system', 'error', 'check', 'fix', 'report', 'backup'],
            description: 'perform system operations',
            requirements: 'specific system action',
            examples: [
                'Check system errors',
                'Generate report',
                'Backup data'
            ]
        }
    ];

    const queryLower = query.toLowerCase();

    for (const intent of intents) {
        const matchCount = intent.keywords.filter(keyword =>
            queryLower.includes(keyword)
        ).length;

        if (matchCount > 0) {
            return intent;
        }
    }

    return null;
};

ZaiqaAIAssistant.prototype.generateErrorSuggestions = function(action, error) {
    const suggestions = [];

    if (error.message.includes('not found')) {
        suggestions.push(
            "💡 Check spelling of names and items",
            "💡 Make sure the item/person exists in the system",
            "💡 Try using the exact name as stored"
        );
    } else if (error.message.includes('invalid') || error.message.includes('amount')) {
        suggestions.push(
            "💡 Make sure to include a valid number",
            "💡 Use only digits for amounts (e.g., 1500, not fifteen hundred)",
            "💡 Check that the amount is positive"
        );
    } else {
        suggestions.push(
            "💡 Try rephrasing your command",
            "💡 Include all required information",
            "💡 Check the examples for proper format"
        );
    }

    return suggestions;
};

ZaiqaAIAssistant.prototype.executeAutomation = async function(action, params, originalQuery) {
    console.log(`🔄 Executing automation: ${action}`, params);

    // Add to pending actions
    const actionId = Date.now().toString();
    this.pendingActions.push({
        id: actionId,
        action,
        params,
        query: originalQuery,
        timestamp: new Date().toISOString()
    });

    try {
        let result;

        // Execute based on action type
        switch (action) {
            case 'addSaleTransaction':
                result = await this.automateAddSale(params);
                break;
            case 'processTableOrder':
                result = await this.automateTableOrder(params);
                break;
            case 'addQuickSale':
                result = await this.automateQuickSale(params);
                break;
            case 'addExpense':
                result = await this.automateAddExpense(params);
                break;
            case 'addStaffAdvance':
                result = await this.automateStaffAdvance(params);
                break;
            case 'updateMenuPrice':
                result = await this.automateUpdateMenuPrice(params);
                break;
            case 'addMenuItem':
                result = await this.automateAddMenuItem(params);
                break;
            case 'removeMenuItem':
                result = await this.automateRemoveMenuItem(params);
                break;
            case 'addInventoryItem':
                result = await this.automateAddInventory(params);
                break;
            case 'useInventoryItem':
                result = await this.automateUseInventory(params);
                break;
            case 'checkInventoryStock':
                result = await this.automateCheckStock(params);
                break;
            case 'markStaffAttendance':
                result = await this.automateStaffAttendance(params);
                break;
            case 'payStaffDaily':
                result = await this.automateStaffPayment(params);
                break;
            case 'addStaffMember':
                result = await this.automateAddStaff(params);
                break;
            case 'checkSystemErrors':
                result = await this.automateErrorCheck(params);
                break;
            case 'autoFixErrors':
                result = await this.automateErrorFix(params);
                break;
            case 'backupSystemData':
                result = await this.automateBackup(params);
                break;
            case 'generateComprehensiveReport':
                result = await this.automateGenerateReport(params);
                break;
            default:
                throw new Error(`Unknown automation action: ${action}`);
        }

        // Move from pending to executed
        this.pendingActions = this.pendingActions.filter(a => a.id !== actionId);
        this.executedActions.push({
            id: actionId,
            action,
            params,
            query: originalQuery,
            result,
            timestamp: new Date().toISOString(),
            success: true
        });

        // Show notification to user
        this.showAutomationNotification(action, params, result);

        return result;

    } catch (error) {
        // Move from pending to executed with error
        this.pendingActions = this.pendingActions.filter(a => a.id !== actionId);
        this.executedActions.push({
            id: actionId,
            action,
            params,
            query: originalQuery,
            error: error.message,
            timestamp: new Date().toISOString(),
            success: false
        });

        throw error;
    }
};

ZaiqaAIAssistant.prototype.classifyIntent = function(query) {
    for (const [intent, patterns] of Object.entries(this.nlpPatterns)) {
        if (patterns.some(pattern => pattern.test(query))) {
            return intent;
        }
    }
    return 'general';
};

ZaiqaAIAssistant.prototype.handleSalesQuery = async function(query, data) {
    const today = new Date().toDateString();
    const todayOrders = data.orders.filter(order =>
        new Date(order.created_at).toDateString() === today
    );

    const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
    const avgOrderValue = todayOrders.length > 0 ? todayRevenue / todayOrders.length : 0;

    // Get top selling items
    const itemSales = {};
    todayOrders.forEach(order => {
        order.items?.forEach(item => {
            itemSales[item.name] = (itemSales[item.name] || 0) + item.quantity;
        });
    });

    const topItems = Object.entries(itemSales)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([name, quantity]) => ({ name, quantity }));

    if (query.toLowerCase().includes('today')) {
        return {
            message: `Today's sales performance: PKR ${todayRevenue.toLocaleString()} revenue from ${todayOrders.length} orders. Average order value is PKR ${Math.round(avgOrderValue)}.`,
            data: {
                'Revenue': `PKR ${todayRevenue.toLocaleString()}`,
                'Orders': todayOrders.length,
                'Average Order': `PKR ${Math.round(avgOrderValue)}`,
                'Top Item': topItems[0]?.name || 'None'
            }
        };
    }

    if (query.toLowerCase().includes('best') || query.toLowerCase().includes('top')) {
        return {
            message: `Here are today's top-selling items:`,
            data: topItems.map(item => ({
                name: item.name,
                value: `${item.quantity} sold`
            }))
        };
    }

    return {
        message: `Sales analysis: ${todayOrders.length} orders today generating PKR ${todayRevenue.toLocaleString()} in revenue.`,
        data: { 'Revenue': `PKR ${todayRevenue.toLocaleString()}`, 'Orders': todayOrders.length }
    };
};

ZaiqaAIAssistant.prototype.handleInventoryQuery = async function(query, data) {
    const lowStockItems = data.inventory.filter(item =>
        item.currentQuantity <= (item.minimumQuantity || 10)
    );

    const criticalItems = data.inventory.filter(item =>
        item.currentQuantity <= (item.minimumQuantity || 10) * 0.5
    );

    if (query.toLowerCase().includes('low') || query.toLowerCase().includes('restock')) {
        if (lowStockItems.length === 0) {
            return {
                message: "Great news! All inventory items are well-stocked. No immediate restocking needed.",
                data: { 'Status': 'All items in stock' }
            };
        }

        return {
            message: `${lowStockItems.length} items need restocking. ${criticalItems.length} are critically low.`,
            data: lowStockItems.slice(0, 10).map(item => ({
                name: item.name,
                value: `${item.currentQuantity} remaining`,
                description: item.currentQuantity <= (item.minimumQuantity || 10) * 0.5 ? 'Critical' : 'Low'
            }))
        };
    }

    if (query.toLowerCase().includes('how much') || query.toLowerCase().includes('remaining')) {
        const totalItems = data.inventory.length;
        const totalValue = data.inventory.reduce((sum, item) =>
            sum + (item.currentQuantity * (item.costPerUnit || 0)), 0
        );

        return {
            message: `Inventory overview: ${totalItems} items tracked with total value of PKR ${totalValue.toLocaleString()}.`,
            data: {
                'Total Items': totalItems,
                'Total Value': `PKR ${totalValue.toLocaleString()}`,
                'Low Stock': lowStockItems.length,
                'Critical': criticalItems.length
            }
        };
    }

    return {
        message: `Inventory status: ${data.inventory.length} items tracked, ${lowStockItems.length} need attention.`,
        data: { 'Total Items': data.inventory.length, 'Need Restocking': lowStockItems.length }
    };
};

ZaiqaAIAssistant.prototype.handleStaffQuery = async function(query, data) {
    const activeStaff = data.staff.filter(member => member.isActive !== false);
    const totalSalaries = activeStaff.reduce((sum, member) => sum + (member.monthlySalary || 0), 0);
    const totalDehari = activeStaff.reduce((sum, member) => sum + (member.dehariBalance || 0), 0);

    if (query.toLowerCase().includes('how many') || query.toLowerCase().includes('working')) {
        return {
            message: `Currently ${activeStaff.length} staff members are active.`,
            data: activeStaff.slice(0, 8).map(member => ({
                name: member.name,
                value: member.position,
                description: `Salary: PKR ${(member.monthlySalary || 0).toLocaleString()}`
            }))
        };
    }

    if (query.toLowerCase().includes('payroll') || query.toLowerCase().includes('salary')) {
        return {
            message: `Staff payroll overview: PKR ${totalSalaries.toLocaleString()} monthly salaries, PKR ${totalDehari.toLocaleString()} dehari balance.`,
            data: {
                'Active Staff': activeStaff.length,
                'Monthly Salaries': `PKR ${totalSalaries.toLocaleString()}`,
                'Dehari Balance': `PKR ${totalDehari.toLocaleString()}`,
                'Average Salary': `PKR ${Math.round(totalSalaries / Math.max(activeStaff.length, 1)).toLocaleString()}`
            }
        };
    }

    return {
        message: `Staff overview: ${activeStaff.length} active members with total monthly cost of PKR ${totalSalaries.toLocaleString()}.`,
        data: { 'Active Staff': activeStaff.length, 'Monthly Cost': `PKR ${totalSalaries.toLocaleString()}` }
    };
};

ZaiqaAIAssistant.prototype.handlePredictionsQuery = async function(query, data) {
    const predictions = this.generatePredictions(data);

    if (query.toLowerCase().includes('tomorrow')) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);

        const salesPrediction = this.predictionModels.sales.predictDailySales(tomorrow);
        const staffingPrediction = this.predictionModels.staffing.predictStaffingNeeds(
            new Date().getHours(), tomorrow.getDay()
        );

        return {
            message: `Tomorrow's predictions: Expected revenue PKR ${Math.round(salesPrediction.prediction).toLocaleString()}, recommended ${staffingPrediction.recommended} staff members.`,
            data: {
                'Expected Revenue': `PKR ${Math.round(salesPrediction.prediction).toLocaleString()}`,
                'Confidence': `${Math.round(salesPrediction.confidence * 100)}%`,
                'Recommended Staff': staffingPrediction.recommended,
                'Peak Hours': '12:00-14:00, 19:00-21:00'
            }
        };
    }

    if (query.toLowerCase().includes('inventory') || query.toLowerCase().includes('stock')) {
        const inventoryPredictions = data.inventory.slice(0, 5).map(item => {
            const prediction = this.predictionModels.inventory.predictUsage(item, 7);
            return {
                name: item.name,
                value: `${Math.round(prediction.prediction)} units needed`,
                description: `${Math.round(prediction.dailyAverage)} per day average`
            };
        });

        return {
            message: "Here are inventory predictions for the next 7 days:",
            data: inventoryPredictions
        };
    }

    return {
        message: "I can predict sales trends, inventory needs, and staffing requirements. What would you like me to forecast?",
        data: {
            'Available Predictions': 'Sales, Inventory, Staffing',
            'Prediction Period': 'Up to 30 days',
            'Accuracy': 'Improves with more data'
        }
    };
};

ZaiqaAIAssistant.prototype.handleMenuQuery = async function(query, data) {
    const menuAnalysis = this.getMenuItemAnalysis(data);

    if (query.toLowerCase().includes('performance') || query.toLowerCase().includes('popular')) {
        const topPerformers = menuAnalysis
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 5);

        return {
            message: "Top performing menu items by revenue:",
            data: topPerformers.map(item => ({
                name: item.name,
                value: `PKR ${item.revenue.toLocaleString()}`,
                description: `${item.orderCount} orders, ${Math.round(item.profitMargin)}% margin`
            }))
        };
    }

    if (query.toLowerCase().includes('profit') || query.toLowerCase().includes('margin')) {
        const profitableItems = menuAnalysis
            .filter(item => item.orderCount > 0)
            .sort((a, b) => b.profitMargin - a.profitMargin)
            .slice(0, 5);

        return {
            message: "Most profitable menu items:",
            data: profitableItems.map(item => ({
                name: item.name,
                value: `${Math.round(item.profitMargin)}% margin`,
                description: `PKR ${item.revenue.toLocaleString()} revenue`
            }))
        };
    }

    const totalRevenue = menuAnalysis.reduce((sum, item) => sum + item.revenue, 0);
    const avgMargin = menuAnalysis.reduce((sum, item) => sum + item.profitMargin, 0) / menuAnalysis.length;

    return {
        message: `Menu analysis: ${menuAnalysis.length} items generating PKR ${totalRevenue.toLocaleString()} with ${Math.round(avgMargin)}% average margin.`,
        data: {
            'Total Items': menuAnalysis.length,
            'Total Revenue': `PKR ${totalRevenue.toLocaleString()}`,
            'Average Margin': `${Math.round(avgMargin)}%`,
            'Top Item': menuAnalysis[0]?.name || 'None'
        }
    };
};

ZaiqaAIAssistant.prototype.handleGeneralQuery = async function(query, data) {
    // If AI is available, use it for general queries
    if (this.isOnline) {
        try {
            const aiResponse = await this.queryLocalAI(`
                You are Zaiqa AI Assistant for a restaurant management system.
                Answer this question about restaurant operations: "${query}"

                Current restaurant data summary:
                - Orders today: ${data.orders.filter(o => new Date(o.created_at).toDateString() === new Date().toDateString()).length}
                - Active staff: ${data.staff.filter(s => s.isActive !== false).length}
                - Inventory items: ${data.inventory.length}
                - Tables: ${data.tables.length}

                Provide a helpful, concise response.
            `);

            return {
                message: aiResponse,
                data: null
            };
        } catch (error) {
            console.error('AI query error:', error);
        }
    }

    // Fallback responses
    const fallbackResponses = [
        "I can help you analyze sales, inventory, staff, and menu performance. What would you like to know?",
        "Try asking me about today's sales, inventory status, or staff information.",
        "I can provide insights on revenue trends, popular items, and operational efficiency.",
        "Ask me to predict future sales, check inventory levels, or analyze menu performance."
    ];

    return {
        message: fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)],
        data: {
            'Available Commands': 'Sales, Inventory, Staff, Menu, Predictions',
            'Example': 'What are today\'s sales?',
            'AI Status': this.isOnline ? 'Online' : 'Offline'
        }
    };
};

// Utility and Analysis Methods
ZaiqaAIAssistant.prototype.showTypingIndicator = function() {
    const messagesContainer = document.getElementById('aiChatMessages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'ai-message typing-indicator';
    typingDiv.id = 'typingIndicator';
    typingDiv.innerHTML = `
        <div class="ai-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="ai-message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    messagesContainer.appendChild(typingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
};

ZaiqaAIAssistant.prototype.hideTypingIndicator = function() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
};

ZaiqaAIAssistant.prototype.quickAnalysis = async function(type) {
    const queries = {
        sales: "What are today's sales trends?",
        inventory: "Which items need restocking?",
        predictions: "What are tomorrow's predictions?"
    };

    const query = queries[type];
    if (query) {
        document.getElementById('aiChatInput').value = query;
        await this.sendChatMessage();
    }
};

ZaiqaAIAssistant.prototype.updateInsightsDisplay = function(insights, anomalies = []) {
    const insightsList = document.getElementById('aiInsightsList');
    if (!insightsList) return;

    if (insights.length === 0 && anomalies.length === 0) {
        insightsList.innerHTML = `
            <div class="ai-no-insights">
                <i class="fas fa-check-circle"></i>
                <p>All systems running smoothly. No immediate insights to report.</p>
            </div>
        `;
        return;
    }

    const allInsights = [...insights, ...anomalies];
    insightsList.innerHTML = allInsights.map(insight => `
        <div class="ai-insight-item ${insight.level} priority-${insight.priority}">
            <div class="insight-header">
                <div class="insight-icon">
                    <i class="fas ${this.getInsightIcon(insight.type)}"></i>
                </div>
                <div class="insight-title">
                    <h5>${insight.title}</h5>
                    <span class="insight-type">${insight.type}</span>
                </div>
                <div class="insight-priority">
                    <span class="priority-badge ${insight.priority}">${insight.priority}</span>
                </div>
            </div>
            <div class="insight-content">
                <p>${insight.message}</p>
                ${insight.action ? `<div class="insight-action"><strong>Action:</strong> ${insight.action}</div>` : ''}
                ${insight.data ? this.formatInsightData(insight.data) : ''}
            </div>
        </div>
    `).join('');
};

ZaiqaAIAssistant.prototype.getInsightIcon = function(type) {
    const icons = {
        revenue: 'fa-chart-line',
        inventory: 'fa-boxes',
        menu: 'fa-utensils',
        staffing: 'fa-users',
        customer: 'fa-user-friends',
        ai: 'fa-brain',
        anomaly: 'fa-exclamation-triangle'
    };
    return icons[type] || 'fa-info-circle';
};

ZaiqaAIAssistant.prototype.formatInsightData = function(data) {
    if (!data || typeof data !== 'object') return '';

    if (Array.isArray(data)) {
        return `
            <div class="insight-data-list">
                ${data.slice(0, 5).map(item => `
                    <div class="data-item">
                        <span class="item-name">${item.name}</span>
                        <span class="item-value">${item.value || item.daysRemaining || item.suggested || ''}</span>
                    </div>
                `).join('')}
                ${data.length > 5 ? `<div class="data-more">+${data.length - 5} more items</div>` : ''}
            </div>
        `;
    }

    return `
        <div class="insight-data-object">
            ${Object.entries(data).slice(0, 4).map(([key, value]) => `
                <div class="data-row">
                    <span class="data-key">${key}:</span>
                    <span class="data-value">${value}</span>
                </div>
            `).join('')}
        </div>
    `;
};

ZaiqaAIAssistant.prototype.refreshInsights = async function() {
    const insightsList = document.getElementById('aiInsightsList');
    if (insightsList) {
        insightsList.innerHTML = `
            <div class="ai-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Refreshing insights...</span>
            </div>
        `;
    }

    await this.performBackgroundAnalysis();
};

ZaiqaAIAssistant.prototype.showAIConfig = function() {
    // This will be called when settings page loads
    console.log('AI Config requested');
};

ZaiqaAIAssistant.prototype.saveAIConfig = function() {
    const config = {
        endpoint: document.getElementById('aiEndpoint')?.value || this.localModelEndpoint,
        model: document.getElementById('aiModel')?.value || this.currentModel,
        analysisFreq: parseInt(document.getElementById('aiAnalysisFreq')?.value || '5'),
        autoInsights: document.getElementById('aiAutoInsights')?.checked !== false,
        predictions: document.getElementById('aiPredictions')?.checked !== false,
        learning: document.getElementById('aiLearning')?.checked !== false
    };

    localStorage.setItem('zaiqaAIConfig', JSON.stringify(config));
    this.applyConfig(config);

    if (window.app && window.app.showNotification) {
        window.app.showNotification('AI configuration saved successfully!', 'success');
    }
};

ZaiqaAIAssistant.prototype.applyConfig = function(config) {
    this.localModelEndpoint = config.endpoint;
    this.currentModel = config.model;

    // Restart background analysis with new frequency
    if (this.analysisInterval) {
        clearInterval(this.analysisInterval);
    }

    if (config.autoInsights) {
        this.analysisInterval = setInterval(() => {
            this.performBackgroundAnalysis();
        }, config.analysisFreq * 60 * 1000);
    }
};

ZaiqaAIAssistant.prototype.testAIConnection = async function() {
    try {
        await this.detectLocalAI();
        const status = this.isOnline ? 'Connected successfully!' : 'No AI server detected';
        const type = this.isOnline ? 'success' : 'warning';

        if (window.app && window.app.showNotification) {
            window.app.showNotification(status, type);
        }
    } catch (error) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification('Connection test failed', 'error');
        }
    }
};

ZaiqaAIAssistant.prototype.resetLearningData = function() {
    if (confirm('Are you sure you want to reset all learning data? This will remove all patterns and predictions.')) {
        this.learningData = {};
        localStorage.removeItem('zaiqaAILearningData');
        this.initializeLearningSystem();

        if (window.app && window.app.showNotification) {
            window.app.showNotification('Learning data reset successfully!', 'success');
        }
    }
};

ZaiqaAIAssistant.prototype.loadLearningData = function() {
    const saved = localStorage.getItem('zaiqaAILearningData');
    return saved ? JSON.parse(saved) : {};
};

ZaiqaAIAssistant.prototype.updateLearningData = function(currentData) {
    // Update patterns with new data
    const today = new Date().toDateString();
    const hour = new Date().getHours();
    const dayOfWeek = new Date().getDay();

    // Sales patterns
    if (!this.learningData.salesPatterns) this.learningData.salesPatterns = {};
    if (!this.learningData.salesPatterns[today]) this.learningData.salesPatterns[today] = [];

    const todayRevenue = currentData.orders
        .filter(order => new Date(order.created_at).toDateString() === today)
        .reduce((sum, order) => sum + (order.total_amount || 0), 0);

    this.learningData.salesPatterns[today].push({
        hour,
        revenue: todayRevenue,
        orderCount: currentData.orders.length
    });

    // Inventory usage patterns
    if (!this.learningData.inventoryUsage) this.learningData.inventoryUsage = {};
    currentData.inventory.forEach(item => {
        if (!this.learningData.inventoryUsage[item.id]) {
            this.learningData.inventoryUsage[item.id] = [];
        }

        this.learningData.inventoryUsage[item.id].push({
            date: today,
            quantity: item.currentQuantity,
            usage: item.lastUsage || 0
        });

        // Keep only last 30 days
        if (this.learningData.inventoryUsage[item.id].length > 30) {
            this.learningData.inventoryUsage[item.id] = this.learningData.inventoryUsage[item.id].slice(-30);
        }
    });

    // Save learning data
    localStorage.setItem('zaiqaAILearningData', JSON.stringify(this.learningData));
};

ZaiqaAIAssistant.prototype.analyzeSalesPatterns = function() {
    const orders = JSON.parse(localStorage.getItem('orders') || '[]');
    const patterns = {
        dailySales: {},
        hourlySales: {},
        itemPopularity: {}
    };

    orders.forEach(order => {
        const date = new Date(order.created_at);
        const dayOfWeek = date.getDay();
        const hour = date.getHours();

        // Daily patterns
        if (!patterns.dailySales[dayOfWeek]) patterns.dailySales[dayOfWeek] = [];
        patterns.dailySales[dayOfWeek].push(order.total_amount || 0);

        // Hourly patterns
        if (!patterns.hourlySales[hour]) patterns.hourlySales[hour] = [];
        patterns.hourlySales[hour].push(order.total_amount || 0);

        // Item popularity
        order.items?.forEach(item => {
            if (!patterns.itemPopularity[item.name]) patterns.itemPopularity[item.name] = 0;
            patterns.itemPopularity[item.name] += item.quantity;
        });
    });

    return patterns;
};

ZaiqaAIAssistant.prototype.analyzeInventoryPatterns = function() {
    const inventory = JSON.parse(localStorage.getItem('inventoryItems') || '[]');
    const patterns = {
        itemUsage: {},
        restockFrequency: {},
        seasonalTrends: {}
    };

    // Use learning data if available
    if (this.learningData.inventoryUsage) {
        Object.entries(this.learningData.inventoryUsage).forEach(([itemId, usage]) => {
            patterns.itemUsage[itemId] = usage.map(u => u.usage || 1);
        });
    }

    return patterns;
};

ZaiqaAIAssistant.prototype.analyzeCustomerPatterns = function() {
    const orders = JSON.parse(localStorage.getItem('orders') || '[]');
    const patterns = {
        serviceTypePreference: { dine_in: 0, takeaway: 0 },
        averageOrderValue: [],
        peakTimes: {}
    };

    orders.forEach(order => {
        patterns.serviceTypePreference[order.service_type] =
            (patterns.serviceTypePreference[order.service_type] || 0) + 1;

        patterns.averageOrderValue.push(order.total_amount || 0);

        const hour = new Date(order.created_at).getHours();
        patterns.peakTimes[hour] = (patterns.peakTimes[hour] || 0) + 1;
    });

    return patterns;
};

ZaiqaAIAssistant.prototype.analyzeOperationalPatterns = function() {
    const staff = JSON.parse(localStorage.getItem('staffMembers') || '[]');
    const orders = JSON.parse(localStorage.getItem('orders') || '[]');

    const patterns = {
        staffingNeeds: {},
        efficiency: {},
        workload: {}
    };

    // Analyze staffing needs by hour and day
    orders.forEach(order => {
        const date = new Date(order.created_at);
        const hour = date.getHours();
        const dayOfWeek = date.getDay();
        const key = `${dayOfWeek}-${hour}`;

        if (!patterns.staffingNeeds[key]) patterns.staffingNeeds[key] = [];
        patterns.staffingNeeds[key].push(staff.filter(s => s.isActive !== false).length);
    });

    return patterns;
};

ZaiqaAIAssistant.prototype.getMenuItemAnalysis = function(data) {
    const menuItems = data.menuItems;
    const orders = data.orders;

    return menuItems.map(item => {
        const itemOrders = [];
        let totalRevenue = 0;
        let totalQuantity = 0;

        orders.forEach(order => {
            order.items?.forEach(orderItem => {
                if (orderItem.name === item.name) {
                    itemOrders.push(orderItem);
                    totalRevenue += orderItem.price * orderItem.quantity;
                    totalQuantity += orderItem.quantity;
                }
            });
        });

        const cost = item.costPrice || item.basePrice * 0.6; // Estimate 60% cost ratio
        const profit = totalRevenue - (cost * totalQuantity);
        const profitMargin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0;

        return {
            name: item.name,
            orderCount: itemOrders.length,
            totalQuantity,
            revenue: totalRevenue,
            profit,
            profitMargin,
            avgPrice: totalQuantity > 0 ? totalRevenue / totalQuantity : item.basePrice
        };
    });
};

ZaiqaAIAssistant.prototype.getAverageDailyRevenue = function() {
    const orders = JSON.parse(localStorage.getItem('orders') || '[]');
    const dailyRevenues = {};

    orders.forEach(order => {
        const date = new Date(order.created_at).toDateString();
        if (!dailyRevenues[date]) dailyRevenues[date] = 0;
        dailyRevenues[date] += order.total_amount || 0;
    });

    const revenues = Object.values(dailyRevenues);
    return revenues.length > 0 ? revenues.reduce((sum, rev) => sum + rev, 0) / revenues.length : 0;
};

ZaiqaAIAssistant.prototype.predictItemUsage = function(item) {
    const usage = this.learningData.inventoryUsage?.[item.id] || [];

    if (usage.length === 0) {
        return { dailyAverage: 1, weeklyAverage: 7 };
    }

    const dailyUsages = usage.map(u => u.usage || 1);
    const dailyAverage = dailyUsages.reduce((sum, u) => sum + u, 0) / dailyUsages.length;

    return {
        dailyAverage,
        weeklyAverage: dailyAverage * 7,
        trend: this.calculateTrend(dailyUsages)
    };
};

ZaiqaAIAssistant.prototype.calculateTrend = function(values) {
    if (values.length < 2) return 'stable';

    const recent = values.slice(-7); // Last 7 data points
    const older = values.slice(-14, -7); // Previous 7 data points

    if (recent.length === 0 || older.length === 0) return 'stable';

    const recentAvg = recent.reduce((sum, v) => sum + v, 0) / recent.length;
    const olderAvg = older.reduce((sum, v) => sum + v, 0) / older.length;

    const change = (recentAvg - olderAvg) / olderAvg;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
};

ZaiqaAIAssistant.prototype.detectAnomalies = function(data) {
    const anomalies = [];
    const now = new Date();
    const hour = now.getHours();

    // Detect unusual order patterns
    const recentOrders = data.orders.filter(order => {
        const orderTime = new Date(order.created_at);
        return (now - orderTime) < 60 * 60 * 1000; // Last hour
    });

    const historicalHourlyAverage = this.getHistoricalHourlyAverage(hour);

    if (recentOrders.length > historicalHourlyAverage * 2) {
        anomalies.push({
            type: 'anomaly',
            level: 'warning',
            title: 'Unusual Order Spike',
            message: `${recentOrders.length} orders in the last hour (${Math.round(historicalHourlyAverage)} average)`,
            action: 'Check if additional staff is needed.',
            priority: 'high'
        });
    }

    // Detect inventory anomalies
    const rapidDepletions = data.inventory.filter(item => {
        const usage = this.predictItemUsage(item);
        return usage.trend === 'increasing' && item.currentQuantity < usage.dailyAverage * 2;
    });

    if (rapidDepletions.length > 0) {
        anomalies.push({
            type: 'anomaly',
            level: 'warning',
            title: 'Rapid Inventory Depletion',
            message: `${rapidDepletions.length} items depleting faster than usual`,
            action: 'Review usage patterns and adjust ordering.',
            priority: 'medium'
        });
    }

    return anomalies;
};

ZaiqaAIAssistant.prototype.getHistoricalHourlyAverage = function(hour) {
    const patterns = this.patterns.sales?.hourlySales?.[hour] || [];
    return patterns.length > 0 ? patterns.reduce((sum, count) => sum + count, 0) / patterns.length : 1;
};

ZaiqaAIAssistant.prototype.generatePredictions = function(data) {
    const predictions = {};

    // Sales predictions
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    predictions.sales = this.predictionModels.sales.predictDailySales(tomorrow);

    // Inventory predictions
    predictions.inventory = data.inventory.map(item => ({
        item: item.name,
        prediction: this.predictionModels.inventory.predictUsage(item, 7)
    }));

    // Staffing predictions
    predictions.staffing = this.predictionModels.staffing.predictStaffingNeeds(
        new Date().getHours(), tomorrow.getDay()
    );

    return predictions;
};

ZaiqaAIAssistant.prototype.updatePredictions = function(data) {
    this.predictions = this.generatePredictions(data);

    // Store predictions for later use
    localStorage.setItem('zaiqaAIPredictions', JSON.stringify({
        timestamp: new Date().toISOString(),
        predictions: this.predictions
    }));
};

ZaiqaAIAssistant.prototype.getWeatherData = function() {
    // Placeholder for weather integration
    // In a real implementation, this would fetch from a weather API
    return {
        temperature: 25,
        condition: 'clear',
        humidity: 60
    };
};

// Initialize AI Assistant when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for the main app to initialize
    setTimeout(() => {
        try {
            window.zaiqaAI = new ZaiqaAIAssistant();
            console.log('🤖 AI Assistant initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize AI Assistant:', error);
            console.log('⚠️ Continuing without AI Assistant');
        }
    }, 2000);
});

// Export for global access
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ZaiqaAIAssistant;
}
