/**
 * Zaiqa Restaurant App Integration - Clean Architecture
 * Integrates the new clean financial system with the original app
 * Replaces old financial functions with new accurate calculations
 */

// Wait for both the original app and new system to be ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait for systems to initialize with multiple retry attempts
    let retryCount = 0;
    const maxRetries = 10;

    function attemptIntegration() {
        retryCount++;
        console.log(`🔄 Integration attempt ${retryCount}/${maxRetries}`);

        if (typeof window.app !== 'undefined' && typeof window.zaiqaRestaurantSystem !== 'undefined') {
            initializeAppIntegration();
        } else if (retryCount < maxRetries) {
            setTimeout(attemptIntegration, 1000);
        } else {
            console.error('❌ Failed to initialize integration after maximum retries');
        }
    }

    setTimeout(attemptIntegration, 2000);
});

/**
 * Initialize the integration between old app and new system
 */
function initializeAppIntegration() {
    try {
        console.log('🔗 Initializing App Integration...');
        
        // Check if both systems are available
        if (typeof window.app === 'undefined') {
            console.warn('⚠️ Original app not found, retrying...');
            setTimeout(initializeAppIntegration, 1000);
            return;
        }
        
        if (typeof window.zaiqaRestaurantSystem === 'undefined') {
            console.warn('⚠️ New system not found, retrying...');
            setTimeout(initializeAppIntegration, 1000);
            return;
        }
        
        // Initialize reports page integration
        initializeReportsPageIntegration();
        
        // Override old financial functions
        overrideFinancialFunctions();
        
        // Set up data synchronization
        setupDataSynchronization();
        
        console.log('✅ App Integration completed successfully');
        
    } catch (error) {
        console.error('❌ App Integration failed:', error);
    }
}

/**
 * Initialize reports page integration
 */
function initializeReportsPageIntegration() {
    try {
        console.log('📊 Setting up Reports Page Integration...');
        
        // Create global reports page instance
        window.zaiqaReports = new ZaiqaReportsPage(window.zaiqaRestaurantSystem);
        
        // Override ALL reports page functions
        if (window.app) {
            // Store original functions
            if (typeof window.app.loadReportsPage === 'function') {
                window.app.originalLoadReportsPage = window.app.loadReportsPage;
            }
            if (typeof window.app.loadReportsPageWithCleanData === 'function') {
                window.app.originalLoadReportsPageWithCleanData = window.app.loadReportsPageWithCleanData;
            }

            // Override main loadReportsPage function
            window.app.loadReportsPage = function(pageElement) {
                try {
                    console.log('📊 Loading NEW Enhanced Reports Page...');

                    // Ensure the new system is ready
                    if (!window.zaiqaReports) {
                        console.warn('⚠️ Enhanced Reports not ready, initializing...');

                        // Initialize reports system immediately
                        try {
                            if (typeof ZaiqaReportsPage !== 'undefined') {
                                window.zaiqaReports = new ZaiqaReportsPage(null);
                                console.log('✅ Enhanced Reports initialized successfully');
                            } else {
                                console.warn('⚠️ ZaiqaReportsPage class not available');
                                throw new Error('ZaiqaReportsPage class not found');
                            }
                        } catch (error) {
                            console.error('❌ Failed to initialize Enhanced Reports:', error);
                            // Fallback to original reports
                            if (window.app && typeof window.app.loadReportsPageWithCleanData === 'function') {
                                window.app.loadReportsPageWithCleanData(pageElement);
                                return;
                            }
                        }
                    }

                    // Show loading state briefly
                    pageElement.innerHTML = `
                        <div class="loading-state">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <h2>Loading Enhanced Reports...</h2>
                            <p>Initializing advanced analytics and cost management tools...</p>
                        </div>
                    `;

                    // Render enhanced reports after brief delay
                    setTimeout(() => {
                        if (window.zaiqaReports && typeof window.zaiqaReports.render === 'function') {
                            try {
                                window.zaiqaReports.render(pageElement);
                                console.log('✅ Enhanced Reports rendered successfully');
                            } catch (renderError) {
                                console.error('❌ Enhanced Reports render failed:', renderError);
                                pageElement.innerHTML = `
                                    <div class="error-state">
                                        <h2>Error Loading Reports</h2>
                                        <p>The enhanced reports system encountered an error: ${renderError.message}</p>
                                        <button onclick="window.location.reload()" class="btn btn-primary">
                                            <i class="fas fa-sync-alt"></i> Refresh Page
                                        </button>
                                    </div>
                                `;
                            }
                        } else {
                            console.error('❌ Enhanced Reports still not available, falling back to original');
                            // Fallback to original reports
                            if (window.app && typeof window.app.originalLoadReportsPage === 'function') {
                                window.app.originalLoadReportsPage(pageElement);
                            } else if (window.app && typeof window.app.loadReportsPageWithCleanData === 'function') {
                                window.app.loadReportsPageWithCleanData(pageElement);
                            } else {
                                pageElement.innerHTML = `
                                    <div class="error-state">
                                        <h2>Reports Unavailable</h2>
                                        <p>The reports system could not be loaded.</p>
                                        <button onclick="window.location.reload()" class="btn btn-primary">
                                            <i class="fas fa-sync-alt"></i> Refresh Page
                                        </button>
                                    </div>
                                `;
                            }
                        }
                    }, 500);

                } catch (error) {
                    console.error('❌ Failed to load new reports page:', error);

                    // Show error state with retry option
                    pageElement.innerHTML = `
                        <div class="error-state">
                            <h2>Error Loading Enhanced Reports</h2>
                            <p>Failed to load the new reports system. Please try refreshing the page.</p>
                            <button onclick="window.location.reload()" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> Refresh Page
                            </button>
                            <button onclick="window.app.originalLoadReportsPage && window.app.originalLoadReportsPage(document.getElementById('reportsPage'))" class="btn btn-secondary">
                                <i class="fas fa-backward"></i> Load Basic Reports
                            </button>
                        </div>
                    `;
                }
            };

            // Also override the clean data function
            window.app.loadReportsPageWithCleanData = function(pageElement) {
                console.log('📊 Redirecting clean data reports to new system...');
                window.app.loadReportsPage(pageElement);
            };
        }
        
        console.log('✅ Reports Page Integration completed');
        
    } catch (error) {
        console.error('❌ Reports Page Integration failed:', error);
    }
}

/**
 * Override old financial functions with new accurate calculations
 */
function overrideFinancialFunctions() {
    try {
        console.log('💰 Overriding Financial Functions...');
        
        if (!window.app) return;
        
        // Override dashboard stats calculation
        if (typeof window.app.updateDashboardStats === 'function') {
            window.app.originalUpdateDashboardStats = window.app.updateDashboardStats;
            
            window.app.updateDashboardStats = function() {
                try {
                    updateDashboardWithNewSystem();
                } catch (error) {
                    console.error('❌ Failed to update dashboard with new system:', error);
                    // Fallback to original
                    if (this.originalUpdateDashboardStats) {
                        this.originalUpdateDashboardStats();
                    }
                }
            };
        }
        
        // Override financial calculation functions
        const financialFunctions = [
            'calculateRevenue',
            'calculateExpenses', 
            'calculateProfit',
            'calculateCashFlow'
        ];
        
        financialFunctions.forEach(funcName => {
            if (typeof window.app[funcName] === 'function') {
                window.app[`original${funcName}`] = window.app[funcName];
                
                window.app[funcName] = function(...args) {
                    try {
                        return getNewSystemFinancialData(funcName, ...args);
                    } catch (error) {
                        console.error(`❌ Failed to calculate ${funcName} with new system:`, error);
                        // Fallback to original
                        const originalFunc = this[`original${funcName}`];
                        if (originalFunc) {
                            return originalFunc.apply(this, args);
                        }
                        return 0;
                    }
                };
            }
        });
        
        console.log('✅ Financial Functions Override completed');
        
    } catch (error) {
        console.error('❌ Financial Functions Override failed:', error);
    }
}

/**
 * Update dashboard with new system data
 */
function updateDashboardWithNewSystem() {
    try {
        const todayMetrics = window.zaiqaRestaurantSystem.getFinancialData('todayMetrics');
        const udharSummary = window.zaiqaRestaurantSystem.getFinancialData('udharSummary');
        
        // Update revenue
        const revenueElement = document.getElementById('todayRevenue');
        if (revenueElement && todayMetrics.revenue) {
            revenueElement.textContent = `PKR ${todayMetrics.revenue.totalRevenue.toLocaleString()}`;
        }
        
        // Update orders count
        const ordersElement = document.getElementById('todayOrders');
        if (ordersElement && todayMetrics.revenue) {
            ordersElement.textContent = todayMetrics.revenue.orderCount.toString();
        }
        
        // Update average order value
        const avgOrderElement = document.getElementById('avgOrderValue');
        if (avgOrderElement && todayMetrics.revenue) {
            avgOrderElement.textContent = `PKR ${Math.round(todayMetrics.revenue.averageOrderValue).toLocaleString()}`;
        }
        
        // Update cash balance
        const cashBalanceElement = document.getElementById('cashBalance');
        if (cashBalanceElement && todayMetrics.cashFlow) {
            const netCash = todayMetrics.cashFlow.closingBalance || 0;
            cashBalanceElement.textContent = `PKR ${netCash.toLocaleString()}`;
        }
        
        // Update recent orders
        updateRecentOrdersList();
        
        console.log('✅ Dashboard updated with new system data');
        
    } catch (error) {
        console.error('❌ Failed to update dashboard:', error);
    }
}

/**
 * Update recent orders list
 */
function updateRecentOrdersList() {
    try {
        const ordersList = document.getElementById('recentOrdersList');
        if (!ordersList) return;
        
        const orders = window.zaiqaRestaurantSystem.getData('orders', { status: 'completed' })
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);
        
        if (orders.length === 0) {
            ordersList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <p>No orders yet today</p>
                    <p style="font-size: 0.875rem; color: var(--gray-500);">Orders will appear here as they are created</p>
                </div>
            `;
            return;
        }
        
        ordersList.innerHTML = orders.map(order => `
            <div class="order-item">
                <div class="order-info">
                    <div class="order-number">${order.orderNumber}</div>
                    <div class="order-time">${new Date(order.createdAt).toLocaleTimeString()}</div>
                </div>
                <div class="order-details">
                    <div class="order-amount">PKR ${(order.totalAmount || 0).toLocaleString()}</div>
                    <div class="order-items">${order.items ? order.items.length : 0} items</div>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('❌ Failed to update recent orders:', error);
    }
}

/**
 * Get financial data from new system
 */
function getNewSystemFinancialData(type, ...args) {
    try {
        switch (type) {
            case 'calculateRevenue':
                const revenueData = window.zaiqaRestaurantSystem.getFinancialData('todayMetrics');
                return revenueData.revenue ? revenueData.revenue.totalRevenue : 0;
                
            case 'calculateExpenses':
                const expenseData = window.zaiqaRestaurantSystem.getFinancialData('todayMetrics');
                return expenseData.expenses ? expenseData.expenses.totalExpenses : 0;
                
            case 'calculateProfit':
                const profitData = window.zaiqaRestaurantSystem.getFinancialData('todayMetrics');
                return profitData.profitLoss ? profitData.profitLoss.grossProfit : 0;
                
            case 'calculateCashFlow':
                const cashData = window.zaiqaRestaurantSystem.getFinancialData('todayMetrics');
                return cashData.cashFlow ? cashData.cashFlow.netCashFlow : 0;
                
            default:
                return 0;
        }
    } catch (error) {
        console.error(`❌ Failed to get ${type} from new system:`, error);
        return 0;
    }
}

/**
 * Set up data synchronization between old and new systems
 */
function setupDataSynchronization() {
    try {
        console.log('🔄 Setting up Data Synchronization...');
        
        // Sync orders data
        syncOrdersData();
        
        // Sync menu items data
        syncMenuItemsData();
        
        // Sync expenses data
        syncExpensesData();
        
        // Set up periodic sync
        setInterval(() => {
            try {
                syncOrdersData();
                syncExpensesData();
            } catch (error) {
                console.error('❌ Periodic sync failed:', error);
            }
        }, 30000); // Sync every 30 seconds
        
        console.log('✅ Data Synchronization setup completed');
        
    } catch (error) {
        console.error('❌ Data Synchronization setup failed:', error);
    }
}

/**
 * Sync orders data from localStorage to new system
 */
function syncOrdersData() {
    try {
        const oldOrders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
        const newOrders = window.zaiqaRestaurantSystem.getData('orders');
        
        // Find orders that exist in old system but not in new system
        oldOrders.forEach(oldOrder => {
            const exists = newOrders.find(newOrder => newOrder.orderNumber === oldOrder.order_number);
            if (!exists && oldOrder.order_number) {
                // Convert old order format to new format
                const convertedOrder = {
                    orderNumber: oldOrder.order_number,
                    customerName: oldOrder.customer_name || '',
                    customerCount: oldOrder.customer_count || 1,
                    serviceType: oldOrder.service_type || 'dine_in',
                    items: oldOrder.items || [],
                    totalAmount: oldOrder.total_amount || 0,
                    paymentMethod: oldOrder.payment_method || 'cash',
                    status: 'completed',
                    createdAt: oldOrder.created_at || new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                window.zaiqaRestaurantSystem.saveData('orders', convertedOrder);
            }
        });
        
    } catch (error) {
        console.error('❌ Failed to sync orders data:', error);
    }
}

/**
 * Sync menu items data
 */
function syncMenuItemsData() {
    try {
        const oldMenuItems = JSON.parse(localStorage.getItem('menuItems') || '[]');
        const newMenuItems = window.zaiqaRestaurantSystem.getData('menuItems');
        
        oldMenuItems.forEach(oldItem => {
            const exists = newMenuItems.find(newItem => newItem.name === oldItem.name);
            if (!exists && oldItem.name) {
                const convertedItem = {
                    name: oldItem.name,
                    category: oldItem.category || 'Main Course',
                    basePrice: oldItem.basePrice || oldItem.price || 0,
                    isAvailable: oldItem.isAvailable !== false,
                    description: oldItem.description || '',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                window.zaiqaRestaurantSystem.saveData('menuItems', convertedItem);
            }
        });
        
    } catch (error) {
        console.error('❌ Failed to sync menu items data:', error);
    }
}

/**
 * Sync expenses data
 */
function syncExpensesData() {
    try {
        const oldExpenses = JSON.parse(localStorage.getItem('expenses') || '[]');
        const newExpenses = window.zaiqaRestaurantSystem.getData('expenses');
        
        oldExpenses.forEach(oldExpense => {
            const exists = newExpenses.find(newExpense => 
                newExpense.description === oldExpense.description && 
                newExpense.date === oldExpense.date &&
                newExpense.amount === oldExpense.amount
            );
            
            if (!exists && oldExpense.description) {
                const convertedExpense = {
                    description: oldExpense.description,
                    amount: oldExpense.amount || 0,
                    category: oldExpense.category || 'Other',
                    date: oldExpense.date || new Date().toISOString().split('T')[0],
                    createdAt: oldExpense.created_at || new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                window.zaiqaRestaurantSystem.saveData('expenses', convertedExpense);
            }
        });
        
    } catch (error) {
        console.error('❌ Failed to sync expenses data:', error);
    }
}

// Export for global access
window.appIntegration = {
    initializeAppIntegration,
    updateDashboardWithNewSystem,
    getNewSystemFinancialData,
    syncOrdersData,
    syncMenuItemsData,
    syncExpensesData
};
