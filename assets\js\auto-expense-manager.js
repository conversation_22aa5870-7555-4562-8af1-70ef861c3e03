/**
 * Zaiqa Auto Expense Manager - Automatic Daily Expense Deductions
 * Manages automatic expense deductions with configurable settings
 */

class ZaiqaAutoExpenseManager {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize the auto expense manager
     */
    init() {
        try {
            console.log('⚙️ Initializing Auto Expense Manager v' + this.version);
            
            // Set up auto expense configuration
            this.setupAutoExpenseConfig();
            
            // Initialize daily expense checking
            this.initializeDailyExpenseCheck();
            
            // Set up expenses page integration
            this.setupExpensesPageIntegration();
            
            this.initialized = true;
            console.log('✅ Auto Expense Manager initialized successfully');
            
        } catch (error) {
            console.error('❌ Auto Expense Manager initialization failed:', error);
        }
    }

    /**
     * Set up auto expense configuration
     */
    setupAutoExpenseConfig() {
        try {
            // Load existing configuration or set defaults
            const defaultConfig = {
                enabled: false,
                lastDeductionDate: null,
                autoExpenses: [
                    {
                        id: 'rent',
                        name: 'Shop Rent',
                        nameUrdu: 'دکان کا کرایہ',
                        amount: 0,
                        enabled: false,
                        frequency: 'daily' // daily, weekly, monthly
                    },
                    {
                        id: 'electricity',
                        name: 'Electricity Bill',
                        nameUrdu: 'بجلی بل',
                        amount: 0,
                        enabled: false,
                        frequency: 'daily'
                    },
                    {
                        id: 'gas',
                        name: 'Gas Bill',
                        nameUrdu: 'گیس بل',
                        amount: 0,
                        enabled: false,
                        frequency: 'daily'
                    },
                    {
                        id: 'water',
                        name: 'Water Bill',
                        nameUrdu: 'پانی بل',
                        amount: 0,
                        enabled: false,
                        frequency: 'daily'
                    },
                    {
                        id: 'maintenance',
                        name: 'Maintenance',
                        nameUrdu: 'مرمت',
                        amount: 0,
                        enabled: false,
                        frequency: 'daily'
                    }
                ]
            };

            const existingConfig = localStorage.getItem('autoExpenseConfig');
            if (!existingConfig) {
                localStorage.setItem('autoExpenseConfig', JSON.stringify(defaultConfig));
            }

            console.log('✅ Auto expense configuration setup completed');

        } catch (error) {
            console.error('❌ Failed to setup auto expense configuration:', error);
        }
    }

    /**
     * Initialize daily expense checking
     */
    initializeDailyExpenseCheck() {
        try {
            // Check for pending auto expenses on startup
            setTimeout(() => {
                this.checkAndProcessAutoExpenses();
            }, 3000);

            // Set up daily checking interval (every hour)
            setInterval(() => {
                this.checkAndProcessAutoExpenses();
            }, 60 * 60 * 1000); // 1 hour

            console.log('✅ Daily expense checking initialized');

        } catch (error) {
            console.error('❌ Failed to initialize daily expense checking:', error);
        }
    }

    /**
     * Check and process auto expenses
     */
    checkAndProcessAutoExpenses() {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            
            if (!config.enabled) {
                return; // Auto expenses disabled
            }

            const today = new Date().toISOString().split('T')[0];
            const lastDeductionDate = config.lastDeductionDate;

            // Check if we need to process expenses for today
            if (lastDeductionDate === today) {
                return; // Already processed today
            }

            // Process enabled auto expenses
            const enabledExpenses = config.autoExpenses.filter(expense => expense.enabled && expense.amount > 0);
            
            if (enabledExpenses.length > 0) {
                this.processAutoExpenses(enabledExpenses, today);
            }

        } catch (error) {
            console.error('❌ Failed to check and process auto expenses:', error);
        }
    }

    /**
     * Process auto expenses for the day
     */
    processAutoExpenses(expenses, date) {
        try {
            let totalDeducted = 0;
            const timestamp = new Date().toISOString();

            expenses.forEach(expense => {
                // Create expense entry
                const expenseEntry = {
                    id: 'auto_' + expense.id + '_' + Date.now(),
                    description: `${expense.name} (Auto Deduction)`,
                    amount: expense.amount,
                    category: 'Auto Expenses',
                    date: date,
                    created_at: timestamp,
                    type: 'auto_expense',
                    autoExpenseId: expense.id
                };

                // Add to expenses
                this.addExpenseEntry(expenseEntry);
                totalDeducted += expense.amount;
            });

            // Deduct from cash in hand
            if (totalDeducted > 0 && window.cashManager) {
                window.cashManager.updateCashInHand(-totalDeducted);
            }

            // Update last deduction date
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            config.lastDeductionDate = date;
            localStorage.setItem('autoExpenseConfig', JSON.stringify(config));

            // Show notification
            this.showNotification(`Auto expenses processed: PKR ${totalDeducted.toLocaleString()} deducted`, 'info');

            console.log(`✅ Auto expenses processed: PKR ${totalDeducted}`);

        } catch (error) {
            console.error('❌ Failed to process auto expenses:', error);
        }
    }

    /**
     * Add expense entry to database
     */
    addExpenseEntry(expenseData) {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            expenses.push(expenseData);
            localStorage.setItem('expenses', JSON.stringify(expenses));

        } catch (error) {
            console.error('❌ Failed to add auto expense entry:', error);
        }
    }

    /**
     * Set up expenses page integration
     */
    setupExpensesPageIntegration() {
        try {
            // Override the expenses page loading function
            if (window.app && typeof window.app.loadExpensesPage === 'function') {
                window.app.originalLoadExpensesPage = window.app.loadExpensesPage;
                
                window.app.loadExpensesPage = (pageElement) => {
                    // Call original function first
                    if (window.app.originalLoadExpensesPage) {
                        window.app.originalLoadExpensesPage(pageElement);
                    }
                    
                    // Add auto expense section
                    setTimeout(() => {
                        this.addAutoExpenseSection(pageElement);
                    }, 100);
                };
            }

        } catch (error) {
            console.error('❌ Failed to setup expenses page integration:', error);
        }
    }

    /**
     * Add auto expense section to expenses page
     */
    addAutoExpenseSection(pageElement) {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            
            // Find the expenses container
            const expensesContainer = pageElement.querySelector('.expenses-container') || pageElement;
            
            // Create auto expense section
            const autoExpenseSection = document.createElement('div');
            autoExpenseSection.className = 'auto-expense-section';
            autoExpenseSection.innerHTML = `
                <div class="section-header">
                    <h2>
                        <i class="fas fa-cog"></i>
                        Auto Expense Management
                    </h2>
                    <div class="auto-expense-toggle">
                        <label class="toggle-switch">
                            <input type="checkbox" id="autoExpenseEnabled" ${config.enabled ? 'checked' : ''} 
                                   onchange="window.autoExpenseManager.toggleAutoExpenses()">
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">
                            ${config.enabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </div>
                </div>
                
                <div class="auto-expense-status">
                    <div class="status-item">
                        <span class="status-label">Status:</span>
                        <span class="status-value ${config.enabled ? 'enabled' : 'disabled'}">
                            ${config.enabled ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Last Deduction:</span>
                        <span class="status-value">
                            ${config.lastDeductionDate || 'Never'}
                        </span>
                    </div>
                </div>
                
                <div class="auto-expense-config">
                    <h3>Configure Auto Expenses</h3>
                    <div class="auto-expense-list">
                        ${this.renderAutoExpenseList(config.autoExpenses || [])}
                    </div>
                    <div class="config-actions">
                        <button onclick="window.autoExpenseManager.saveAutoExpenseConfig()" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                        <button onclick="window.autoExpenseManager.processManualAutoExpenses()" class="btn btn-secondary">
                            <i class="fas fa-play"></i> Process Now
                        </button>
                    </div>
                </div>
            `;
            
            // Insert at the beginning of the expenses container
            expensesContainer.insertBefore(autoExpenseSection, expensesContainer.firstChild);

        } catch (error) {
            console.error('❌ Failed to add auto expense section:', error);
        }
    }

    /**
     * Render auto expense list
     */
    renderAutoExpenseList(autoExpenses) {
        return autoExpenses.map(expense => `
            <div class="auto-expense-item">
                <div class="expense-info">
                    <label class="checkbox-label">
                        <input type="checkbox" 
                               id="expense_${expense.id}" 
                               ${expense.enabled ? 'checked' : ''}
                               onchange="window.autoExpenseManager.updateExpenseEnabled('${expense.id}', this.checked)">
                        <span class="expense-name">${expense.name} (${expense.nameUrdu})</span>
                    </label>
                </div>
                <div class="expense-amount">
                    <input type="number" 
                           id="amount_${expense.id}" 
                           value="${expense.amount}" 
                           placeholder="Amount"
                           onchange="window.autoExpenseManager.updateExpenseAmount('${expense.id}', this.value)">
                    <span class="currency">PKR</span>
                </div>
            </div>
        `).join('');
    }

    /**
     * Toggle auto expenses on/off
     */
    toggleAutoExpenses() {
        try {
            const enabled = document.getElementById('autoExpenseEnabled')?.checked || false;
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            
            config.enabled = enabled;
            localStorage.setItem('autoExpenseConfig', JSON.stringify(config));
            
            // Update UI
            const toggleLabel = document.querySelector('.toggle-label');
            const statusValue = document.querySelector('.status-value');
            
            if (toggleLabel) {
                toggleLabel.textContent = enabled ? 'Enabled' : 'Disabled';
            }
            
            if (statusValue) {
                statusValue.textContent = enabled ? 'Active' : 'Inactive';
                statusValue.className = `status-value ${enabled ? 'enabled' : 'disabled'}`;
            }
            
            this.showNotification(`Auto expenses ${enabled ? 'enabled' : 'disabled'}`, 'success');

        } catch (error) {
            console.error('❌ Failed to toggle auto expenses:', error);
        }
    }

    /**
     * Update expense enabled status
     */
    updateExpenseEnabled(expenseId, enabled) {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const expense = config.autoExpenses.find(exp => exp.id === expenseId);
            
            if (expense) {
                expense.enabled = enabled;
                localStorage.setItem('autoExpenseConfig', JSON.stringify(config));
            }

        } catch (error) {
            console.error('❌ Failed to update expense enabled status:', error);
        }
    }

    /**
     * Update expense amount
     */
    updateExpenseAmount(expenseId, amount) {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const expense = config.autoExpenses.find(exp => exp.id === expenseId);
            
            if (expense) {
                expense.amount = parseFloat(amount) || 0;
                localStorage.setItem('autoExpenseConfig', JSON.stringify(config));
            }

        } catch (error) {
            console.error('❌ Failed to update expense amount:', error);
        }
    }

    /**
     * Save auto expense configuration
     */
    saveAutoExpenseConfig() {
        try {
            this.showNotification('Auto expense configuration saved successfully', 'success');

        } catch (error) {
            console.error('❌ Failed to save auto expense configuration:', error);
            this.showNotification('Failed to save configuration', 'error');
        }
    }

    /**
     * Process auto expenses manually
     */
    processManualAutoExpenses() {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const enabledExpenses = config.autoExpenses.filter(expense => expense.enabled && expense.amount > 0);
            
            if (enabledExpenses.length === 0) {
                this.showNotification('No auto expenses configured or enabled', 'warning');
                return;
            }

            const today = new Date().toISOString().split('T')[0];
            this.processAutoExpenses(enabledExpenses, today);

        } catch (error) {
            console.error('❌ Failed to process manual auto expenses:', error);
            this.showNotification('Failed to process auto expenses', 'error');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        try {
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(message, type);
            } else {
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        } catch (error) {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize the auto expense manager
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.autoExpenseManager = new ZaiqaAutoExpenseManager();
    }, 2000);
});

// Export for global use
window.ZaiqaAutoExpenseManager = ZaiqaAutoExpenseManager;
