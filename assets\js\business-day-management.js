/**
 * Business Day Management System
 * Handles morning balance, owner withdrawals, and end of day operations
 */

class BusinessDayManager {
    constructor(analytics) {
        this.analytics = analytics;
        this.modals = new Map();
    }

    /**
     * Show Morning Balance Modal
     */
    showMorningBalanceModal() {
        const modal = this.createModal('Set Morning Balance', `
            <div class="modal-form">
                <div class="form-group">
                    <label for="morningBalanceAmount">Opening Balance Amount (PKR)</label>
                    <input type="number" id="morningBalanceAmount" class="form-control" 
                           placeholder="Enter opening balance" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="morningBalanceDate">Date</label>
                    <input type="date" id="morningBalanceDate" class="form-control" 
                           value="${new Date().toISOString().split('T')[0]}" required>
                </div>
                <div class="form-group">
                    <label for="morningBalanceNotes">Notes (Optional)</label>
                    <textarea id="morningBalanceNotes" class="form-control" 
                              placeholder="Additional notes about the opening balance" rows="3"></textarea>
                </div>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    <span>This will set the starting cash amount for the selected date.</span>
                </div>
            </div>
        `, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => this.closeModal('morningBalance')
            },
            {
                text: 'Set Balance',
                class: 'btn-primary',
                action: () => this.saveMorningBalance()
            }
        ]);
        
        this.showModal('morningBalance', modal);
    }

    /**
     * Save Morning Balance
     */
    async saveMorningBalance() {
        try {
            const amount = parseFloat(document.getElementById('morningBalanceAmount').value);
            const date = document.getElementById('morningBalanceDate').value;
            const notes = document.getElementById('morningBalanceNotes').value;
            
            if (isNaN(amount) || amount < 0) {
                this.showError('Please enter a valid amount');
                return;
            }
            
            if (!date) {
                this.showError('Please select a date');
                return;
            }
            
            // Save to business days
            const businessDays = JSON.parse(localStorage.getItem('businessDays') || '[]');
            let dayEntry = businessDays.find(entry => entry.date === date);
            
            if (!dayEntry) {
                dayEntry = {
                    id: 'day_' + Date.now(),
                    date: date,
                    opening_balance: amount,
                    closing_balance: amount,
                    expected_closing: amount,
                    cash_difference: 0,
                    total_sales: 0,
                    total_expenses: 0,
                    net_profit: 0,
                    order_count: 0,
                    is_finalized: false,
                    notes: notes,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };
                businessDays.push(dayEntry);
            } else {
                dayEntry.opening_balance = amount;
                dayEntry.notes = notes;
                dayEntry.updated_at = new Date().toISOString();
            }
            
            localStorage.setItem('businessDays', JSON.stringify(businessDays));
            
            // Update current cash if setting for today
            const today = new Date().toISOString().split('T')[0];
            if (date === today) {
                localStorage.setItem('currentCashInHand', amount.toString());
                
                // Add cash transaction
                this.addCashTransaction('opening', amount, `Opening balance for ${date}`, null, 'manual');
            }
            
            this.closeModal('morningBalance');
            this.showSuccess(`Morning balance of PKR ${amount.toLocaleString()} set successfully for ${date}`);
            
            // Refresh analytics and reports
            if (this.analytics) {
                this.analytics.clearCache();
            }

            // Force refresh reports page if it exists
            if (window.freshReportsController || window.reportsPageController) {
                const controller = window.freshReportsController || window.reportsPageController;
                console.log('🔄 Refreshing reports page after morning balance update');
                if (controller.refreshCashData) {
                    controller.refreshCashData();
                }
                if (controller.loadAllData) {
                    controller.loadAllData();
                }
            }
            
        } catch (error) {
            console.error('❌ Failed to save morning balance:', error);
            this.showError('Failed to save morning balance: ' + error.message);
        }
    }

    /**
     * Show Owner Withdrawal Modal
     */
    showOwnerWithdrawalModal() {
        const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
        
        const modal = this.createModal('Owner Withdrawal', `
            <div class="modal-form">
                <div class="form-group">
                    <label for="withdrawalAmount">Withdrawal Amount (PKR)</label>
                    <input type="number" id="withdrawalAmount" class="form-control" 
                           placeholder="Enter withdrawal amount" min="0" step="0.01" max="${currentCash}" required>
                </div>
                <div class="form-group">
                    <label for="withdrawalReason">Reason</label>
                    <select id="withdrawalReason" class="form-control" required>
                        <option value="">Select reason</option>
                        <option value="Personal Use">Personal Use</option>
                        <option value="Business Investment">Business Investment</option>
                        <option value="Emergency">Emergency</option>
                        <option value="Family Expense">Family Expense</option>
                        <option value="Loan Repayment">Loan Repayment</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group" id="customReasonGroup" style="display: none;">
                    <label for="customReason">Custom Reason</label>
                    <input type="text" id="customReason" class="form-control" 
                           placeholder="Enter custom reason">
                </div>
                <div class="form-group">
                    <label for="withdrawalNotes">Notes (Optional)</label>
                    <textarea id="withdrawalNotes" class="form-control" 
                              placeholder="Additional notes about the withdrawal" rows="3"></textarea>
                </div>
                <div class="cash-info">
                    <div class="info-item">
                        <span>Current Cash in Hand:</span>
                        <strong>PKR ${currentCash.toLocaleString()}</strong>
                    </div>
                </div>
            </div>
        `, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => this.closeModal('ownerWithdrawal')
            },
            {
                text: 'Record Withdrawal',
                class: 'btn-warning',
                action: () => this.saveOwnerWithdrawal()
            }
        ]);
        
        this.showModal('ownerWithdrawal', modal);
        
        // Add event listener for reason dropdown
        setTimeout(() => {
            document.getElementById('withdrawalReason').addEventListener('change', (e) => {
                const customGroup = document.getElementById('customReasonGroup');
                if (e.target.value === 'Other') {
                    customGroup.style.display = 'block';
                    document.getElementById('customReason').required = true;
                } else {
                    customGroup.style.display = 'none';
                    document.getElementById('customReason').required = false;
                }
            });
        }, 100);
    }

    /**
     * Save Owner Withdrawal
     */
    async saveOwnerWithdrawal() {
        try {
            const amount = parseFloat(document.getElementById('withdrawalAmount').value);
            const reason = document.getElementById('withdrawalReason').value;
            const customReason = document.getElementById('customReason').value;
            const notes = document.getElementById('withdrawalNotes').value;
            
            if (isNaN(amount) || amount <= 0) {
                this.showError('Please enter a valid withdrawal amount');
                return;
            }
            
            if (!reason) {
                this.showError('Please select a reason for withdrawal');
                return;
            }
            
            if (reason === 'Other' && !customReason.trim()) {
                this.showError('Please enter a custom reason');
                return;
            }
            
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            if (amount > currentCash) {
                this.showError('Insufficient cash in hand for this withdrawal');
                return;
            }
            
            const finalReason = reason === 'Other' ? customReason : reason;
            const description = `Owner Withdrawal: ${finalReason}`;
            
            // Add to expenses
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const withdrawal = {
                id: 'expense_' + Date.now(),
                description: description,
                amount: amount,
                category: 'owner_withdrawal',
                subcategory: finalReason,
                expense_date: new Date().toISOString().split('T')[0],
                expense_time: new Date().toTimeString().split(' ')[0],
                payment_method: 'cash',
                notes: notes,
                created_by: 'owner',
                created_at: new Date().toISOString()
            };
            
            expenses.push(withdrawal);
            localStorage.setItem('expenses', JSON.stringify(expenses));
            
            // Update cash in hand
            const newCash = currentCash - amount;
            localStorage.setItem('currentCashInHand', newCash.toString());
            
            // Add cash transaction
            this.addCashTransaction('withdrawal', -amount, description, withdrawal.id, 'expense');
            
            this.closeModal('ownerWithdrawal');
            this.showSuccess(`Owner withdrawal of PKR ${amount.toLocaleString()} recorded successfully`);
            
            // Refresh analytics and reports
            if (this.analytics) {
                this.analytics.clearCache();
            }

            // Force refresh reports page if it exists
            if (window.freshReportsController || window.reportsPageController) {
                const controller = window.freshReportsController || window.reportsPageController;
                console.log('🔄 Refreshing reports page after withdrawal');
                if (controller.refreshCashData) {
                    controller.refreshCashData();
                }
                if (controller.loadAllData) {
                    controller.loadAllData();
                }
            }
            
        } catch (error) {
            console.error('❌ Failed to save owner withdrawal:', error);
            this.showError('Failed to record withdrawal: ' + error.message);
        }
    }

    /**
     * Show End Business Day Modal
     */
    showEndBusinessDayModal() {
        const today = new Date().toISOString().split('T')[0];
        const stats = this.analytics.calculateQuickStats('today');
        const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
        
        const modal = this.createModal('End Business Day', `
            <div class="ebd-summary">
                <h4>Today's Business Summary (${today})</h4>
                <div class="ebd-grid">
                    <div class="ebd-item">
                        <span>Total Sales:</span>
                        <strong>PKR ${stats.totalRevenue.toLocaleString()}</strong>
                    </div>
                    <div class="ebd-item">
                        <span>Total Expenses:</span>
                        <strong>PKR ${stats.totalExpenses.toLocaleString()}</strong>
                    </div>
                    <div class="ebd-item">
                        <span>Net Profit:</span>
                        <strong class="${stats.netProfit >= 0 ? 'profit-positive' : 'profit-negative'}">
                            PKR ${stats.netProfit.toLocaleString()}
                        </strong>
                    </div>
                    <div class="ebd-item">
                        <span>Orders Count:</span>
                        <strong>${stats.orderCount}</strong>
                    </div>
                    <div class="ebd-item">
                        <span>Current Cash:</span>
                        <strong>PKR ${currentCash.toLocaleString()}</strong>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="actualClosingBalance">Actual Closing Balance (PKR)</label>
                    <input type="number" id="actualClosingBalance" class="form-control" 
                           placeholder="Enter actual cash count" min="0" step="0.01" 
                           value="${currentCash}" required>
                </div>
                
                <div class="form-group">
                    <label for="ebdNotes">End of Day Notes</label>
                    <textarea id="ebdNotes" class="form-control" 
                              placeholder="Any notes about today's business" rows="3"></textarea>
                </div>
                
                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This will finalize today's business and prepare the system for tomorrow.
                    This action cannot be undone.
                </div>
            </div>
        `, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => this.closeModal('endBusinessDay')
            },
            {
                text: 'End Business Day',
                class: 'btn-danger',
                action: () => this.finalizeBusinessDay()
            }
        ]);
        
        this.showModal('endBusinessDay', modal);
    }

    /**
     * Finalize Business Day
     */
    async finalizeBusinessDay() {
        try {
            const actualClosing = parseFloat(document.getElementById('actualClosingBalance').value);
            const notes = document.getElementById('ebdNotes').value;
            
            if (isNaN(actualClosing) || actualClosing < 0) {
                this.showError('Please enter a valid closing balance');
                return;
            }
            
            const today = new Date().toISOString().split('T')[0];
            const stats = this.analytics.calculateQuickStats('today');
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const cashDifference = actualClosing - currentCash;
            
            // Update business day record
            const businessDays = JSON.parse(localStorage.getItem('businessDays') || '[]');
            let todayEntry = businessDays.find(entry => entry.date === today);
            
            if (!todayEntry) {
                todayEntry = {
                    id: 'day_' + Date.now(),
                    date: today,
                    opening_balance: 0,
                    created_at: new Date().toISOString()
                };
                businessDays.push(todayEntry);
            }
            
            // Update with final data
            todayEntry.closing_balance = actualClosing;
            todayEntry.expected_closing = currentCash;
            todayEntry.cash_difference = cashDifference;
            todayEntry.total_sales = stats.totalRevenue;
            todayEntry.total_expenses = stats.totalExpenses;
            todayEntry.net_profit = stats.netProfit;
            todayEntry.order_count = stats.orderCount;
            todayEntry.is_finalized = true;
            todayEntry.finalized_at = new Date().toISOString();
            todayEntry.notes = notes;
            todayEntry.updated_at = new Date().toISOString();
            
            localStorage.setItem('businessDays', JSON.stringify(businessDays));
            
            // Update current cash
            localStorage.setItem('currentCashInHand', actualClosing.toString());
            
            // Add closing transaction
            this.addCashTransaction('closing', 0, `Business day closed - Actual: PKR ${actualClosing.toLocaleString()}`, null, 'manual');
            
            // If there's a cash difference, record it
            if (cashDifference !== 0) {
                const diffDescription = cashDifference > 0 ? 'Cash overage' : 'Cash shortage';
                this.addCashTransaction('adjustment', cashDifference, `${diffDescription}: PKR ${Math.abs(cashDifference).toLocaleString()}`, null, 'manual');
            }
            
            this.closeModal('endBusinessDay');
            this.showSuccess('Business day finalized successfully!');
            
            // Refresh analytics
            if (this.analytics) {
                this.analytics.clearCache();
            }
            
        } catch (error) {
            console.error('❌ Failed to finalize business day:', error);
            this.showError('Failed to finalize business day: ' + error.message);
        }
    }

    /**
     * Add cash transaction record
     */
    addCashTransaction(type, amount, description, referenceId, referenceType) {
        try {
            const transactions = JSON.parse(localStorage.getItem('cashTransactions') || '[]');
            const currentBalance = parseFloat(localStorage.getItem('currentCashInHand') || '0');

            const transaction = {
                id: 'cash_' + Date.now(),
                transaction_type: type,
                amount: amount,
                description: description,
                reference_id: referenceId,
                reference_type: referenceType,
                balance_before: currentBalance - amount,
                balance_after: currentBalance,
                transaction_date: new Date().toISOString().split('T')[0],
                transaction_time: new Date().toTimeString().split(' ')[0],
                created_by: 'system',
                created_at: new Date().toISOString()
            };

            transactions.push(transaction);
            localStorage.setItem('cashTransactions', JSON.stringify(transactions));

        } catch (error) {
            console.error('❌ Failed to add cash transaction:', error);
        }
    }

    /**
     * Create modal structure
     */
    createModal(title, content, buttons = []) {
        return {
            title,
            content,
            buttons
        };
    }

    /**
     * Show modal
     */
    showModal(id, modal) {
        // Remove existing modal if any
        this.closeModal(id);

        const modalHTML = `
            <div class="modal-overlay" id="modal_${id}">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>${modal.title}</h3>
                        <button class="modal-close" onclick="window.businessDayManager.closeModal('${id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${modal.content}
                    </div>
                    <div class="modal-footer">
                        ${modal.buttons.map((btn, index) =>
                            `<button class="btn ${btn.class}" onclick="window.businessDayManager.handleButtonClick('${id}', ${index})">${btn.text}</button>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;

        // Add to body
        const modalElement = document.createElement('div');
        modalElement.innerHTML = modalHTML;
        document.body.appendChild(modalElement.firstElementChild);

        // Store reference
        this.modals.set(id, document.getElementById(`modal_${id}`));

        // Store button actions for this modal
        this.modalButtons = this.modalButtons || new Map();
        this.modalButtons.set(id, modal.buttons);

        // Focus first input
        setTimeout(() => {
            const firstInput = document.querySelector(`#modal_${id} input, #modal_${id} textarea, #modal_${id} select`);
            if (firstInput) firstInput.focus();
        }, 100);
    }

    /**
     * Handle button click in modal
     */
    handleButtonClick(modalId, buttonIndex) {
        const buttons = this.modalButtons.get(modalId);
        if (buttons && buttons[buttonIndex] && buttons[buttonIndex].action) {
            buttons[buttonIndex].action();
        }
    }

    /**
     * Close modal
     */
    closeModal(id) {
        const modal = this.modals.get(id);
        if (modal) {
            modal.remove();
            this.modals.delete(id);
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#e7f3ff'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#b3d9ff'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0066cc'};
        `;

        notification.querySelector('.notification-content').style.cssText = `
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        notification.querySelector('.notification-close').style.cssText = `
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            margin-left: auto;
            padding: 0;
            font-size: 1.2rem;
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Add CSS for modals and notifications
const modalStyles = `
<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.modal-dialog {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #1a202c;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-form .form-group {
    margin-bottom: 20px;
}

.modal-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #374151;
}

.modal-form .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.modal-form .form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.info-box, .cash-info, .warning-message {
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.info-box {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    color: #0066cc;
}

.cash-info {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    color: #0369a1;
}

.warning-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.ebd-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.ebd-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.profit-positive {
    color: #28a745;
}

.profit-negative {
    color: #dc3545;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-warning {
    background: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}
</style>
`;

// Add styles to head
if (!document.querySelector('#businessDayStyles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'businessDayStyles';
    styleElement.innerHTML = modalStyles;
    document.head.appendChild(styleElement);
}

// Export for global use
window.BusinessDayManager = BusinessDayManager;
