/**
 * Zaiqa Business Day Manager - Manual Day Transition Control
 * Prevents automatic data clearing and provides manual day transition controls
 */

class ZaiqaBusinessDayManager {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        this.autoTransitionDisabled = true; // Disable automatic transitions by default
        
        this.init();
    }

    /**
     * Initialize the business day manager
     */
    init() {
        try {
            console.log('📅 Initializing Business Day Manager v' + this.version);
            
            // Disable any existing automatic date transitions
            this.disableAutomaticTransitions();
            
            // Set up manual controls
            this.setupManualControls();
            
            // Check if we need to show day transition prompt
            this.checkForPendingDayTransition();
            
            this.initialized = true;
            console.log('✅ Business Day Manager initialized - Automatic transitions DISABLED');
            
        } catch (error) {
            console.error('❌ Business Day Manager initialization failed:', error);
        }
    }

    /**
     * Disable automatic date transitions throughout the system
     */
    disableAutomaticTransitions() {
        try {
            // Clear any existing intervals that might trigger automatic transitions
            if (window.businessDayInterval) {
                clearInterval(window.businessDayInterval);
                window.businessDayInterval = null;
            }
            
            // Override any automatic date checking functions
            if (window.app && typeof window.app.checkBusinessDayTransition === 'function') {
                window.app.originalCheckBusinessDayTransition = window.app.checkBusinessDayTransition;
                window.app.checkBusinessDayTransition = function() {
                    console.log('📅 Automatic business day transition disabled - manual control required');
                    return false;
                };
            }
            
            // Disable automatic midnight resets
            if (window.app && typeof window.app.handleMidnightReset === 'function') {
                window.app.originalHandleMidnightReset = window.app.handleMidnightReset;
                window.app.handleMidnightReset = function() {
                    console.log('📅 Automatic midnight reset disabled - manual control required');
                    return false;
                };
            }
            
            console.log('✅ Automatic date transitions disabled');
            
        } catch (error) {
            console.error('❌ Failed to disable automatic transitions:', error);
        }
    }

    /**
     * Set up manual day transition controls
     */
    setupManualControls() {
        try {
            // Add manual day transition button to the dashboard
            this.addDayTransitionButton();
            
            // Set up keyboard shortcut (Ctrl+Shift+D)
            document.addEventListener('keydown', (event) => {
                if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                    event.preventDefault();
                    this.showDayTransitionModal();
                }
            });
            
            console.log('✅ Manual day transition controls set up');
            
        } catch (error) {
            console.error('❌ Failed to set up manual controls:', error);
        }
    }

    /**
     * Add day transition button to dashboard
     */
    addDayTransitionButton() {
        try {
            // Find the dashboard header or appropriate location
            const dashboardHeader = document.querySelector('.dashboard-header') || 
                                  document.querySelector('.page-header') ||
                                  document.querySelector('.header-actions');
            
            if (dashboardHeader) {
                const dayTransitionBtn = document.createElement('button');
                dayTransitionBtn.className = 'btn btn-warning day-transition-btn';
                dayTransitionBtn.innerHTML = `
                    <i class="fas fa-calendar-check"></i>
                    End Business Day
                `;
                dayTransitionBtn.onclick = () => this.showDayTransitionModal();
                dayTransitionBtn.title = 'Manually transition to next business day (Ctrl+Shift+D)';
                
                dashboardHeader.appendChild(dayTransitionBtn);
                console.log('✅ Day transition button added to dashboard');
            }
            
        } catch (error) {
            console.error('❌ Failed to add day transition button:', error);
        }
    }

    /**
     * Check if there's a pending day transition that needs user attention
     */
    checkForPendingDayTransition() {
        try {
            const lastBusinessDate = localStorage.getItem('lastBusinessDate');
            const currentDate = new Date().toISOString().split('T')[0];
            
            if (lastBusinessDate && lastBusinessDate !== currentDate) {
                // Show notification that a new business day is available
                this.showNewDayNotification(lastBusinessDate, currentDate);
            } else if (!lastBusinessDate) {
                // First time setup
                localStorage.setItem('lastBusinessDate', currentDate);
            }
            
        } catch (error) {
            console.error('❌ Failed to check pending day transition:', error);
        }
    }

    /**
     * Show notification about new business day availability
     */
    showNewDayNotification(lastDate, currentDate) {
        try {
            const notification = document.createElement('div');
            notification.className = 'day-transition-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="notification-text">
                        <h4>New Business Day Available</h4>
                        <p>Last business day: ${lastDate}</p>
                        <p>Current date: ${currentDate}</p>
                        <p>Click "End Business Day" when ready to transition.</p>
                    </div>
                    <div class="notification-actions">
                        <button onclick="this.parentElement.parentElement.parentElement.remove()" class="btn btn-sm btn-secondary">
                            Dismiss
                        </button>
                        <button onclick="window.businessDayManager.showDayTransitionModal()" class="btn btn-sm btn-primary">
                            Transition Now
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-dismiss after 30 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 30000);
            
        } catch (error) {
            console.error('❌ Failed to show new day notification:', error);
        }
    }

    /**
     * Show day transition modal with options
     */
    showDayTransitionModal() {
        try {
            const currentDate = new Date().toISOString().split('T')[0];
            const lastBusinessDate = localStorage.getItem('lastBusinessDate') || currentDate;
            
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content day-transition-modal">
                    <div class="modal-header">
                        <h2><i class="fas fa-calendar-check"></i> End Business Day</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="day-transition-info">
                            <div class="info-item">
                                <label>Current Business Date:</label>
                                <span class="current-date">${lastBusinessDate}</span>
                            </div>
                            <div class="info-item">
                                <label>Transition to Date:</label>
                                <input type="date" id="transitionDate" value="${currentDate}" class="form-control">
                            </div>
                        </div>
                        
                        <div class="transition-options">
                            <h3>Transition Options</h3>
                            
                            <div class="option-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="createBackup" checked>
                                    Create backup before transition
                                </label>
                            </div>
                            
                            <div class="option-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="generateReport">
                                    Generate end-of-day report
                                </label>
                            </div>
                            
                            <div class="option-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="clearCashRegister">
                                    Reset cash register for new day
                                </label>
                            </div>
                            
                            <div class="option-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="archiveOrders">
                                    Archive completed orders
                                </label>
                            </div>
                        </div>
                        
                        <div class="warning-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Important:</strong> This action will transition to a new business day. 
                            Make sure all today's transactions are complete before proceeding.
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button onclick="window.businessDayManager.executeTransition()" class="btn btn-warning">
                            <i class="fas fa-calendar-check"></i>
                            End Business Day
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
        } catch (error) {
            console.error('❌ Failed to show day transition modal:', error);
        }
    }

    /**
     * Execute the business day transition
     */
    executeTransition() {
        try {
            const transitionDate = document.getElementById('transitionDate')?.value;
            const createBackup = document.getElementById('createBackup')?.checked;
            const generateReport = document.getElementById('generateReport')?.checked;
            const clearCashRegister = document.getElementById('clearCashRegister')?.checked;
            const archiveOrders = document.getElementById('archiveOrders')?.checked;
            
            if (!transitionDate) {
                alert('Please select a transition date.');
                return;
            }
            
            // Show confirmation dialog
            const confirmed = confirm(`Are you sure you want to end the current business day and transition to ${transitionDate}?`);
            if (!confirmed) {
                return;
            }
            
            console.log('📅 Executing business day transition...');
            
            // Create backup if requested
            if (createBackup) {
                this.createDayEndBackup();
            }
            
            // Generate report if requested
            if (generateReport) {
                this.generateEndOfDayReport();
            }
            
            // Clear cash register if requested
            if (clearCashRegister) {
                this.resetCashRegister(transitionDate);
            }
            
            // Archive orders if requested
            if (archiveOrders) {
                this.archiveCompletedOrders();
            }
            
            // Update the business date
            localStorage.setItem('lastBusinessDate', transitionDate);
            
            // Close modal
            document.querySelector('.modal-overlay')?.remove();
            
            // Show success notification
            this.showTransitionSuccessNotification(transitionDate);
            
            // Refresh the system
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                window.app.updateDashboardStats();
            }
            
            console.log('✅ Business day transition completed successfully');
            
        } catch (error) {
            console.error('❌ Failed to execute day transition:', error);
            alert('Failed to execute day transition. Please try again.');
        }
    }

    /**
     * Create backup before day transition
     */
    createDayEndBackup() {
        try {
            const backupData = {
                date: new Date().toISOString(),
                businessDate: localStorage.getItem('lastBusinessDate'),
                orders: JSON.parse(localStorage.getItem('restaurantOrders') || '[]'),
                expenses: JSON.parse(localStorage.getItem('expenses') || '[]'),
                cashRegister: JSON.parse(localStorage.getItem('cashRegister') || '[]'),
                udhars: JSON.parse(localStorage.getItem('udhars') || '[]'),
                inventory: JSON.parse(localStorage.getItem('inventory') || '[]'),
                menuItems: JSON.parse(localStorage.getItem('menuItems') || '[]')
            };
            
            const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-day-end-backup-${backupData.businessDate}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log('✅ Day-end backup created');
            
        } catch (error) {
            console.error('❌ Failed to create day-end backup:', error);
        }
    }

    /**
     * Generate end-of-day report
     */
    generateEndOfDayReport() {
        try {
            // This would integrate with the reports system
            if (window.zaiqaReports && typeof window.zaiqaReports.generateDailyReport === 'function') {
                window.zaiqaReports.generateDailyReport();
            }
            
            console.log('✅ End-of-day report generated');
            
        } catch (error) {
            console.error('❌ Failed to generate end-of-day report:', error);
        }
    }

    /**
     * Reset cash register for new day
     */
    resetCashRegister(newDate) {
        try {
            const cashRegister = JSON.parse(localStorage.getItem('cashRegister') || '[]');
            
            // Add new entry for the new date
            const newEntry = {
                date: newDate,
                morningBalance: 0,
                eveningBalance: 0,
                totalSales: 0,
                totalExpenses: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            cashRegister.push(newEntry);
            localStorage.setItem('cashRegister', JSON.stringify(cashRegister));
            
            console.log('✅ Cash register reset for new day');
            
        } catch (error) {
            console.error('❌ Failed to reset cash register:', error);
        }
    }

    /**
     * Archive completed orders
     */
    archiveCompletedOrders() {
        try {
            const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
            const archivedOrders = JSON.parse(localStorage.getItem('archivedOrders') || '[]');
            
            const completedOrders = orders.filter(order => order.status === 'completed');
            const activeOrders = orders.filter(order => order.status !== 'completed');
            
            // Move completed orders to archive
            archivedOrders.push(...completedOrders);
            
            // Keep only active orders in main storage
            localStorage.setItem('restaurantOrders', JSON.stringify(activeOrders));
            localStorage.setItem('archivedOrders', JSON.stringify(archivedOrders));
            
            console.log(`✅ Archived ${completedOrders.length} completed orders`);
            
        } catch (error) {
            console.error('❌ Failed to archive orders:', error);
        }
    }

    /**
     * Show transition success notification
     */
    showTransitionSuccessNotification(newDate) {
        try {
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(
                    `Business day successfully transitioned to ${newDate}!`, 
                    'success'
                );
            } else {
                alert(`Business day successfully transitioned to ${newDate}!`);
            }
            
        } catch (error) {
            console.error('❌ Failed to show success notification:', error);
        }
    }

    /**
     * Get current business date
     */
    getCurrentBusinessDate() {
        return localStorage.getItem('lastBusinessDate') || new Date().toISOString().split('T')[0];
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize the business day manager
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.businessDayManager = new ZaiqaBusinessDayManager();
    }, 1000);
});

// Export for global use
window.ZaiqaBusinessDayManager = ZaiqaBusinessDayManager;
