/**
 * Critical Fixes Test Suite
 * Tests all the fixes implemented for the restaurant POS system
 */

class CriticalFixesTest {
    constructor() {
        this.testResults = {};
        this.version = '1.0.0';
    }

    /**
     * Run all critical fixes tests
     */
    async runAllTests() {
        console.log('🧪 Starting Critical Fixes Test Suite v' + this.version);
        console.log('=' .repeat(60));

        try {
            // Test 1: Cash in Hand Card in Reports
            await this.testCashInHandCard();

            // Test 2: Bill Deletion Process
            await this.testBillDeletion();

            // Test 3: Date Filter Controls in Reports
            await this.testDateFilterControls();

            // Test 4: Business Date Transition Feature
            await this.testBusinessDateTransition();

            // Generate test report
            this.generateTestReport();

        } catch (error) {
            console.error('❌ Test suite failed:', error);
        }
    }

    /**
     * Test 1: Cash in Hand Card in Reports Page
     */
    async testCashInHandCard() {
        console.log('\n1️⃣ Testing Cash in Hand Card in Reports Page...');
        
        try {
            // Check if enhanced reports page is available
            const reportsAvailable = typeof window.enhancedReportsPage !== 'undefined';
            console.log('📊 Enhanced reports page available:', reportsAvailable);

            // Check if cash calculation methods are available
            const appCashMethod = window.app && typeof window.app.calculateCashInHandData === 'function';
            const localStorageCash = localStorage.getItem('currentCashInHand');
            const cashManagerMethod = window.cashManager && typeof window.cashManager.getCurrentCash === 'function';

            console.log('💰 Cash calculation methods:');
            console.log('  - App method available:', appCashMethod);
            console.log('  - LocalStorage cash:', localStorageCash);
            console.log('  - Cash manager method:', cashManagerMethod);

            // Test cash calculation
            let cashAmount = 0;
            if (appCashMethod) {
                try {
                    const cashData = window.app.calculateCashInHandData();
                    cashAmount = cashData.currentBalance || 0;
                    console.log('💰 App cash calculation result:', cashAmount);
                } catch (error) {
                    console.log('⚠️ App cash calculation failed:', error.message);
                }
            }

            if (cashAmount === 0 && localStorageCash) {
                cashAmount = parseFloat(localStorageCash);
                console.log('💰 Using localStorage cash:', cashAmount);
            }

            this.testResults.cashInHandCard = {
                passed: cashAmount > 0,
                amount: cashAmount,
                methods: {
                    appMethod: appCashMethod,
                    localStorage: !!localStorageCash,
                    cashManager: cashManagerMethod
                }
            };

            console.log('✅ Cash in Hand Card test completed');

        } catch (error) {
            console.error('❌ Cash in Hand Card test failed:', error);
            this.testResults.cashInHandCard = { passed: false, error: error.message };
        }
    }

    /**
     * Test 2: Bill Deletion Process
     */
    async testBillDeletion() {
        console.log('\n2️⃣ Testing Bill Deletion Process...');
        
        try {
            // Check if delete bill function exists
            const deleteBillExists = window.app && typeof window.app.deleteBill === 'function';
            console.log('🗑️ Delete bill function exists:', deleteBillExists);

            // Check if refresh functions exist
            const refreshBillsExists = window.app && typeof window.app.refreshBillsList === 'function';
            const refreshAllDisplaysExists = window.app && typeof window.app.refreshAllDisplaysAfterOrderDeletion === 'function';

            console.log('🔄 Refresh functions:');
            console.log('  - refreshBillsList:', refreshBillsExists);
            console.log('  - refreshAllDisplaysAfterOrderDeletion:', refreshAllDisplaysExists);

            // Check if bills have proper data attributes
            const billElements = document.querySelectorAll('.bill-card[data-order-id]');
            console.log('📋 Bills with data-order-id attribute:', billElements.length);

            this.testResults.billDeletion = {
                passed: deleteBillExists && refreshBillsExists && refreshAllDisplaysExists,
                functions: {
                    deleteBill: deleteBillExists,
                    refreshBills: refreshBillsExists,
                    refreshAllDisplays: refreshAllDisplaysExists
                },
                billsWithAttributes: billElements.length
            };

            console.log('✅ Bill Deletion test completed');

        } catch (error) {
            console.error('❌ Bill Deletion test failed:', error);
            this.testResults.billDeletion = { passed: false, error: error.message };
        }
    }

    /**
     * Test 3: Date Filter Controls in Reports Page
     */
    async testDateFilterControls() {
        console.log('\n3️⃣ Testing Date Filter Controls in Reports Page...');
        
        try {
            // Check if enhanced reports page is available
            const reportsAvailable = typeof window.enhancedReportsPage !== 'undefined';
            console.log('📊 Enhanced reports page available:', reportsAvailable);

            if (reportsAvailable) {
                // Test date range calculation
                const todayRange = window.enhancedReportsPage.getDateRange('today');
                const yesterdayRange = window.enhancedReportsPage.getDateRange('yesterday');

                console.log('📅 Date range calculations:');
                console.log('  - Today:', todayRange);
                console.log('  - Yesterday:', yesterdayRange);

                // Verify date ranges are different
                const rangesAreDifferent = todayRange.start.getTime() !== yesterdayRange.start.getTime();
                console.log('📅 Date ranges are properly different:', rangesAreDifferent);

                this.testResults.dateFilterControls = {
                    passed: reportsAvailable && rangesAreDifferent,
                    reportsAvailable: reportsAvailable,
                    rangesAreDifferent: rangesAreDifferent,
                    todayRange: todayRange,
                    yesterdayRange: yesterdayRange
                };
            } else {
                this.testResults.dateFilterControls = {
                    passed: false,
                    error: 'Enhanced reports page not available'
                };
            }

            console.log('✅ Date Filter Controls test completed');

        } catch (error) {
            console.error('❌ Date Filter Controls test failed:', error);
            this.testResults.dateFilterControls = { passed: false, error: error.message };
        }
    }

    /**
     * Test 4: Business Date Transition Feature
     */
    async testBusinessDateTransition() {
        console.log('\n4️⃣ Testing Business Date Transition Feature...');
        
        try {
            // Check if business day manager is available
            const businessDayManagerExists = typeof window.businessDayManager !== 'undefined';
            console.log('📅 Business Day Manager exists:', businessDayManagerExists);

            if (businessDayManagerExists) {
                // Check if manager is initialized
                const isInitialized = window.businessDayManager.isInitialized();
                console.log('📅 Business Day Manager initialized:', isInitialized);

                // Check key functions
                const hasTransitionModal = typeof window.businessDayManager.showDayTransitionModal === 'function';
                const hasExecuteTransition = typeof window.businessDayManager.executeTransition === 'function';
                const hasCurrentDate = typeof window.businessDayManager.getCurrentBusinessDate === 'function';

                console.log('📅 Business Day Manager functions:');
                console.log('  - showDayTransitionModal:', hasTransitionModal);
                console.log('  - executeTransition:', hasExecuteTransition);
                console.log('  - getCurrentBusinessDate:', hasCurrentDate);

                // Get current business date
                let currentBusinessDate = null;
                if (hasCurrentDate) {
                    currentBusinessDate = window.businessDayManager.getCurrentBusinessDate();
                    console.log('📅 Current business date:', currentBusinessDate);
                }

                this.testResults.businessDateTransition = {
                    passed: businessDayManagerExists && isInitialized && hasTransitionModal && hasExecuteTransition,
                    managerExists: businessDayManagerExists,
                    isInitialized: isInitialized,
                    functions: {
                        transitionModal: hasTransitionModal,
                        executeTransition: hasExecuteTransition,
                        getCurrentDate: hasCurrentDate
                    },
                    currentBusinessDate: currentBusinessDate
                };
            } else {
                this.testResults.businessDateTransition = {
                    passed: false,
                    error: 'Business Day Manager not available'
                };
            }

            console.log('✅ Business Date Transition test completed');

        } catch (error) {
            console.error('❌ Business Date Transition test failed:', error);
            this.testResults.businessDateTransition = { passed: false, error: error.message };
        }
    }

    /**
     * Generate comprehensive test report
     */
    generateTestReport() {
        console.log('\n📋 CRITICAL FIXES TEST REPORT');
        console.log('=' .repeat(60));

        const tests = [
            { name: 'Cash in Hand Card', key: 'cashInHandCard' },
            { name: 'Bill Deletion Process', key: 'billDeletion' },
            { name: 'Date Filter Controls', key: 'dateFilterControls' },
            { name: 'Business Date Transition', key: 'businessDateTransition' }
        ];

        let passedTests = 0;
        let totalTests = tests.length;

        tests.forEach((test, index) => {
            const result = this.testResults[test.key];
            const status = result && result.passed ? '✅ PASSED' : '❌ FAILED';
            
            if (result && result.passed) {
                passedTests++;
            }

            console.log(`${index + 1}. ${test.name}: ${status}`);
            
            if (result && result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });

        console.log('\n📊 SUMMARY:');
        console.log(`Tests Passed: ${passedTests}/${totalTests}`);
        console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

        if (passedTests === totalTests) {
            console.log('🎉 All critical fixes are working correctly!');
        } else {
            console.log('⚠️ Some fixes need attention. Check the detailed results above.');
        }

        console.log('\n📋 Detailed Results:');
        console.log(JSON.stringify(this.testResults, null, 2));

        return {
            passed: passedTests,
            total: totalTests,
            successRate: Math.round((passedTests / totalTests) * 100),
            results: this.testResults
        };
    }
}

// Export for global use
window.CriticalFixesTest = CriticalFixesTest;

// Auto-run test if in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // Run tests after page load
    window.addEventListener('load', () => {
        setTimeout(() => {
            console.log('🧪 Auto-running Critical Fixes Test Suite...');
            const testSuite = new CriticalFixesTest();
            testSuite.runAllTests();
        }, 3000);
    });
}

// Manual test function
window.testCriticalFixes = function() {
    const testSuite = new CriticalFixesTest();
    return testSuite.runAllTests();
};
