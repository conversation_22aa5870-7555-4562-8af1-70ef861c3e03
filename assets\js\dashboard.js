// Dashboard specific functionality
class DashboardManager {
    constructor() {
        this.init();
    }

    init() {
        this.updateDashboardStats();
        this.updateRecentOrders();
        this.startRealTimeUpdates();
    }

    updateDashboardStats() {
        // Get real data from localStorage
        const orders = this.getOrders();
        const tables = this.getTables();

        // Calculate today's orders and revenue
        const today = new Date().toDateString();
        const todayOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.toDateString() === today;
        });

        const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const activeOrders = orders.filter(order => ['pending', 'confirmed', 'preparing'].includes(order.status)).length;
        const avgOrderValue = todayOrders.length > 0 ? Math.round(todayRevenue / todayOrders.length) : 0;

        // Calculate table availability
        const occupiedTables = tables.filter(table => table.status === 'occupied').length;
        const availableTables = tables.length - occupiedTables;

        // Update dashboard elements
        const revenueElement = document.getElementById('todayRevenue');
        const ordersElement = document.getElementById('todayOrders');
        const activeElement = document.getElementById('activeOrders');
        const avgElement = document.getElementById('avgOrderValue');
        const tableElement = document.getElementById('tableStatus');

        if (revenueElement) revenueElement.textContent = this.formatCurrency(todayRevenue);
        if (ordersElement) ordersElement.textContent = todayOrders.length.toString();
        if (activeElement) activeElement.textContent = `${activeOrders} active`;
        if (avgElement) avgElement.textContent = this.formatCurrency(avgOrderValue);
        if (tableElement) tableElement.textContent = `${availableTables}/${tables.length}`;
    }

    formatCurrency(amount) {
        // Handle null, undefined, or invalid amounts
        if (amount === null || amount === undefined || isNaN(amount)) {
            return 'PKR 0';
        }

        // Ensure amount is a number
        const numAmount = Number(amount);
        if (isNaN(numAmount)) {
            return 'PKR 0';
        }

        return `PKR ${numAmount.toLocaleString()}`;
    }

    getOrders() {
        const saved = localStorage.getItem('restaurantOrders');
        return saved ? JSON.parse(saved) : [];
    }

    getTables() {
        const saved = localStorage.getItem('restaurantTables');
        if (saved) {
            return JSON.parse(saved);
        }

        // Initialize default tables if none exist
        const defaultTables = [];
        for (let i = 1; i <= 20; i++) {
            defaultTables.push({
                id: i.toString(),
                number: `T-${i.toString().padStart(2, '0')}`,
                capacity: i <= 10 ? 4 : 6, // First 10 tables have 4 seats, rest have 6
                status: 'available', // available, occupied, reserved, cleaning
                currentOrder: null,
                customers: 0,
                position: { x: (i - 1) % 5, y: Math.floor((i - 1) / 5) }
            });
        }

        localStorage.setItem('restaurantTables', JSON.stringify(defaultTables));
        return defaultTables;
    }

    updateRecentOrders() {
        const ordersList = document.getElementById('recentOrdersList');
        if (!ordersList) return;

        const orders = this.getOrders();
        const today = new Date().toDateString();
        const todayOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.toDateString() === today;
        }).slice(-5).reverse(); // Get last 5 orders and reverse to show newest first

        if (todayOrders.length === 0) {
            ordersList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <p>No orders yet today</p>
                    <p style="font-size: 0.875rem; color: var(--gray-500);">Orders will appear here as they are created</p>
                </div>
            `;
            return;
        }

        ordersList.innerHTML = todayOrders.map(order => {
            const timeAgo = this.getTimeAgo(new Date(order.created_at));
            // Ensure order.total_amount is safe before formatting
            const safeAmount = order.total_amount || 0;
            return `
                <div class="order-item">
                    <div class="order-info">
                        <span class="order-id">${order.order_number || 'N/A'}</span>
                        <span class="order-table">${order.table_number || 'Takeaway'} • ${order.items?.length || 0} items</span>
                    </div>
                    <div class="order-status">
                        <span class="status-badge ${order.status || 'pending'}">${order.status || 'pending'}</span>
                        <span class="order-amount">${this.formatCurrency(safeAmount)}</span>
                        <span class="order-time">${timeAgo}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    getTimeAgo(date) {
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes} min${diffInMinutes > 1 ? 's' : ''} ago`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    startRealTimeUpdates() {
        // Update dashboard every 30 seconds
        setInterval(() => {
            this.updateDashboardStats();
            this.updateRecentOrders();
        }, 30000);
    }

    refreshDashboard() {
        this.updateDashboardStats();
        this.updateRecentOrders();

        // Show notification
        if (window.app) {
            app.showNotification('Dashboard refreshed successfully', 'success');
        }
    }
}

// Initialize dashboard manager
const dashboardManager = new DashboardManager();
