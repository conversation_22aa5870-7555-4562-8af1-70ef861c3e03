/**
 * Database Connection Manager
 * Handles all database operations for the restaurant management system
 */

class DatabaseManager {
    constructor() {
        this.baseUrl = this.getBaseUrl();
        this.apiEndpoint = `${this.baseUrl}/api`;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        
        console.log('🗄️ Database Manager initialized with endpoint:', this.apiEndpoint);
    }

    /**
     * Get base URL for API calls
     */
    getBaseUrl() {
        // Auto-detect base URL based on current location
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        const port = window.location.port;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            // Local development
            return `${protocol}//${hostname}${port ? ':' + port : ''}`;
        } else {
            // Production
            return `${protocol}//${hostname}`;
        }
    }

    /**
     * Make API request with error handling
     */
    async makeRequest(endpoint, options = {}) {
        try {
            const url = `${this.apiEndpoint}/${endpoint}`;
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            const requestOptions = { ...defaultOptions, ...options };
            
            console.log(`🌐 Making ${requestOptions.method} request to:`, url);
            
            const response = await fetch(url, requestOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`✅ Request successful:`, data);
            
            return data;
        } catch (error) {
            console.error('❌ Database request failed:', error);

            // Fallback to localStorage if database is unavailable
            console.log('🔄 Falling back to localStorage...');
            const fallbackResult = this.fallbackToLocalStorage(endpoint, options);
            console.log('📦 Fallback result:', fallbackResult);
            return fallbackResult;
        }
    }

    /**
     * Fallback to localStorage when database is unavailable
     */
    fallbackToLocalStorage(endpoint, options) {
        console.log('📦 Using localStorage fallback for:', endpoint);

        const method = options.method || 'GET';

        // Extract base endpoint without query parameters
        const baseEndpoint = endpoint.split('?')[0];
        console.log('📦 Base endpoint:', baseEndpoint);

        try {
            switch (baseEndpoint) {
                case 'orders':
                    if (method === 'GET') {
                        let orders = [];
                        try {
                            const ordersData = localStorage.getItem('orders');
                            orders = ordersData ? JSON.parse(ordersData) : [];

                            // Ensure it's an array
                            if (!Array.isArray(orders)) {
                                console.warn('📦 localStorage orders is not an array, using empty array');
                                orders = [];
                            }
                        } catch (parseError) {
                            console.error('📦 Error parsing localStorage orders:', parseError);
                            orders = [];
                        }

                        console.log('📦 Fallback returning', orders.length, 'orders');
                        return { success: true, data: orders };

                    } else if (method === 'POST') {
                        try {
                            const orders = JSON.parse(localStorage.getItem('orders') || '[]');
                            const newOrder = JSON.parse(options.body);
                            newOrder.id = 'order_' + Date.now();
                            orders.push(newOrder);
                            localStorage.setItem('orders', JSON.stringify(orders));
                            return { success: true, data: newOrder };
                        } catch (error) {
                            console.error('📦 Error creating order in localStorage:', error);
                            return { success: false, error: error.message, data: [] };
                        }
                    }
                    break;
                
            case 'expenses':
                if (method === 'GET') {
                    let expenses = [];
                    try {
                        const expensesData = localStorage.getItem('expenses');
                        expenses = expensesData ? JSON.parse(expensesData) : [];

                        // Ensure it's an array
                        if (!Array.isArray(expenses)) {
                            console.warn('📦 localStorage expenses is not an array, using empty array');
                            expenses = [];
                        }
                    } catch (parseError) {
                        console.error('📦 Error parsing localStorage expenses:', parseError);
                        expenses = [];
                    }

                    console.log('📦 Fallback returning', expenses.length, 'expenses');
                    return { success: true, data: expenses };

                } else if (method === 'POST') {
                    try {
                        const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                        const newExpense = JSON.parse(options.body);
                        newExpense.id = 'expense_' + Date.now();
                        expenses.push(newExpense);
                        localStorage.setItem('expenses', JSON.stringify(expenses));
                        return { success: true, data: newExpense };
                    } catch (error) {
                        console.error('📦 Error creating expense in localStorage:', error);
                        return { success: false, error: error.message, data: [] };
                    }
                }
                break;
                
            case 'menu-items':
                return { success: true, data: JSON.parse(localStorage.getItem('menuItems') || '[]') };
                
            case 'inventory':
                return { success: true, data: JSON.parse(localStorage.getItem('inventory') || '[]') };
                
            case 'business-days':
                if (method === 'GET') {
                    return { success: true, data: JSON.parse(localStorage.getItem('businessDays') || '[]') };
                } else if (method === 'POST') {
                    const businessDays = JSON.parse(localStorage.getItem('businessDays') || '[]');
                    const newDay = JSON.parse(options.body);
                    newDay.id = 'day_' + Date.now();
                    businessDays.push(newDay);
                    localStorage.setItem('businessDays', JSON.stringify(businessDays));
                    return { success: true, data: newDay };
                }
                break;
                
            default:
                console.warn('📦 Unknown endpoint for fallback:', baseEndpoint);
                return { success: false, error: 'Unknown endpoint', data: [] };
        }
        } catch (fallbackError) {
            console.error('📦 Critical error in fallback:', fallbackError);
            return { success: false, error: fallbackError.message, data: [] };
        }
    }

    /**
     * Get orders from database
     */
    async getOrders(filters = {}) {
        const cacheKey = `orders_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const queryParams = new URLSearchParams();
        if (filters.startDate) queryParams.append('start_date', filters.startDate);
        if (filters.endDate) queryParams.append('end_date', filters.endDate);
        if (filters.status) queryParams.append('status', filters.status);
        if (filters.limit) queryParams.append('limit', filters.limit);

        const endpoint = `orders${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
        const result = await this.makeRequest(endpoint);
        
        if (result && result.success && Array.isArray(result.data)) {
            this.setCached(cacheKey, result.data);
            console.log('✅ Database orders retrieved successfully:', result.data.length);
            return result.data;
        }

        console.warn('⚠️ Invalid database response for orders:', result);
        console.log('🔄 Returning empty array as fallback');
        return [];
    }

    /**
     * Get expenses from database
     */
    async getExpenses(filters = {}) {
        const cacheKey = `expenses_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const queryParams = new URLSearchParams();
        if (filters.startDate) queryParams.append('start_date', filters.startDate);
        if (filters.endDate) queryParams.append('end_date', filters.endDate);
        if (filters.category) queryParams.append('category', filters.category);
        if (filters.limit) queryParams.append('limit', filters.limit);

        const endpoint = `expenses${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
        const result = await this.makeRequest(endpoint);
        
        if (result && result.success && Array.isArray(result.data)) {
            this.setCached(cacheKey, result.data);
            return result.data;
        }

        console.warn('⚠️ Invalid database response for expenses:', result);
        return [];
    }

    /**
     * Get menu items from database
     */
    async getMenuItems() {
        const cached = this.getCached('menuItems');
        if (cached) return cached;

        const result = await this.makeRequest('menu-items');
        
        if (result && result.success && Array.isArray(result.data)) {
            this.setCached('menuItems', result.data);
            return result.data;
        }

        console.warn('⚠️ Invalid database response for menu items:', result);
        return [];
    }

    /**
     * Get inventory from database
     */
    async getInventory() {
        const cached = this.getCached('inventory');
        if (cached) return cached;

        const result = await this.makeRequest('inventory');
        
        if (result && result.success && Array.isArray(result.data)) {
            this.setCached('inventory', result.data);
            return result.data;
        }

        console.warn('⚠️ Invalid database response for inventory:', result);
        return [];
    }

    /**
     * Get business days from database
     */
    async getBusinessDays(filters = {}) {
        const cacheKey = `businessDays_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const queryParams = new URLSearchParams();
        if (filters.startDate) queryParams.append('start_date', filters.startDate);
        if (filters.endDate) queryParams.append('end_date', filters.endDate);
        if (filters.limit) queryParams.append('limit', filters.limit);

        const endpoint = `business-days${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
        const result = await this.makeRequest(endpoint);
        
        if (result && result.success && Array.isArray(result.data)) {
            this.setCached(cacheKey, result.data);
            return result.data;
        }

        console.warn('⚠️ Invalid database response for business days:', result);
        return [];
    }

    /**
     * Create new order
     */
    async createOrder(orderData) {
        const result = await this.makeRequest('orders', {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
        
        if (result.success) {
            this.clearCache(); // Clear cache to force refresh
            return result.data;
        }
        
        throw new Error(result.error || 'Failed to create order');
    }

    /**
     * Create new expense
     */
    async createExpense(expenseData) {
        const result = await this.makeRequest('expenses', {
            method: 'POST',
            body: JSON.stringify(expenseData)
        });
        
        if (result.success) {
            this.clearCache(); // Clear cache to force refresh
            return result.data;
        }
        
        throw new Error(result.error || 'Failed to create expense');
    }

    /**
     * Create business day record
     */
    async createBusinessDay(dayData) {
        const result = await this.makeRequest('business-days', {
            method: 'POST',
            body: JSON.stringify(dayData)
        });
        
        if (result.success) {
            this.clearCache(); // Clear cache to force refresh
            return result.data;
        }
        
        throw new Error(result.error || 'Failed to create business day');
    }

    /**
     * Cache management
     */
    getCached(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            console.log('📋 Using cached data for:', key);
            return cached.data;
        }
        return null;
    }

    setCached(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    clearCache() {
        this.cache.clear();
        console.log('🗑️ Cache cleared');
    }
}

// Initialize global database manager
window.dbManager = new DatabaseManager();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseManager;
}
