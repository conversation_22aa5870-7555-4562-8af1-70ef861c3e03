/**
 * Enhanced Reports Page - Comprehensive Business Analytics
 * Features: Real-time data, End Business Day, Morning Balance, Owner <PERSON>,
 * Sales Analytics, Expense Analysis, Inventory Analytics, Cash Management
 */

class EnhancedReportsPage {
    constructor() {
        this.currentDateRange = 'today';
        this.charts = {};
        this.isInitialized = false;
        this.refreshInterval = null;
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }

    /**
     * Initialize the reports page
     */
    initialize() {
        try {
            console.log('🚀 Initializing Enhanced Reports Page...');
            
            // Check dependencies
            if (!this.checkDependencies()) {
                this.showError('Required dependencies not found. Please refresh the page.');
                return;
            }

            // Create page structure
            this.createPageStructure();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Load initial data
            this.loadAllReports();
            
            // Set up auto-refresh
            this.setupAutoRefresh();
            
            this.isInitialized = true;
            console.log('✅ Enhanced Reports Page initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Enhanced Reports Page:', error);
            this.showError('Failed to initialize reports page: ' + error.message);
        }
    }

    /**
     * Check if all required dependencies are available
     */
    checkDependencies() {
        const required = [
            'ZaiqaFinancialEngine',
            'ZaiqaDatabaseManager',
            'ZaiqaFinancialCalculations'
        ];

        // Check if classes are available
        for (const dep of required) {
            if (!window[dep]) {
                console.error(`❌ Missing dependency class: ${dep}`);
                return false;
            }
        }

        // Check if ZaiqaSystem is available and initialized
        if (!window.zaiqaSystem || !window.zaiqaSystem.initialized) {
            console.warn('⚠️ ZaiqaSystem not initialized, will create instances manually');
        }

        // Ensure we have sample data to prevent errors
        this.ensureSampleData();

        console.log('✅ All dependencies available');
        return true;
    }

    /**
     * Get or create financial calculation instance
     */
    getFinancialCalculations() {
        try {
            console.log('🔧 Getting financial calculations instance...');

            // Try to use the system instance first
            if (window.zaiqaSystem && window.zaiqaSystem.financialCalculations) {
                console.log('✅ Using system financial calculations');
                return window.zaiqaSystem.financialCalculations;
            }

            // Check if classes are available
            if (!window.ZaiqaFinancialEngine) {
                console.error('❌ ZaiqaFinancialEngine not available');
                throw new Error('ZaiqaFinancialEngine not available');
            }

            if (!window.ZaiqaDatabaseManager) {
                console.error('❌ ZaiqaDatabaseManager not available');
                throw new Error('ZaiqaDatabaseManager not available');
            }

            if (!window.ZaiqaFinancialCalculations) {
                console.error('❌ ZaiqaFinancialCalculations not available');
                throw new Error('ZaiqaFinancialCalculations not available');
            }

            // Create instances manually if system not available
            console.log('🔧 Creating financial calculation instances manually...');
            const financialEngine = new window.ZaiqaFinancialEngine();
            const databaseManager = new window.ZaiqaDatabaseManager();

            console.log('🔧 Financial Engine created:', !!financialEngine);
            console.log('🔧 Database Manager created:', !!databaseManager);

            const financialCalc = new window.ZaiqaFinancialCalculations(financialEngine, databaseManager);
            console.log('✅ Financial calculations instance created successfully');

            return financialCalc;

        } catch (error) {
            console.error('❌ Failed to get financial calculations:', error);
            console.error('❌ Available classes:', {
                ZaiqaFinancialEngine: !!window.ZaiqaFinancialEngine,
                ZaiqaDatabaseManager: !!window.ZaiqaDatabaseManager,
                ZaiqaFinancialCalculations: !!window.ZaiqaFinancialCalculations
            });
            throw error;
        }
    }

    /**
     * Ensure sample data exists to prevent errors
     */
    ensureSampleData() {
        try {
            console.log('📊 Checking for sample data...');

            // Check orders
            const ordersData = localStorage.getItem('restaurantOrders');
            if (!ordersData || ordersData === 'null' || ordersData === '[]') {
                console.log('📝 Creating sample orders...');
                this.createSampleOrders();
            }

            // Check expenses
            const expensesData = localStorage.getItem('restaurantExpenses');
            if (!expensesData || expensesData === 'null' || expensesData === '[]') {
                console.log('📝 Creating sample expenses...');
                this.createSampleExpenses();
            }

            console.log('✅ Sample data check completed');

        } catch (error) {
            console.warn('⚠️ Failed to ensure sample data:', error);
        }
    }

    createSampleOrders() {
        try {
            const sampleOrders = [
                {
                    id: 'sample-order-1',
                    order_number: 'ORD-001',
                    items: [
                        { name: 'Chicken Biryani', quantity: 2, price: 300 },
                        { name: 'Soft Drink', quantity: 2, price: 50 }
                    ],
                    total_amount: 700,
                    totalAmount: 700,
                    service_type: 'dine-in',
                    customer_count: 2,
                    payment_method: 'cash',
                    status: 'completed',
                    created_at: new Date().toISOString(),
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'sample-order-2',
                    order_number: 'ORD-002',
                    items: [
                        { name: 'Chicken Karahi', quantity: 1, price: 450 }
                    ],
                    total_amount: 450,
                    totalAmount: 450,
                    service_type: 'takeaway',
                    customer_count: 1,
                    payment_method: 'cash',
                    status: 'completed',
                    created_at: new Date().toISOString(),
                    createdAt: new Date().toISOString()
                }
            ];

            localStorage.setItem('restaurantOrders', JSON.stringify(sampleOrders));
            console.log('✅ Sample orders created');

        } catch (error) {
            console.error('❌ Failed to create sample orders:', error);
        }
    }

    createSampleExpenses() {
        try {
            const sampleExpenses = [
                {
                    id: 'sample-expense-1',
                    category: 'Ingredients',
                    amount: 200,
                    description: 'Sample ingredient purchase',
                    date: new Date().toISOString().split('T')[0],
                    created_at: new Date().toISOString()
                },
                {
                    id: 'sample-expense-2',
                    category: 'Utilities',
                    amount: 150,
                    description: 'Sample utility bill',
                    date: new Date().toISOString().split('T')[0],
                    created_at: new Date().toISOString()
                }
            ];

            localStorage.setItem('restaurantExpenses', JSON.stringify(sampleExpenses));
            console.log('✅ Sample expenses created');

        } catch (error) {
            console.error('❌ Failed to create sample expenses:', error);
        }
    }

    /**
     * Create the complete page structure
     */
    createPageStructure() {
        const reportsPage = document.getElementById('reportsPage');
        if (!reportsPage) {
            throw new Error('Reports page container not found');
        }

        reportsPage.innerHTML = `
            <div class="enhanced-reports-container">
                <!-- Header Section -->
                <div class="reports-header">
                    <div class="header-title">
                        <h1><i class="fas fa-chart-bar"></i> Enhanced Reports & Analytics</h1>
                        <p class="header-subtitle">Real-time business insights and comprehensive analytics</p>
                    </div>
                    
                    <div class="header-actions">
                        <button id="morningBalanceBtn" class="btn btn-primary">
                            <i class="fas fa-sun"></i> Morning Balance
                        </button>
                        <button id="ownerWithdrawalBtn" class="btn btn-warning">
                            <i class="fas fa-hand-holding-usd"></i> Owner Withdrawal
                        </button>
                        <button id="endBusinessDayBtn" class="btn btn-danger">
                            <i class="fas fa-moon"></i> End Business Day
                        </button>
                        
                        <div class="date-range-selector">
                            <select id="dateRangeSelect" class="form-select">
                                <option value="today">Today</option>
                                <option value="yesterday">Yesterday</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        
                        <button id="refreshReportsBtn" class="btn btn-secondary">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        
                        <div class="export-dropdown">
                            <button class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="exportPdfBtn">
                                    <i class="fas fa-file-pdf"></i> Export PDF
                                </a></li>
                                <li><a class="dropdown-item" href="#" id="exportExcelBtn">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </a></li>
                                <li><a class="dropdown-item" href="#" id="exportCsvBtn">
                                    <i class="fas fa-file-csv"></i> Export CSV
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="summary-cards-grid">
                    <div class="summary-card revenue-card">
                        <div class="card-icon"><i class="fas fa-money-bill-wave"></i></div>
                        <div class="card-content">
                            <h3 id="totalRevenue">PKR 0</h3>
                            <p>Total Revenue</p>
                            <small class="trend" id="revenueTrend"></small>
                        </div>
                    </div>
                    
                    <div class="summary-card expense-card">
                        <div class="card-icon"><i class="fas fa-receipt"></i></div>
                        <div class="card-content">
                            <h3 id="totalExpenses">PKR 0</h3>
                            <p>Total Expenses</p>
                            <small class="trend" id="expenseTrend"></small>
                        </div>
                    </div>
                    
                    <div class="summary-card profit-card">
                        <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                        <div class="card-content">
                            <h3 id="netProfit">PKR 0</h3>
                            <p>Net Profit</p>
                            <small class="trend" id="profitTrend"></small>
                        </div>
                    </div>
                    
                    <div class="summary-card orders-card">
                        <div class="card-icon"><i class="fas fa-shopping-cart"></i></div>
                        <div class="card-content">
                            <h3 id="totalOrders">0</h3>
                            <p>Total Orders</p>
                            <small class="trend" id="ordersTrend"></small>
                        </div>
                    </div>
                    
                    <div class="summary-card cash-card">
                        <div class="card-icon"><i class="fas fa-wallet"></i></div>
                        <div class="card-content">
                            <h3 id="cashInHand">PKR 0</h3>
                            <p>Cash in Hand</p>
                            <small class="trend" id="cashTrend"></small>
                        </div>
                    </div>
                    
                    <div class="summary-card balance-card">
                        <div class="card-icon"><i class="fas fa-balance-scale"></i></div>
                        <div class="card-content">
                            <h3 id="morningBalance">PKR 0</h3>
                            <p>Morning Balance</p>
                            <small class="trend" id="balanceTrend"></small>
                        </div>
                    </div>
                </div>

                <!-- Navigation Tabs -->
                <div class="reports-navigation">
                    <nav class="nav nav-tabs" id="reportsNav">
                        <button class="nav-link active" data-tab="eod-summary">
                            <i class="fas fa-calendar-day"></i> End of Day Summary
                        </button>
                        <button class="nav-link" data-tab="sales-analytics">
                            <i class="fas fa-chart-area"></i> Sales Analytics
                        </button>
                        <button class="nav-link" data-tab="expense-analysis">
                            <i class="fas fa-coins"></i> Expense Analysis
                        </button>
                        <button class="nav-link" data-tab="inventory-analytics">
                            <i class="fas fa-boxes"></i> Inventory Analytics
                        </button>
                        <button class="nav-link" data-tab="cash-management">
                            <i class="fas fa-cash-register"></i> Cash Management
                        </button>
                        <button class="nav-link" data-tab="profit-analysis">
                            <i class="fas fa-percentage"></i> Profit Analysis
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="tab-content" id="reportsTabContent">
                    <!-- End of Day Summary Tab -->
                    <div class="tab-pane active" id="eod-summary">
                        <div class="eod-summary-container">
                            <div class="eod-header">
                                <h2>End of Day Summary</h2>
                                <div class="eod-date" id="eodDate"></div>
                            </div>
                            <div class="eod-content" id="eodContent">
                                <!-- Content will be loaded dynamically -->
                            </div>
                        </div>
                    </div>

                    <!-- Sales Analytics Tab -->
                    <div class="tab-pane" id="sales-analytics">
                        <!-- Financial Health Dashboard -->
                        <div class="analytics-section">
                            <div class="section-header">
                                <h4><i class="fas fa-chart-pie"></i> Financial Health Dashboard</h4>
                                <button class="btn btn-outline btn-sm" onclick="window.enhancedReportsPage.showFinancialBreakdown()">
                                    <i class="fas fa-calculator"></i> Detailed Breakdown
                                </button>
                            </div>
                            <div class="financial-health-grid">
                                <div class="financial-card revenue">
                                    <div class="financial-header">
                                        <h5><i class="fas fa-arrow-up"></i> Revenue Streams</h5>
                                    </div>
                                    <div class="financial-content">
                                        <div class="financial-item">
                                            <span>Dine-in Sales</span>
                                            <span class="amount positive" id="financialDineInRevenue">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Takeaway Sales</span>
                                            <span class="amount positive" id="financialTakeawayRevenue">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Total Per Head Charges</span>
                                            <span class="amount info" id="financialPerHeadCharges">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Additional Charges</span>
                                            <span class="amount info" id="financialAdditionalCharges">PKR 0</span>
                                        </div>
                                        <div class="financial-item total">
                                            <span>Total Revenue</span>
                                            <span class="amount positive" id="financialTotalRevenue">PKR 0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="financial-card expenses">
                                    <div class="financial-header">
                                        <h5><i class="fas fa-arrow-down"></i> Expense Breakdown</h5>
                                    </div>
                                    <div class="financial-content">
                                        <div class="financial-item">
                                            <span>Operational Costs</span>
                                            <span class="amount negative" id="financialOperationalCosts">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Supplier Payments</span>
                                            <span class="amount negative" id="financialSupplierPayments">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Total Daily Wages</span>
                                            <span class="amount negative" id="financialStaffCosts">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Owner Withdrawals</span>
                                            <span class="amount negative" id="financialOwnerWithdrawals">PKR 0</span>
                                        </div>
                                        <div class="financial-item total">
                                            <span>Total Expenses</span>
                                            <span class="amount negative" id="financialTotalExpenses">PKR 0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="financial-card profit">
                                    <div class="financial-header">
                                        <h5><i class="fas fa-chart-line"></i> Profitability</h5>
                                    </div>
                                    <div class="financial-content">
                                        <div class="financial-item">
                                            <span>Gross Profit</span>
                                            <span class="amount" id="financialGrossProfit">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Profit Margin</span>
                                            <span class="amount" id="financialProfitMargin">0%</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Average Order Value</span>
                                            <span class="amount info" id="financialAvgOrderValue">PKR 0</span>
                                        </div>
                                        <div class="financial-item">
                                            <span>Total Orders</span>
                                            <span class="amount info" id="financialTotalOrders">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="analytics-grid">
                            <div class="chart-container">
                                <h3>Sales Trends</h3>
                                <canvas id="salesTrendsChart"></canvas>
                            </div>
                            <div class="chart-container">
                                <h3>Peak Hours Analysis</h3>
                                <canvas id="peakHoursChart"></canvas>
                            </div>
                            <div class="table-container">
                                <h3>Top Selling Items</h3>
                                <div id="topSellingItems"></div>
                            </div>
                            <div class="table-container">
                                <h3>Recent Orders</h3>
                                <div id="recentOrders"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Expense Analysis Tab -->
                    <div class="tab-pane" id="expense-analysis">
                        <div class="analytics-grid">
                            <div class="chart-container">
                                <h3>Expense Breakdown</h3>
                                <canvas id="expenseBreakdownChart"></canvas>
                            </div>
                            <div class="table-container">
                                <h3>Ingredient Costs</h3>
                                <div id="ingredientCosts"></div>
                            </div>
                            <div class="table-container">
                                <h3>Staff Costs</h3>
                                <div id="staffCosts"></div>
                            </div>
                            <div class="table-container">
                                <h3>Operational Costs</h3>
                                <div id="operationalCosts"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Analytics Tab -->
                    <div class="tab-pane" id="inventory-analytics">
                        <div class="analytics-grid">
                            <div class="chart-container">
                                <h3>Inventory Status</h3>
                                <canvas id="inventoryStatusChart"></canvas>
                            </div>
                            <div class="table-container">
                                <h3>Most Used Items</h3>
                                <div id="mostUsedItems"></div>
                            </div>
                            <div class="table-container">
                                <h3>Low Stock Alerts</h3>
                                <div id="lowStockItems"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Management Tab -->
                    <div class="tab-pane" id="cash-management">
                        <div class="analytics-grid">
                            <div class="chart-container">
                                <h3>Cash Flow</h3>
                                <canvas id="cashFlowChart"></canvas>
                            </div>
                            <div class="table-container">
                                <h3>Cash Withdrawal Records</h3>
                                <div id="cashWithdrawals"></div>
                            </div>
                            <div class="table-container">
                                <h3>Owner Withdrawals</h3>
                                <div id="ownerWithdrawals"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Profit Analysis Tab -->
                    <div class="tab-pane" id="profit-analysis">
                        <div class="analytics-grid">
                            <div class="chart-container">
                                <h3>Item-wise Profit Margins</h3>
                                <canvas id="profitMarginsChart"></canvas>
                            </div>
                            <div class="table-container">
                                <h3>Item-wise Profit Analysis</h3>
                                <div id="itemWiseProfitAnalysis"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Overlay -->
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading reports...</p>
                    </div>
                </div>
            </div>

            <!-- Modals -->
            <div id="modalsContainer"></div>
        `;
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Header action buttons
        document.getElementById('morningBalanceBtn')?.addEventListener('click', () => this.showMorningBalanceModal());
        document.getElementById('ownerWithdrawalBtn')?.addEventListener('click', () => this.showOwnerWithdrawalModal());
        document.getElementById('endBusinessDayBtn')?.addEventListener('click', () => this.showEndBusinessDayModal());
        document.getElementById('refreshReportsBtn')?.addEventListener('click', () => this.loadAllReports());

        // Date range selector
        document.getElementById('dateRangeSelect')?.addEventListener('change', (e) => {
            this.currentDateRange = e.target.value;
            if (this.currentDateRange === 'custom') {
                this.showCustomDateRangeModal();
            } else {
                this.loadAllReports();
            }
        });

        // Export buttons
        document.getElementById('exportPdfBtn')?.addEventListener('click', () => this.exportReports('pdf'));
        document.getElementById('exportExcelBtn')?.addEventListener('click', () => this.exportReports('excel'));
        document.getElementById('exportCsvBtn')?.addEventListener('click', () => this.exportReports('csv'));

        // Tab navigation
        document.querySelectorAll('#reportsNav .nav-link').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(tab.dataset.tab);
            });
        });
    }

    /**
     * Switch between tabs
     */
    switchTab(tabName) {
        // Update active tab
        document.querySelectorAll('#reportsNav .nav-link').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update active content
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        // Load tab-specific data
        this.loadTabData(tabName);
    }

    /**
     * Load all reports data
     */
    loadAllReports() {
        this.showLoading(true);

        try {
            // Update summary cards
            this.updateSummaryCards();

            // Load current tab data
            const activeTab = document.querySelector('#reportsNav .nav-link.active')?.dataset.tab || 'eod-summary';
            this.loadTabData(activeTab);

        } catch (error) {
            console.error('❌ Failed to load reports:', error);
            this.showError('Failed to load reports: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Update summary cards with real-time data
     */
    updateSummaryCards() {
        try {
            const financialCalc = this.getFinancialCalculations();
            const dateRange = this.getDateRange(this.currentDateRange);

            // Ensure we have valid date range
            if (!dateRange || !dateRange.start || !dateRange.end) {
                console.warn('⚠️ Invalid date range for summary cards');
                this.displayEmptySummaryCards();
                return;
            }

            let metrics, cashFlow;

            try {
                console.log('📊 Calculating period metrics...');
                metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);
                console.log('📊 Metrics result:', metrics);
            } catch (metricsError) {
                console.error('❌ Failed to calculate metrics:', metricsError);
                metrics = {
                    revenue: { totalRevenue: 0, orderCount: 0 },
                    expenses: { totalExpenses: 0 }
                };
            }

            try {
                console.log('💰 Calculating cash flow...');
                cashFlow = financialCalc.calculateCashFlow(dateRange.start);
                console.log('💰 Cash flow result:', cashFlow);
            } catch (cashFlowError) {
                console.error('❌ Failed to calculate cash flow:', cashFlowError);
                cashFlow = {
                    openingBalance: 0,
                    closingBalance: 0,
                    variance: 0
                };
            }

            // Ensure metrics have valid structure
            const revenue = metrics?.revenue || { totalRevenue: 0, orderCount: 0 };
            const expenses = metrics?.expenses || { totalExpenses: 0 };

            // Update revenue
            const revenueElement = document.getElementById('totalRevenue');
            if (revenueElement) {
                revenueElement.textContent = `PKR ${(revenue.totalRevenue || 0).toLocaleString()}`;
            }

            // Update expenses
            const expensesElement = document.getElementById('totalExpenses');
            if (expensesElement) {
                expensesElement.textContent = `PKR ${(expenses.totalExpenses || 0).toLocaleString()}`;
            }

            // Update profit
            const profit = (revenue.totalRevenue || 0) - (expenses.totalExpenses || 0);
            const profitElement = document.getElementById('netProfit');
            if (profitElement) {
                profitElement.textContent = `PKR ${profit.toLocaleString()}`;
                profitElement.className = profit >= 0 ? 'profit-positive' : 'profit-negative';
            }

            // Update orders
            const ordersElement = document.getElementById('totalOrders');
            if (ordersElement) {
                ordersElement.textContent = (revenue.orderCount || 0).toLocaleString();
            }

            // Update cash in hand - use multiple sources for accurate calculation
            let currentCash = 0;
            let cashSource = 'unknown';

            console.log('💰 Reports: Starting cash calculation...');
            console.log('💰 Reports: window.app available:', !!window.app);
            console.log('💰 Reports: calculateCashInHandData function available:', !!(window.app && typeof window.app.calculateCashInHandData === 'function'));

            // Method 1: Try main app calculation
            if (window.app && typeof window.app.calculateCashInHandData === 'function') {
                try {
                    const cashData = window.app.calculateCashInHandData();
                    currentCash = cashData.currentBalance || 0;
                    cashSource = 'app_calculation';
                    console.log('💰 Reports: Using app calculation:', currentCash, cashData);
                } catch (error) {
                    console.error('💰 Reports: App calculation failed:', error);
                }
            }

            // Method 2: Fallback to localStorage if app calculation failed or returned 0
            if (currentCash === 0) {
                const localStorageCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
                if (localStorageCash > 0) {
                    currentCash = localStorageCash;
                    cashSource = 'localStorage';
                    console.log('💰 Reports: Using localStorage cash:', currentCash);
                }
            }

            // Method 3: Try cash integration system
            if (currentCash === 0 && window.cashManager && typeof window.cashManager.getCurrentCash === 'function') {
                try {
                    const integrationCash = window.cashManager.getCurrentCash();
                    if (integrationCash > 0) {
                        currentCash = integrationCash;
                        cashSource = 'cash_integration';
                        console.log('💰 Reports: Using cash integration:', currentCash);
                    }
                } catch (error) {
                    console.error('💰 Reports: Cash integration failed:', error);
                }
            }

            // Method 4: Try restaurant analytics system
            if (currentCash === 0 && window.zaiqaAnalytics && typeof window.zaiqaAnalytics.getCurrentCash === 'function') {
                try {
                    const analyticsCash = window.zaiqaAnalytics.getCurrentCash();
                    if (analyticsCash > 0) {
                        currentCash = analyticsCash;
                        cashSource = 'analytics_system';
                        console.log('💰 Reports: Using analytics system:', currentCash);
                    }
                } catch (error) {
                    console.error('💰 Reports: Analytics system failed:', error);
                }
            }

            console.log('💰 Reports: Final cash amount:', currentCash, 'from source:', cashSource);

            const cashElement = document.getElementById('cashInHand');
            if (cashElement) {
                cashElement.textContent = `PKR ${currentCash.toLocaleString()}`;

                // Add source indicator for debugging
                const sourceIndicator = cashElement.parentElement.querySelector('.cash-source');
                if (sourceIndicator) {
                    sourceIndicator.textContent = `Source: ${cashSource}`;
                } else {
                    const sourceSpan = document.createElement('small');
                    sourceSpan.className = 'cash-source';
                    sourceSpan.style.color = '#666';
                    sourceSpan.style.fontSize = '0.8em';
                    sourceSpan.textContent = `Source: ${cashSource}`;
                    cashElement.parentElement.appendChild(sourceSpan);
                }

                // Make cash card clickable to open cash management
                const cashCard = cashElement.closest('.summary-card');
                if (cashCard && window.app && typeof window.app.showCashInHandModal === 'function') {
                    cashCard.style.cursor = 'pointer';
                    cashCard.title = 'Click to open Cash Management';
                    cashCard.onclick = () => {
                        window.app.showCashInHandModal();
                    };
                }
            }

            // Update morning balance
            document.getElementById('morningBalance').textContent = `PKR ${cashFlow.openingBalance.toLocaleString()}`;

            // Update trends (compare with previous period)
            this.updateTrends(metrics);

            // Update Financial Health Dashboard if it's visible
            const salesAnalyticsTab = document.getElementById('sales-analytics');
            if (salesAnalyticsTab && salesAnalyticsTab.classList.contains('active')) {
                this.loadFinancialHealthDashboard();
            }

        } catch (error) {
            console.error('❌ Failed to update summary cards:', error);
            this.displayEmptySummaryCards();
        }
    }

    /**
     * Display empty/default values for summary cards when data is unavailable
     */
    displayEmptySummaryCards() {
        const defaultValues = {
            'totalRevenue': 'PKR 0',
            'totalExpenses': 'PKR 0',
            'netProfit': 'PKR 0',
            'totalOrders': '0'
        };

        Object.entries(defaultValues).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                if (id === 'netProfit') {
                    element.className = 'profit-neutral';
                }
            }
        });

        console.log('📊 Summary cards set to default values due to data unavailability');
    }

    /**
     * Update trend indicators
     */
    updateTrends(currentMetrics) {
        try {
            // Get previous period for comparison
            const prevDateRange = this.getPreviousDateRange(this.currentDateRange);
            const financialCalc = this.getFinancialCalculations();
            const prevMetrics = financialCalc.calculatePeriodMetrics(prevDateRange.start, prevDateRange.end);

            // Calculate and display trends
            this.displayTrend('revenueTrend', currentMetrics.revenue.totalRevenue, prevMetrics.revenue.totalRevenue);
            this.displayTrend('expenseTrend', currentMetrics.expenses.totalExpenses, prevMetrics.expenses.totalExpenses);
            this.displayTrend('profitTrend',
                currentMetrics.revenue.totalRevenue - currentMetrics.expenses.totalExpenses,
                prevMetrics.revenue.totalRevenue - prevMetrics.expenses.totalExpenses
            );
            this.displayTrend('ordersTrend', currentMetrics.revenue.orderCount, prevMetrics.revenue.orderCount);

        } catch (error) {
            console.error('❌ Failed to update trends:', error);
        }
    }

    /**
     * Display trend indicator
     */
    displayTrend(elementId, current, previous) {
        const element = document.getElementById(elementId);
        if (!element) return;

        if (previous === 0) {
            element.textContent = 'No previous data';
            element.className = 'trend neutral';
            return;
        }

        const change = ((current - previous) / previous) * 100;
        const isPositive = change > 0;

        element.textContent = `${isPositive ? '+' : ''}${change.toFixed(1)}%`;
        element.className = `trend ${isPositive ? 'positive' : 'negative'}`;
        element.innerHTML = `
            <i class="fas fa-arrow-${isPositive ? 'up' : 'down'}"></i>
            ${Math.abs(change).toFixed(1)}%
        `;
    }

    /**
     * Load data for specific tab
     */
    loadTabData(tabName) {
        switch (tabName) {
            case 'eod-summary':
                this.loadEndOfDaySummary();
                break;
            case 'sales-analytics':
                this.loadSalesAnalytics();
                break;
            case 'expense-analysis':
                this.loadExpenseAnalysis();
                break;
            case 'inventory-analytics':
                this.loadInventoryAnalytics();
                break;
            case 'cash-management':
                this.loadCashManagement();
                break;
            case 'profit-analysis':
                this.loadProfitAnalysis();
                break;
        }
    }

    /**
     * Get date range based on selection
     */
    getDateRange(range) {
        const today = new Date();
        const start = new Date();
        const end = new Date();

        switch (range) {
            case 'today':
                start.setHours(0, 0, 0, 0);
                end.setHours(23, 59, 59, 999);
                break;
            case 'yesterday':
                start.setDate(start.getDate() - 1);
                start.setHours(0, 0, 0, 0);
                end.setDate(end.getDate() - 1);
                end.setHours(23, 59, 59, 999);
                break;
            case 'week':
                const dayOfWeek = today.getDay();
                start.setDate(start.getDate() - dayOfWeek);
                start.setHours(0, 0, 0, 0);
                end.setHours(23, 59, 59, 999);
                break;
            case 'month':
                start.setDate(1);
                start.setHours(0, 0, 0, 0);
                end.setHours(23, 59, 59, 999);
                break;
        }

        return { start, end };
    }

    /**
     * Get previous date range for comparison
     */
    getPreviousDateRange(range) {
        const current = this.getDateRange(range);
        const daysDiff = Math.ceil((current.end - current.start) / (1000 * 60 * 60 * 24));

        const start = new Date(current.start);
        const end = new Date(current.end);

        start.setDate(start.getDate() - daysDiff);
        end.setDate(end.getDate() - daysDiff);

        return { start, end };
    }

    /**
     * Show/hide loading overlay
     */
    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = show ? 'flex' : 'none';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button class="close-btn" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * Set up auto-refresh
     */
    setupAutoRefresh() {
        // Refresh every 5 minutes
        this.refreshInterval = setInterval(() => {
            if (this.isInitialized) {
                this.updateSummaryCards();
            }
        }, 5 * 60 * 1000);
    }

    /**
     * Load End of Day Summary
     */
    loadEndOfDaySummary() {
        try {
            const financialCalc = this.getFinancialCalculations();
            const dateRange = this.getDateRange(this.currentDateRange);
            const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);
            const cashFlow = financialCalc.calculateCashFlow(dateRange.start);

            // Update date display
            document.getElementById('eodDate').textContent = this.formatDateRange(dateRange);

            // Create EOD summary content
            const eodContent = document.getElementById('eodContent');
            eodContent.innerHTML = `
                <div class="eod-grid">
                    <div class="eod-section opening-balance">
                        <h3><i class="fas fa-sun"></i> Opening Balance</h3>
                        <div class="eod-value">PKR ${cashFlow.openingBalance.toLocaleString()}</div>
                        <div class="eod-details">Morning cash in hand</div>
                    </div>

                    <div class="eod-section sales-summary">
                        <h3><i class="fas fa-shopping-cart"></i> Sales Summary</h3>
                        <div class="eod-value">PKR ${metrics.revenue.totalRevenue.toLocaleString()}</div>
                        <div class="eod-details">
                            ${metrics.revenue.orderCount} orders •
                            Avg: PKR ${metrics.revenue.averageOrderValue.toLocaleString()}
                        </div>
                        <div class="sales-breakdown">
                            <div class="breakdown-item">
                                <span class="breakdown-label">
                                    <i class="fas fa-utensils"></i> Dine-in Sales:
                                </span>
                                <span class="breakdown-value">PKR ${(metrics.revenue.dineInRevenue || 0).toLocaleString()}</span>
                                <span class="breakdown-count">(${metrics.revenue.dineInOrders || 0} orders)</span>
                            </div>
                            <div class="breakdown-item">
                                <span class="breakdown-label">
                                    <i class="fas fa-shopping-bag"></i> Takeaway Sales:
                                </span>
                                <span class="breakdown-value">PKR ${(metrics.revenue.takeawayRevenue || 0).toLocaleString()}</span>
                                <span class="breakdown-count">(${metrics.revenue.takeawayOrders || 0} orders)</span>
                            </div>
                        </div>
                    </div>

                    <div class="eod-section per-head-charges">
                        <h3><i class="fas fa-users"></i> Per Head Charges</h3>
                        <div class="eod-value">PKR ${(metrics.revenue.perHeadCharges || 0).toLocaleString()}</div>
                        <div class="eod-details">Today's per head service charges</div>
                    </div>

                    <div class="eod-section additional-charges">
                        <h3><i class="fas fa-plus-circle"></i> Additional Charges</h3>
                        <div class="eod-value">PKR ${(metrics.revenue.additionalCharges || 0).toLocaleString()}</div>
                        <div class="eod-details">Extra charges from bills</div>
                    </div>

                    <div class="eod-section expenses-summary">
                        <h3><i class="fas fa-receipt"></i> Expenses Summary</h3>
                        <div class="eod-value">PKR ${metrics.expenses.totalExpenses.toLocaleString()}</div>
                        <div class="eod-details">
                            ${Object.keys(metrics.expenses.expensesByCategory).length} categories
                        </div>
                    </div>

                    <div class="eod-section profit-summary">
                        <h3><i class="fas fa-chart-line"></i> Net Profit</h3>
                        <div class="eod-value ${(metrics.revenue.totalRevenue - metrics.expenses.totalExpenses) >= 0 ? 'profit-positive' : 'profit-negative'}">
                            PKR ${(metrics.revenue.totalRevenue - metrics.expenses.totalExpenses).toLocaleString()}
                        </div>
                        <div class="eod-details">
                            Margin: ${((metrics.revenue.totalRevenue - metrics.expenses.totalExpenses) / Math.max(metrics.revenue.totalRevenue, 1) * 100).toFixed(1)}%
                        </div>
                    </div>

                    <div class="eod-section withdrawals-summary">
                        <h3><i class="fas fa-hand-holding-usd"></i> Owner Withdrawals</h3>
                        <div class="eod-value">PKR ${Math.abs(metrics.expenses.expensesByCategory['Owner Withdrawal'] || 0).toLocaleString()}</div>
                        <div class="eod-details">Personal withdrawals</div>
                    </div>

                    <div class="eod-section closing-balance">
                        <h3><i class="fas fa-moon"></i> Expected Closing</h3>
                        <div class="eod-value">PKR ${cashFlow.expectedClosing.toLocaleString()}</div>
                        <div class="eod-details">
                            Actual: PKR ${cashFlow.closingBalance.toLocaleString()}
                            ${cashFlow.cashDifference !== 0 ? `<br><span class="cash-difference ${cashFlow.cashDifference > 0 ? 'positive' : 'negative'}">
                                Difference: PKR ${Math.abs(cashFlow.cashDifference).toLocaleString()}
                            </span>` : ''}
                        </div>
                    </div>
                </div>

                <div class="eod-actions">
                    <button class="btn btn-primary" onclick="window.enhancedReportsPage.showMorningBalanceModal()">
                        <i class="fas fa-sun"></i> Set Morning Balance
                    </button>
                    <button class="btn btn-warning" onclick="window.enhancedReportsPage.showOwnerWithdrawalModal()">
                        <i class="fas fa-hand-holding-usd"></i> Record Withdrawal
                    </button>
                    <button class="btn btn-danger" onclick="window.enhancedReportsPage.showEndBusinessDayModal()">
                        <i class="fas fa-moon"></i> End Business Day
                    </button>
                </div>
            `;

        } catch (error) {
            console.error('❌ Failed to load EOD summary:', error);
            document.getElementById('eodContent').innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    Failed to load End of Day summary: ${error.message}
                </div>
            `;
        }
    }

    /**
     * Show Morning Balance Modal
     */
    showMorningBalanceModal() {
        const modal = this.createModal('Morning Balance', `
            <div class="modal-form">
                <div class="form-group">
                    <label for="morningBalanceAmount">Opening Balance Amount</label>
                    <input type="number" id="morningBalanceAmount" class="form-control"
                           placeholder="Enter opening balance" min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label for="morningBalanceDate">Date</label>
                    <input type="date" id="morningBalanceDate" class="form-control"
                           value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label for="morningBalanceNotes">Notes (Optional)</label>
                    <textarea id="morningBalanceNotes" class="form-control"
                              placeholder="Additional notes about the opening balance"></textarea>
                </div>
            </div>
        `, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => this.closeModal()
            },
            {
                text: 'Set Balance',
                class: 'btn-primary',
                action: () => this.saveMorningBalance()
            }
        ]);

        this.showModal(modal);
    }

    /**
     * Save Morning Balance
     */
    saveMorningBalance() {
        try {
            const amount = parseFloat(document.getElementById('morningBalanceAmount').value);
            const date = document.getElementById('morningBalanceDate').value;
            const notes = document.getElementById('morningBalanceNotes').value;

            if (isNaN(amount) || amount < 0) {
                this.showError('Please enter a valid amount');
                return;
            }

            if (!date) {
                this.showError('Please select a date');
                return;
            }

            // Use cash management system to set opening balance
            if (window.zaiqaCashManagement && typeof window.zaiqaCashManagement.setOpeningBalance === 'function') {
                window.zaiqaCashManagement.setOpeningBalance(amount, date);
            } else {
                // Fallback implementation
                this.setOpeningBalanceDirectly(amount, date, notes);
            }

            this.closeModal();
            this.showSuccess(`Morning balance of PKR ${amount.toLocaleString()} set successfully`);
            this.loadAllReports();

        } catch (error) {
            console.error('❌ Failed to save morning balance:', error);
            this.showError('Failed to save morning balance: ' + error.message);
        }
    }

    /**
     * Direct implementation for setting opening balance
     */
    setOpeningBalanceDirectly(amount, date, notes) {
        try {
            const cashRegister = JSON.parse(localStorage.getItem('cashRegister') || '[]');

            // Find or create entry for the date
            let dateEntry = cashRegister.find(entry => entry.date === date);

            if (!dateEntry) {
                dateEntry = {
                    id: 'cash_' + Date.now(),
                    date: date,
                    morningBalance: amount,
                    eveningBalance: amount,
                    totalSales: 0,
                    totalExpenses: 0,
                    notes: notes,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                cashRegister.push(dateEntry);
            } else {
                dateEntry.morningBalance = amount;
                dateEntry.notes = notes;
                dateEntry.updatedAt = new Date().toISOString();
            }

            localStorage.setItem('cashRegister', JSON.stringify(cashRegister));

            // Update current cash in hand if setting for today
            const today = new Date().toISOString().split('T')[0];
            if (date === today) {
                localStorage.setItem('currentCashInHand', amount.toString());
            }

        } catch (error) {
            console.error('❌ Failed to set opening balance directly:', error);
            throw error;
        }
    }

    /**
     * Format date range for display
     */
    formatDateRange(dateRange) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        if (dateRange.start.toDateString() === dateRange.end.toDateString()) {
            return dateRange.start.toLocaleDateString('en-US', options);
        } else {
            return `${dateRange.start.toLocaleDateString('en-US', options)} - ${dateRange.end.toLocaleDateString('en-US', options)}`;
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        const notification = document.createElement('div');
        notification.className = 'success-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-check-circle"></i>
                <span>${message}</span>
                <button class="close-btn" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    /**
     * Create modal
     */
    createModal(title, content, buttons = []) {
        return {
            title,
            content,
            buttons
        };
    }

    /**
     * Show modal
     */
    showModal(modal) {
        const modalsContainer = document.getElementById('modalsContainer');

        const modalHtml = `
            <div class="modal-overlay" id="currentModal">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>${modal.title}</h3>
                        <button class="modal-close" onclick="window.enhancedReportsPage.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${modal.content}
                    </div>
                    <div class="modal-footer">
                        ${modal.buttons.map(btn =>
                            `<button class="btn ${btn.class}" onclick="(${btn.action.toString()})()">${btn.text}</button>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;

        modalsContainer.innerHTML = modalHtml;

        // Focus first input
        setTimeout(() => {
            const firstInput = modalsContainer.querySelector('input, textarea, select');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    /**
     * Close modal
     */
    closeModal() {
        const modal = document.getElementById('currentModal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * Show Owner Withdrawal Modal
     */
    showOwnerWithdrawalModal() {
        const modal = this.createModal('Owner Withdrawal', `
            <div class="modal-form">
                <div class="form-group">
                    <label for="withdrawalAmount">Withdrawal Amount</label>
                    <input type="number" id="withdrawalAmount" class="form-control"
                           placeholder="Enter withdrawal amount" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="withdrawalReason">Reason</label>
                    <select id="withdrawalReason" class="form-control" required>
                        <option value="">Select reason</option>
                        <option value="Personal Use">Personal Use</option>
                        <option value="Business Investment">Business Investment</option>
                        <option value="Emergency">Emergency</option>
                        <option value="Family Expense">Family Expense</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group" id="customReasonGroup" style="display: none;">
                    <label for="customReason">Custom Reason</label>
                    <input type="text" id="customReason" class="form-control"
                           placeholder="Enter custom reason">
                </div>
                <div class="form-group">
                    <label for="withdrawalNotes">Notes (Optional)</label>
                    <textarea id="withdrawalNotes" class="form-control"
                              placeholder="Additional notes about the withdrawal"></textarea>
                </div>
                <div class="cash-info">
                    <div class="info-item">
                        <span>Current Cash in Hand:</span>
                        <strong>PKR ${parseFloat(localStorage.getItem('currentCashInHand') || '0').toLocaleString()}</strong>
                    </div>
                </div>
            </div>
        `, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => this.closeModal()
            },
            {
                text: 'Record Withdrawal',
                class: 'btn-warning',
                action: () => this.saveOwnerWithdrawal()
            }
        ]);

        this.showModal(modal);

        // Add event listener for reason dropdown
        setTimeout(() => {
            document.getElementById('withdrawalReason').addEventListener('change', (e) => {
                const customGroup = document.getElementById('customReasonGroup');
                if (e.target.value === 'Other') {
                    customGroup.style.display = 'block';
                    document.getElementById('customReason').required = true;
                } else {
                    customGroup.style.display = 'none';
                    document.getElementById('customReason').required = false;
                }
            });
        }, 100);
    }

    /**
     * Save Owner Withdrawal
     */
    saveOwnerWithdrawal() {
        try {
            const amount = parseFloat(document.getElementById('withdrawalAmount').value);
            const reason = document.getElementById('withdrawalReason').value;
            const customReason = document.getElementById('customReason').value;
            const notes = document.getElementById('withdrawalNotes').value;

            if (isNaN(amount) || amount <= 0) {
                this.showError('Please enter a valid withdrawal amount');
                return;
            }

            if (!reason) {
                this.showError('Please select a reason for withdrawal');
                return;
            }

            if (reason === 'Other' && !customReason.trim()) {
                this.showError('Please enter a custom reason');
                return;
            }

            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            if (amount > currentCash) {
                this.showError('Insufficient cash in hand for this withdrawal');
                return;
            }

            const finalReason = reason === 'Other' ? customReason : reason;

            // Use cash management system if available
            if (window.zaiqaCashManagement && typeof window.zaiqaCashManagement.processEnhancedCashTransaction === 'function') {
                const description = `Owner Withdrawal: ${finalReason}${notes ? ' - ' + notes : ''}`;
                window.zaiqaCashManagement.processEnhancedCashTransaction(-amount, description, 'Owner Withdrawal');
            } else {
                // Fallback implementation
                this.recordOwnerWithdrawalDirectly(amount, finalReason, notes);
            }

            this.closeModal();
            this.showSuccess(`Owner withdrawal of PKR ${amount.toLocaleString()} recorded successfully`);
            this.loadAllReports();

        } catch (error) {
            console.error('❌ Failed to save owner withdrawal:', error);
            this.showError('Failed to record withdrawal: ' + error.message);
        }
    }

    /**
     * Direct implementation for recording owner withdrawal
     */
    recordOwnerWithdrawalDirectly(amount, reason, notes) {
        try {
            // Add to expenses
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const withdrawal = {
                id: 'expense_' + Date.now(),
                description: `Owner Withdrawal: ${reason}`,
                amount: amount,
                category: 'Owner Withdrawal',
                date: new Date().toISOString().split('T')[0],
                notes: notes,
                createdAt: new Date().toISOString()
            };

            expenses.push(withdrawal);
            localStorage.setItem('expenses', JSON.stringify(expenses));

            // Update cash in hand
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const newCash = currentCash - amount;
            localStorage.setItem('currentCashInHand', newCash.toString());

            // Update cash register
            const today = new Date().toISOString().split('T')[0];
            const cashRegister = JSON.parse(localStorage.getItem('cashRegister') || '[]');
            let todayEntry = cashRegister.find(entry => entry.date === today);

            if (!todayEntry) {
                todayEntry = {
                    id: 'cash_' + Date.now(),
                    date: today,
                    morningBalance: 0,
                    eveningBalance: newCash,
                    totalSales: 0,
                    totalExpenses: amount,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                cashRegister.push(todayEntry);
            } else {
                todayEntry.totalExpenses = (todayEntry.totalExpenses || 0) + amount;
                todayEntry.eveningBalance = newCash;
                todayEntry.updatedAt = new Date().toISOString();
            }

            localStorage.setItem('cashRegister', JSON.stringify(cashRegister));

        } catch (error) {
            console.error('❌ Failed to record owner withdrawal directly:', error);
            throw error;
        }
    }

    /**
     * Show End Business Day Modal
     */
    showEndBusinessDayModal() {
        // Use existing business day manager if available
        if (window.businessDayManager && typeof window.businessDayManager.showDayTransitionModal === 'function') {
            window.businessDayManager.showDayTransitionModal();
            return;
        }

        // Fallback implementation
        const financialCalc = this.getFinancialCalculations();
        const today = new Date().toISOString().split('T')[0];
        const dateRange = { start: new Date(), end: new Date() };
        dateRange.start.setHours(0, 0, 0, 0);
        dateRange.end.setHours(23, 59, 59, 999);

        const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);
        const cashFlow = financialCalc.calculateCashFlow(dateRange.start);

        const modal = this.createModal('End Business Day', `
            <div class="ebd-summary">
                <h4>Today's Business Summary</h4>
                <div class="ebd-grid">
                    <div class="ebd-item">
                        <span>Opening Balance:</span>
                        <strong>PKR ${cashFlow.openingBalance.toLocaleString()}</strong>
                    </div>
                    <div class="ebd-item">
                        <span>Total Sales:</span>
                        <strong>PKR ${metrics.revenue.totalRevenue.toLocaleString()}</strong>
                    </div>
                    <div class="ebd-item">
                        <span>Total Expenses:</span>
                        <strong>PKR ${metrics.expenses.totalExpenses.toLocaleString()}</strong>
                    </div>
                    <div class="ebd-item">
                        <span>Net Profit:</span>
                        <strong class="${(metrics.revenue.totalRevenue - metrics.expenses.totalExpenses) >= 0 ? 'profit-positive' : 'profit-negative'}">
                            PKR ${(metrics.revenue.totalRevenue - metrics.expenses.totalExpenses).toLocaleString()}
                        </strong>
                    </div>
                    <div class="ebd-item">
                        <span>Expected Closing:</span>
                        <strong>PKR ${cashFlow.expectedClosing.toLocaleString()}</strong>
                    </div>
                </div>

                <div class="form-group">
                    <label for="actualClosingBalance">Actual Closing Balance</label>
                    <input type="number" id="actualClosingBalance" class="form-control"
                           placeholder="Enter actual cash count" min="0" step="0.01"
                           value="${cashFlow.expectedClosing}">
                </div>

                <div class="form-group">
                    <label for="ebdNotes">End of Day Notes</label>
                    <textarea id="ebdNotes" class="form-control"
                              placeholder="Any notes about today's business"></textarea>
                </div>

                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This will finalize today's business and prepare the system for tomorrow.
                    This action cannot be undone.
                </div>
            </div>
        `, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => this.closeModal()
            },
            {
                text: 'End Business Day',
                class: 'btn-danger',
                action: () => this.finalizeBusinessDay()
            }
        ]);

        this.showModal(modal);
    }

    /**
     * Finalize Business Day
     */
    finalizeBusinessDay() {
        try {
            const actualClosing = parseFloat(document.getElementById('actualClosingBalance').value);
            const notes = document.getElementById('ebdNotes').value;

            if (isNaN(actualClosing) || actualClosing < 0) {
                this.showError('Please enter a valid closing balance');
                return;
            }

            const today = new Date().toISOString().split('T')[0];

            // Update cash register with final closing balance
            const cashRegister = JSON.parse(localStorage.getItem('cashRegister') || '[]');
            let todayEntry = cashRegister.find(entry => entry.date === today);

            if (todayEntry) {
                todayEntry.eveningBalance = actualClosing;
                todayEntry.notes = notes;
                todayEntry.finalizedAt = new Date().toISOString();
                todayEntry.updatedAt = new Date().toISOString();
            }

            localStorage.setItem('cashRegister', JSON.stringify(cashRegister));

            // Update current cash in hand
            localStorage.setItem('currentCashInHand', actualClosing.toString());

            // Create end of day report
            this.createEndOfDayReport(actualClosing, notes);

            this.closeModal();
            this.showSuccess('Business day finalized successfully');
            this.loadAllReports();

        } catch (error) {
            console.error('❌ Failed to finalize business day:', error);
            this.showError('Failed to finalize business day: ' + error.message);
        }
    }

    /**
     * Create End of Day Report
     */
    createEndOfDayReport(closingBalance, notes) {
        try {
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const today = new Date().toISOString().split('T')[0];
            const dateRange = { start: new Date(), end: new Date() };
            dateRange.start.setHours(0, 0, 0, 0);
            dateRange.end.setHours(23, 59, 59, 999);

            const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);
            const cashFlow = financialCalc.calculateCashFlow(dateRange.start);

            const report = {
                id: 'eod_' + Date.now(),
                date: today,
                openingBalance: cashFlow.openingBalance,
                closingBalance: closingBalance,
                expectedClosing: cashFlow.expectedClosing,
                cashDifference: closingBalance - cashFlow.expectedClosing,
                totalRevenue: metrics.revenue.totalRevenue,
                totalExpenses: metrics.expenses.totalExpenses,
                netProfit: metrics.revenue.totalRevenue - metrics.expenses.totalExpenses,
                orderCount: metrics.revenue.orderCount,
                expensesByCategory: metrics.expenses.expensesByCategory,
                notes: notes,
                createdAt: new Date().toISOString()
            };

            // Save report
            const reports = JSON.parse(localStorage.getItem('eodReports') || '[]');
            reports.push(report);
            localStorage.setItem('eodReports', JSON.stringify(reports));

            console.log('✅ End of day report created:', report);

        } catch (error) {
            console.error('❌ Failed to create EOD report:', error);
        }
    }

    /**
     * Load Financial Health Dashboard
     */
    loadFinancialHealthDashboard() {
        try {
            console.log('📊 Loading Financial Health Dashboard...');

            // Check if Financial Health Dashboard elements exist
            const requiredElements = [
                'financialDineInRevenue',
                'financialTakeawayRevenue',
                'financialPerHeadCharges',
                'financialStaffCosts'
            ];

            const missingElements = requiredElements.filter(id => !document.getElementById(id));
            if (missingElements.length > 0) {
                console.warn('⚠️ Financial Health Dashboard elements not found:', missingElements);
                console.log('🔧 Current active tab:', document.querySelector('.tab-pane.active')?.id);
                console.log('🔧 Sales analytics tab exists:', !!document.getElementById('sales-analytics'));
                return;
            }

            const financialCalc = this.getFinancialCalculations();
            const dateRange = this.getDateRange(this.currentDateRange);

            // Calculate metrics with enhanced debugging
            const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);
            console.log('📊 Financial Health Metrics:', metrics);

            // Update Revenue Streams
            this.updateFinancialElement('financialDineInRevenue', metrics.revenue.dineInRevenue || 0);
            this.updateFinancialElement('financialTakeawayRevenue', metrics.revenue.takeawayRevenue || 0);
            this.updateFinancialElement('financialPerHeadCharges', metrics.revenue.perHeadCharges || 0);
            this.updateFinancialElement('financialAdditionalCharges', metrics.revenue.additionalCharges || 0);
            this.updateFinancialElement('financialTotalRevenue', metrics.revenue.totalRevenue || 0);

            // Update Expense Breakdown
            const expensesByCategory = metrics.expenses.expensesByCategory || {};
            console.log('💸 Expense categories for dashboard:', expensesByCategory);

            const operationalCosts = (expensesByCategory['Operational'] || 0) +
                                   (expensesByCategory['Utilities'] || 0) +
                                   (expensesByCategory['Rent'] || 0);

            const supplierPayments = (expensesByCategory['Supplier'] || 0) +
                                   (expensesByCategory['Supplier Payment'] || 0) +
                                   (expensesByCategory['Suppliers'] || 0);

            // Calculate daily wages for present staff instead of generic staff costs
            const today = new Date().toISOString().split('T')[0];
            const dailyWages = financialCalc.calculateDailyWagesForPresentStaff(today);
            console.log('👥 Daily wages calculated:', dailyWages);

            const ownerWithdrawals = Math.abs(expensesByCategory['Owner Withdrawal'] || 0);

            this.updateFinancialElement('financialOperationalCosts', operationalCosts);
            this.updateFinancialElement('financialSupplierPayments', supplierPayments);
            this.updateFinancialElement('financialStaffCosts', dailyWages);
            this.updateFinancialElement('financialOwnerWithdrawals', ownerWithdrawals);
            this.updateFinancialElement('financialTotalExpenses', metrics.expenses.totalExpenses || 0);

            // Update Profitability
            const grossProfit = (metrics.revenue.totalRevenue || 0) - (metrics.expenses.totalExpenses || 0);
            const profitMargin = metrics.revenue.totalRevenue > 0 ?
                ((grossProfit / metrics.revenue.totalRevenue) * 100).toFixed(1) : 0;

            this.updateFinancialElement('financialGrossProfit', grossProfit, grossProfit >= 0 ? 'positive' : 'negative');
            document.getElementById('financialProfitMargin').textContent = `${profitMargin}%`;
            document.getElementById('financialProfitMargin').className = `amount ${grossProfit >= 0 ? 'positive' : 'negative'}`;

            this.updateFinancialElement('financialAvgOrderValue', metrics.revenue.averageOrderValue || 0);
            document.getElementById('financialTotalOrders').textContent = (metrics.revenue.orderCount || 0).toString();

            console.log('✅ Financial Health Dashboard loaded successfully');

        } catch (error) {
            console.error('❌ Failed to load Financial Health Dashboard:', error);
            this.showError('Failed to load Financial Health Dashboard: ' + error.message);
        }
    }

    /**
     * Update financial element with proper formatting
     */
    updateFinancialElement(elementId, value, amountClass = null) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = `PKR ${(value || 0).toLocaleString()}`;
            if (amountClass) {
                element.className = `amount ${amountClass}`;
            }
        } else {
            console.warn(`⚠️ Financial element not found: ${elementId}`);
            console.log('🔧 Available elements:', {
                financialDineInRevenue: !!document.getElementById('financialDineInRevenue'),
                financialTakeawayRevenue: !!document.getElementById('financialTakeawayRevenue'),
                financialPerHeadCharges: !!document.getElementById('financialPerHeadCharges'),
                financialStaffCosts: !!document.getElementById('financialStaffCosts'),
                financialSupplierPayments: !!document.getElementById('financialSupplierPayments')
            });
        }
    }

    /**
     * Show Financial Breakdown Modal
     */
    showFinancialBreakdown() {
        try {
            const financialCalc = this.getFinancialCalculations();
            const dateRange = this.getDateRange(this.currentDateRange);
            const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);

            const modal = this.createModal('Financial Breakdown Details', `
                <div class="financial-breakdown-content">
                    <div class="breakdown-section">
                        <h4><i class="fas fa-chart-line"></i> Revenue Analysis</h4>
                        <div class="breakdown-grid">
                            <div class="breakdown-item">
                                <span>Dine-in Revenue:</span>
                                <span>PKR ${(metrics.revenue.dineInRevenue || 0).toLocaleString()}</span>
                            </div>
                            <div class="breakdown-item">
                                <span>Takeaway Revenue:</span>
                                <span>PKR ${(metrics.revenue.takeawayRevenue || 0).toLocaleString()}</span>
                            </div>
                            <div class="breakdown-item">
                                <span>Per Head Charges:</span>
                                <span>PKR ${(metrics.revenue.perHeadCharges || 0).toLocaleString()}</span>
                            </div>
                            <div class="breakdown-item">
                                <span>Additional Charges:</span>
                                <span>PKR ${(metrics.revenue.additionalCharges || 0).toLocaleString()}</span>
                            </div>
                            <div class="breakdown-item total">
                                <span>Total Revenue:</span>
                                <span>PKR ${(metrics.revenue.totalRevenue || 0).toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    <div class="breakdown-section">
                        <h4><i class="fas fa-receipt"></i> Expense Analysis</h4>
                        <div class="breakdown-grid">
                            ${Object.entries(metrics.expenses.expensesByCategory || {}).map(([category, amount]) => `
                                <div class="breakdown-item">
                                    <span>${category}:</span>
                                    <span>PKR ${amount.toLocaleString()}</span>
                                </div>
                            `).join('')}
                            <div class="breakdown-item">
                                <span>Daily Wages (Present Staff):</span>
                                <span>PKR ${(financialCalc.calculateDailyWagesForPresentStaff(new Date().toISOString().split('T')[0])).toLocaleString()}</span>
                            </div>
                            <div class="breakdown-item total">
                                <span>Total Expenses:</span>
                                <span>PKR ${(metrics.expenses.totalExpenses || 0).toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    <div class="breakdown-section">
                        <h4><i class="fas fa-calculator"></i> Profitability</h4>
                        <div class="breakdown-grid">
                            <div class="breakdown-item">
                                <span>Gross Profit:</span>
                                <span class="${((metrics.revenue.totalRevenue || 0) - (metrics.expenses.totalExpenses || 0)) >= 0 ? 'positive' : 'negative'}">
                                    PKR ${((metrics.revenue.totalRevenue || 0) - (metrics.expenses.totalExpenses || 0)).toLocaleString()}
                                </span>
                            </div>
                            <div class="breakdown-item">
                                <span>Profit Margin:</span>
                                <span>${metrics.revenue.totalRevenue > 0 ? (((metrics.revenue.totalRevenue - metrics.expenses.totalExpenses) / metrics.revenue.totalRevenue) * 100).toFixed(2) : 0}%</span>
                            </div>
                            <div class="breakdown-item">
                                <span>Average Order Value:</span>
                                <span>PKR ${(metrics.revenue.averageOrderValue || 0).toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `, [
                {
                    text: 'Close',
                    class: 'btn-primary',
                    action: () => this.closeModal()
                }
            ]);

            this.showModal(modal);

        } catch (error) {
            console.error('❌ Failed to show financial breakdown:', error);
            this.showError('Failed to show financial breakdown: ' + error.message);
        }
    }

    /**
     * Load Sales Analytics
     */
    loadSalesAnalytics() {
        try {
            console.log('📊 Loading Sales Analytics tab...');

            // Add a small delay to ensure DOM elements are ready
            setTimeout(() => {
                try {
                    this.loadFinancialHealthDashboard();
                } catch (dashboardError) {
                    console.error('❌ Failed to load Financial Health Dashboard:', dashboardError);
                }
            }, 100);

            this.loadSalesTrends();
            this.loadPeakHoursAnalysis();
            this.loadTopSellingItems();
            this.loadRecentOrders();

        } catch (error) {
            console.error('❌ Failed to load sales analytics:', error);
        }
    }

    /**
     * Manually trigger Financial Health Dashboard loading (for debugging)
     */
    triggerFinancialHealthDashboard() {
        console.log('🔧 Manually triggering Financial Health Dashboard...');

        // Ensure we're on the sales analytics tab
        const salesTab = document.getElementById('sales-analytics');
        if (!salesTab) {
            console.error('❌ Sales analytics tab not found');
            return;
        }

        // Make sure the tab is active
        if (!salesTab.classList.contains('active')) {
            console.log('🔧 Activating sales analytics tab...');
            this.switchTab('sales-analytics');
        }

        // Wait for DOM to be ready and then load dashboard
        setTimeout(() => {
            this.loadFinancialHealthDashboard();
        }, 500);
    }

    /**
     * Load Sales Trends Chart
     */
    loadSalesTrends() {
        try {
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const last7Days = [];
            const salesData = [];

            // Get last 7 days data
            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                const dayStart = new Date(date);
                dayStart.setHours(0, 0, 0, 0);
                const dayEnd = new Date(date);
                dayEnd.setHours(23, 59, 59, 999);

                const dayMetrics = financialCalc.calculatePeriodMetrics(dayStart, dayEnd);

                last7Days.push(date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }));
                salesData.push(dayMetrics.revenue.totalRevenue);
            }

            this.renderChart('salesTrendsChart', 'line', {
                labels: last7Days,
                datasets: [{
                    label: 'Daily Sales',
                    data: salesData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            });

        } catch (error) {
            console.error('❌ Failed to load sales trends:', error);
        }
    }

    /**
     * Load Peak Hours Analysis
     */
    loadPeakHoursAnalysis() {
        try {
            const orders = JSON.parse(localStorage.getItem('orders') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            // Filter orders by date range
            const filteredOrders = orders.filter(order => {
                const orderDate = new Date(order.createdAt);
                return orderDate >= dateRange.start && orderDate <= dateRange.end && order.status === 'completed';
            });

            // Group by hour
            const hourlyData = {};
            for (let hour = 0; hour < 24; hour++) {
                hourlyData[hour] = 0;
            }

            filteredOrders.forEach(order => {
                const hour = new Date(order.createdAt).getHours();
                hourlyData[hour]++;
            });

            // Convert to chart data (only show business hours)
            const businessHours = [];
            const orderCounts = [];

            for (let hour = 8; hour <= 23; hour++) {
                const timeLabel = hour <= 12 ? `${hour}:00 AM` : `${hour - 12}:00 PM`;
                businessHours.push(timeLabel);
                orderCounts.push(hourlyData[hour]);
            }

            this.renderChart('peakHoursChart', 'bar', {
                labels: businessHours,
                datasets: [{
                    label: 'Orders per Hour',
                    data: orderCounts,
                    backgroundColor: 'rgba(40, 167, 69, 0.8)',
                    borderColor: '#28a745',
                    borderWidth: 1
                }]
            });

        } catch (error) {
            console.error('❌ Failed to load peak hours analysis:', error);
        }
    }

    /**
     * Load Top Selling Items
     */
    loadTopSellingItems() {
        try {
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const dateRange = this.getDateRange(this.currentDateRange);
            const topItems = financialCalc.calculateTopItems(dateRange.start, dateRange.end, 10);

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Item Name</th>
                                <th>Quantity Sold</th>
                                <th>Total Revenue</th>
                                <th>Avg Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${topItems.map((item, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>PKR ${item.revenue.toLocaleString()}</td>
                                    <td>PKR ${(item.revenue / item.quantity).toLocaleString()}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('topSellingItems').innerHTML = topItems.length > 0 ? tableHtml :
                '<div class="no-data">No sales data available for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load top selling items:', error);
            document.getElementById('topSellingItems').innerHTML = '<div class="error-message">Failed to load top selling items</div>';
        }
    }

    /**
     * Load Recent Orders
     */
    loadRecentOrders() {
        try {
            const orders = JSON.parse(localStorage.getItem('orders') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            const recentOrders = orders
                .filter(order => {
                    const orderDate = new Date(order.createdAt);
                    return orderDate >= dateRange.start && orderDate <= dateRange.end;
                })
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 10);

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Time</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${recentOrders.map(order => `
                                <tr>
                                    <td>${order.orderNumber || order.id}</td>
                                    <td>${new Date(order.createdAt).toLocaleTimeString()}</td>
                                    <td>${order.customerName || 'Walk-in'}</td>
                                    <td>PKR ${(order.totalAmount || 0).toLocaleString()}</td>
                                    <td><span class="status-badge status-${order.status}">${order.status}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('recentOrders').innerHTML = recentOrders.length > 0 ? tableHtml :
                '<div class="no-data">No orders found for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load recent orders:', error);
            document.getElementById('recentOrders').innerHTML = '<div class="error-message">Failed to load recent orders</div>';
        }
    }

    /**
     * Render chart using Chart.js
     */
    renderChart(canvasId, type, data, options = {}) {
        try {
            const canvas = document.getElementById(canvasId);
            if (!canvas) {
                console.warn(`Canvas element ${canvasId} not found`);
                return;
            }

            const ctx = canvas.getContext('2d');

            // Destroy existing chart if it exists
            if (this.charts[canvasId]) {
                this.charts[canvasId].destroy();
            }

            // Default options
            const defaultOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: type !== 'doughnut' && type !== 'pie' ? {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'PKR ' + value.toLocaleString();
                            }
                        }
                    }
                } : {}
            };

            // Create new chart
            this.charts[canvasId] = new Chart(ctx, {
                type: type,
                data: data,
                options: { ...defaultOptions, ...options }
            });

        } catch (error) {
            console.error(`❌ Failed to render chart ${canvasId}:`, error);
        }
    }

    /**
     * Export reports
     */
    exportReports(format) {
        try {
            console.log(`Exporting reports to ${format.toUpperCase()}`);

            // Get current data
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const dateRange = this.getDateRange(this.currentDateRange);
            const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);

            const reportData = {
                dateRange: this.formatDateRange(dateRange),
                summary: {
                    totalRevenue: metrics.revenue.totalRevenue,
                    totalExpenses: metrics.expenses.totalExpenses,
                    netProfit: metrics.revenue.totalRevenue - metrics.expenses.totalExpenses,
                    orderCount: metrics.revenue.orderCount
                },
                generatedAt: new Date().toISOString()
            };

            switch (format) {
                case 'pdf':
                    this.exportToPDF(reportData);
                    break;
                case 'excel':
                    this.exportToExcel(reportData);
                    break;
                case 'csv':
                    this.exportToCSV(reportData);
                    break;
            }

        } catch (error) {
            console.error(`❌ Failed to export to ${format}:`, error);
            this.showError(`Failed to export to ${format.toUpperCase()}: ${error.message}`);
        }
    }

    /**
     * Export to CSV
     */
    exportToCSV(data) {
        const csvContent = [
            ['Report Period', data.dateRange],
            ['Generated At', new Date(data.generatedAt).toLocaleString()],
            [''],
            ['Summary'],
            ['Total Revenue', `PKR ${data.summary.totalRevenue.toLocaleString()}`],
            ['Total Expenses', `PKR ${data.summary.totalExpenses.toLocaleString()}`],
            ['Net Profit', `PKR ${data.summary.netProfit.toLocaleString()}`],
            ['Total Orders', data.summary.orderCount]
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `zaiqa-reports-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        this.showSuccess('Report exported to CSV successfully');
    }

    /**
     * Export to Excel using SheetJS
     */
    exportToExcel(data) {
        try {
            if (typeof XLSX === 'undefined') {
                this.showError('Excel export library not loaded. Please use CSV export.');
                return;
            }

            // Create workbook
            const wb = XLSX.utils.book_new();

            // Summary sheet
            const summaryData = [
                ['Report Period', data.dateRange],
                ['Generated At', new Date(data.generatedAt).toLocaleString()],
                [''],
                ['Summary'],
                ['Total Revenue', data.summary.totalRevenue],
                ['Total Expenses', data.summary.totalExpenses],
                ['Net Profit', data.summary.netProfit],
                ['Total Orders', data.summary.orderCount]
            ];

            const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
            XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

            // Write file
            XLSX.writeFile(wb, `zaiqa-reports-${new Date().toISOString().split('T')[0]}.xlsx`);
            this.showSuccess('Report exported to Excel successfully');

        } catch (error) {
            console.error('❌ Failed to export to Excel:', error);
            this.showError('Failed to export to Excel: ' + error.message);
        }
    }

    /**
     * Export to PDF using jsPDF
     */
    exportToPDF(data) {
        try {
            if (typeof jsPDF === 'undefined') {
                this.showError('PDF export library not loaded. Please use CSV export.');
                return;
            }

            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Add title
            doc.setFontSize(20);
            doc.text('Zaiqa Al-Hayat - Business Report', 20, 30);

            // Add report period
            doc.setFontSize(12);
            doc.text(`Report Period: ${data.dateRange}`, 20, 50);
            doc.text(`Generated: ${new Date(data.generatedAt).toLocaleString()}`, 20, 60);

            // Add summary
            doc.setFontSize(16);
            doc.text('Summary', 20, 80);

            doc.setFontSize(12);
            doc.text(`Total Revenue: PKR ${data.summary.totalRevenue.toLocaleString()}`, 20, 100);
            doc.text(`Total Expenses: PKR ${data.summary.totalExpenses.toLocaleString()}`, 20, 110);
            doc.text(`Net Profit: PKR ${data.summary.netProfit.toLocaleString()}`, 20, 120);
            doc.text(`Total Orders: ${data.summary.orderCount}`, 20, 130);

            // Save PDF
            doc.save(`zaiqa-reports-${new Date().toISOString().split('T')[0]}.pdf`);
            this.showSuccess('Report exported to PDF successfully');

        } catch (error) {
            console.error('❌ Failed to export to PDF:', error);
            this.showError('Failed to export to PDF: ' + error.message);
        }
    }

    /**
     * Load Expense Analysis
     */
    loadExpenseAnalysis() {
        try {
            this.loadExpenseBreakdown();
            this.loadIngredientCosts();
            this.loadStaffCosts();
            this.loadOperationalCosts();

        } catch (error) {
            console.error('❌ Failed to load expense analysis:', error);
        }
    }

    /**
     * Load Expense Breakdown Chart
     */
    loadExpenseBreakdown() {
        try {
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const dateRange = this.getDateRange(this.currentDateRange);
            const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);

            const categories = Object.keys(metrics.expenses.expensesByCategory);
            const amounts = Object.values(metrics.expenses.expensesByCategory);

            if (categories.length === 0) {
                document.getElementById('expenseBreakdownChart').parentElement.innerHTML =
                    '<div class="no-data">No expense data available for the selected period</div>';
                return;
            }

            this.renderChart('expenseBreakdownChart', 'doughnut', {
                labels: categories,
                datasets: [{
                    data: amounts,
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ],
                    borderWidth: 2
                }]
            });

        } catch (error) {
            console.error('❌ Failed to load expense breakdown:', error);
        }
    }

    /**
     * Load Ingredient Costs
     */
    loadIngredientCosts() {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            const ingredientExpenses = expenses.filter(expense => {
                const expenseDate = new Date(expense.date || expense.createdAt);
                return expenseDate >= dateRange.start &&
                       expenseDate <= dateRange.end &&
                       (expense.category === 'Ingredients' || expense.category === 'Food Items');
            });

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Item</th>
                                <th>Amount</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${ingredientExpenses.map(expense => `
                                <tr>
                                    <td>${new Date(expense.date || expense.createdAt).toLocaleDateString()}</td>
                                    <td>${expense.description}</td>
                                    <td>PKR ${expense.amount.toLocaleString()}</td>
                                    <td>${expense.notes || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('ingredientCosts').innerHTML = ingredientExpenses.length > 0 ? tableHtml :
                '<div class="no-data">No ingredient costs found for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load ingredient costs:', error);
            document.getElementById('ingredientCosts').innerHTML = '<div class="error-message">Failed to load ingredient costs</div>';
        }
    }

    /**
     * Load Staff Costs
     */
    loadStaffCosts() {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            // Get staff-related expenses
            const staffExpenses = expenses.filter(expense => {
                const expenseDate = new Date(expense.date || expense.createdAt);
                return expenseDate >= dateRange.start &&
                       expenseDate <= dateRange.end &&
                       (expense.category === 'Staff' || expense.category === 'Salary' || expense.category === 'Wages');
            });

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Staff Member</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${staffExpenses.map(expense => `
                                <tr>
                                    <td>${new Date(expense.date || expense.createdAt).toLocaleDateString()}</td>
                                    <td>${expense.staffName || 'General'}</td>
                                    <td>${expense.category}</td>
                                    <td>PKR ${expense.amount.toLocaleString()}</td>
                                    <td>${expense.notes || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('staffCosts').innerHTML = staffExpenses.length > 0 ? tableHtml :
                '<div class="no-data">No staff costs found for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load staff costs:', error);
            document.getElementById('staffCosts').innerHTML = '<div class="error-message">Failed to load staff costs</div>';
        }
    }

    /**
     * Load Operational Costs
     */
    loadOperationalCosts() {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            const operationalExpenses = expenses.filter(expense => {
                const expenseDate = new Date(expense.date || expense.createdAt);
                return expenseDate >= dateRange.start &&
                       expenseDate <= dateRange.end &&
                       !['Ingredients', 'Food Items', 'Staff', 'Salary', 'Wages', 'Owner Withdrawal'].includes(expense.category);
            });

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Amount</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${operationalExpenses.map(expense => `
                                <tr>
                                    <td>${new Date(expense.date || expense.createdAt).toLocaleDateString()}</td>
                                    <td>${expense.category}</td>
                                    <td>${expense.description}</td>
                                    <td>PKR ${expense.amount.toLocaleString()}</td>
                                    <td>${expense.notes || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('operationalCosts').innerHTML = operationalExpenses.length > 0 ? tableHtml :
                '<div class="no-data">No operational costs found for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load operational costs:', error);
            document.getElementById('operationalCosts').innerHTML = '<div class="error-message">Failed to load operational costs</div>';
        }
    }

    /**
     * Load Inventory Analytics
     */
    loadInventoryAnalytics() {
        try {
            this.loadInventoryStatus();
            this.loadMostUsedItems();
            this.loadLowStockItems();

        } catch (error) {
            console.error('❌ Failed to load inventory analytics:', error);
        }
    }

    /**
     * Load Inventory Status Chart
     */
    loadInventoryStatus() {
        try {
            const inventory = JSON.parse(localStorage.getItem('inventory') || '[]');

            let inStock = 0;
            let lowStock = 0;
            let outOfStock = 0;

            inventory.forEach(item => {
                const quantity = item.quantity || 0;
                const minStock = item.minStock || 5;

                if (quantity === 0) {
                    outOfStock++;
                } else if (quantity <= minStock) {
                    lowStock++;
                } else {
                    inStock++;
                }
            });

            if (inventory.length === 0) {
                document.getElementById('inventoryStatusChart').parentElement.innerHTML =
                    '<div class="no-data">No inventory data available</div>';
                return;
            }

            this.renderChart('inventoryStatusChart', 'pie', {
                labels: ['In Stock', 'Low Stock', 'Out of Stock'],
                datasets: [{
                    data: [inStock, lowStock, outOfStock],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                    borderWidth: 2
                }]
            });

        } catch (error) {
            console.error('❌ Failed to load inventory status:', error);
        }
    }

    /**
     * Load Most Used Items
     */
    loadMostUsedItems() {
        try {
            const orders = JSON.parse(localStorage.getItem('orders') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            const itemUsage = {};

            orders.filter(order => {
                const orderDate = new Date(order.createdAt);
                return orderDate >= dateRange.start && orderDate <= dateRange.end && order.status === 'completed';
            }).forEach(order => {
                if (order.items && Array.isArray(order.items)) {
                    order.items.forEach(item => {
                        const itemName = item.name || item.itemName;
                        const quantity = item.quantity || 1;

                        if (itemUsage[itemName]) {
                            itemUsage[itemName] += quantity;
                        } else {
                            itemUsage[itemName] = quantity;
                        }
                    });
                }
            });

            const sortedItems = Object.entries(itemUsage)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Item Name</th>
                                <th>Times Used</th>
                                <th>Usage Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sortedItems.map(([name, usage], index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${name}</td>
                                    <td>${usage}</td>
                                    <td>${((usage / Math.max(Object.values(itemUsage).reduce((a, b) => a + b, 0), 1)) * 100).toFixed(1)}%</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('mostUsedItems').innerHTML = sortedItems.length > 0 ? tableHtml :
                '<div class="no-data">No usage data available for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load most used items:', error);
            document.getElementById('mostUsedItems').innerHTML = '<div class="error-message">Failed to load most used items</div>';
        }
    }

    /**
     * Load Low Stock Items
     */
    loadLowStockItems() {
        try {
            const inventory = JSON.parse(localStorage.getItem('inventory') || '[]');

            const lowStockItems = inventory.filter(item => {
                const quantity = item.quantity || 0;
                const minStock = item.minStock || 5;
                return quantity <= minStock;
            }).sort((a, b) => (a.quantity || 0) - (b.quantity || 0));

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Item Name</th>
                                <th>Current Stock</th>
                                <th>Min Stock</th>
                                <th>Status</th>
                                <th>Action Needed</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${lowStockItems.map(item => {
                                const quantity = item.quantity || 0;
                                const minStock = item.minStock || 5;
                                const status = quantity === 0 ? 'Out of Stock' : 'Low Stock';
                                const statusClass = quantity === 0 ? 'status-danger' : 'status-warning';

                                return `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${quantity} ${item.unit || ''}</td>
                                        <td>${minStock} ${item.unit || ''}</td>
                                        <td><span class="status-badge ${statusClass}">${status}</span></td>
                                        <td>${quantity === 0 ? 'Restock Immediately' : 'Restock Soon'}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('lowStockItems').innerHTML = lowStockItems.length > 0 ? tableHtml :
                '<div class="no-data">All items are adequately stocked</div>';

        } catch (error) {
            console.error('❌ Failed to load low stock items:', error);
            document.getElementById('lowStockItems').innerHTML = '<div class="error-message">Failed to load low stock items</div>';
        }
    }

    /**
     * Load Cash Management
     */
    loadCashManagement() {
        try {
            this.loadCashFlow();
            this.loadCashWithdrawals();
            this.loadOwnerWithdrawalsHistory();

        } catch (error) {
            console.error('❌ Failed to load cash management:', error);
        }
    }

    /**
     * Load Cash Flow Chart
     */
    loadCashFlow() {
        try {
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const dateRange = this.getDateRange(this.currentDateRange);
            const metrics = financialCalc.calculatePeriodMetrics(dateRange.start, dateRange.end);

            this.renderChart('cashFlowChart', 'bar', {
                labels: ['Cash In', 'Cash Out', 'Net Flow'],
                datasets: [{
                    label: 'Amount (PKR)',
                    data: [
                        metrics.revenue.totalRevenue,
                        metrics.expenses.totalExpenses,
                        metrics.revenue.totalRevenue - metrics.expenses.totalExpenses
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#dc3545',
                        metrics.revenue.totalRevenue - metrics.expenses.totalExpenses >= 0 ? '#28a745' : '#dc3545'
                    ],
                    borderWidth: 1
                }]
            });

        } catch (error) {
            console.error('❌ Failed to load cash flow:', error);
        }
    }

    /**
     * Load Cash Withdrawals
     */
    loadCashWithdrawals() {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            const withdrawals = expenses.filter(expense => {
                const expenseDate = new Date(expense.date || expense.createdAt);
                return expenseDate >= dateRange.start &&
                       expenseDate <= dateRange.end &&
                       expense.description &&
                       expense.description.toLowerCase().includes('withdrawal');
            });

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Time</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Category</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${withdrawals.map(withdrawal => `
                                <tr>
                                    <td>${new Date(withdrawal.date || withdrawal.createdAt).toLocaleDateString()}</td>
                                    <td>${new Date(withdrawal.createdAt).toLocaleTimeString()}</td>
                                    <td>PKR ${withdrawal.amount.toLocaleString()}</td>
                                    <td>${withdrawal.description}</td>
                                    <td>${withdrawal.category}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('cashWithdrawals').innerHTML = withdrawals.length > 0 ? tableHtml :
                '<div class="no-data">No cash withdrawals found for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load cash withdrawals:', error);
            document.getElementById('cashWithdrawals').innerHTML = '<div class="error-message">Failed to load cash withdrawals</div>';
        }
    }

    /**
     * Load Owner Withdrawals History
     */
    loadOwnerWithdrawalsHistory() {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const dateRange = this.getDateRange(this.currentDateRange);

            const ownerWithdrawals = expenses.filter(expense => {
                const expenseDate = new Date(expense.date || expense.createdAt);
                return expenseDate >= dateRange.start &&
                       expenseDate <= dateRange.end &&
                       expense.category === 'Owner Withdrawal';
            });

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Time</th>
                                <th>Amount</th>
                                <th>Reason</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${ownerWithdrawals.map(withdrawal => `
                                <tr>
                                    <td>${new Date(withdrawal.date || withdrawal.createdAt).toLocaleDateString()}</td>
                                    <td>${new Date(withdrawal.createdAt).toLocaleTimeString()}</td>
                                    <td>PKR ${withdrawal.amount.toLocaleString()}</td>
                                    <td>${withdrawal.description.replace('Owner Withdrawal: ', '')}</td>
                                    <td>${withdrawal.notes || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('ownerWithdrawals').innerHTML = ownerWithdrawals.length > 0 ? tableHtml :
                '<div class="no-data">No owner withdrawals found for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load owner withdrawals:', error);
            document.getElementById('ownerWithdrawals').innerHTML = '<div class="error-message">Failed to load owner withdrawals</div>';
        }
    }

    /**
     * Load Profit Analysis
     */
    loadProfitAnalysis() {
        try {
            this.loadProfitMargins();
            this.loadItemWiseProfitAnalysis();

        } catch (error) {
            console.error('❌ Failed to load profit analysis:', error);
        }
    }

    /**
     * Load Profit Margins Chart
     */
    loadProfitMargins() {
        try {
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const dateRange = this.getDateRange(this.currentDateRange);
            const topItems = financialCalc.calculateTopItems(dateRange.start, dateRange.end, 10);
            const menuItems = JSON.parse(localStorage.getItem('menuItems') || '[]');

            const profitData = topItems.map(item => {
                const menuItem = menuItems.find(m => m.name === item.name);
                const cost = menuItem ? (menuItem.cost || 0) : 0;
                const revenue = item.revenue;
                const totalCost = cost * item.quantity;
                const profit = revenue - totalCost;
                const margin = revenue > 0 ? (profit / revenue) * 100 : 0;

                return {
                    name: item.name,
                    margin: margin,
                    profit: profit
                };
            }).filter(item => item.margin > 0);

            if (profitData.length === 0) {
                document.getElementById('profitMarginsChart').parentElement.innerHTML =
                    '<div class="no-data">No profit data available for the selected period</div>';
                return;
            }

            this.renderChart('profitMarginsChart', 'bar', {
                labels: profitData.map(item => item.name),
                datasets: [{
                    label: 'Profit Margin (%)',
                    data: profitData.map(item => item.margin),
                    backgroundColor: profitData.map(item =>
                        item.margin >= 50 ? '#28a745' :
                        item.margin >= 25 ? '#ffc107' : '#dc3545'
                    ),
                    borderWidth: 1
                }]
            }, {
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            });

        } catch (error) {
            console.error('❌ Failed to load profit margins:', error);
        }
    }

    /**
     * Load Item-wise Profit Analysis
     */
    loadItemWiseProfitAnalysis() {
        try {
            const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
            const dateRange = this.getDateRange(this.currentDateRange);
            const topItems = financialCalc.calculateTopItems(dateRange.start, dateRange.end, 20);
            const menuItems = JSON.parse(localStorage.getItem('menuItems') || '[]');

            const profitAnalysis = topItems.map(item => {
                const menuItem = menuItems.find(m => m.name === item.name);
                const unitCost = menuItem ? (menuItem.cost || 0) : 0;
                const unitPrice = item.revenue / item.quantity;
                const totalCost = unitCost * item.quantity;
                const totalRevenue = item.revenue;
                const totalProfit = totalRevenue - totalCost;
                const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

                return {
                    name: item.name,
                    quantity: item.quantity,
                    unitCost: unitCost,
                    unitPrice: unitPrice,
                    totalCost: totalCost,
                    totalRevenue: totalRevenue,
                    totalProfit: totalProfit,
                    profitMargin: profitMargin
                };
            }).sort((a, b) => b.totalProfit - a.totalProfit);

            const tableHtml = `
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Item Name</th>
                                <th>Qty Sold</th>
                                <th>Unit Cost</th>
                                <th>Unit Price</th>
                                <th>Total Cost</th>
                                <th>Total Revenue</th>
                                <th>Total Profit</th>
                                <th>Margin %</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${profitAnalysis.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>PKR ${item.unitCost.toLocaleString()}</td>
                                    <td>PKR ${item.unitPrice.toLocaleString()}</td>
                                    <td>PKR ${item.totalCost.toLocaleString()}</td>
                                    <td>PKR ${item.totalRevenue.toLocaleString()}</td>
                                    <td class="${item.totalProfit >= 0 ? 'profit-positive' : 'profit-negative'}">
                                        PKR ${item.totalProfit.toLocaleString()}
                                    </td>
                                    <td class="${item.profitMargin >= 50 ? 'margin-high' : item.profitMargin >= 25 ? 'margin-medium' : 'margin-low'}">
                                        ${item.profitMargin.toFixed(1)}%
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('itemWiseProfitAnalysis').innerHTML = profitAnalysis.length > 0 ? tableHtml :
                '<div class="no-data">No profit analysis data available for the selected period</div>';

        } catch (error) {
            console.error('❌ Failed to load item-wise profit analysis:', error);
            document.getElementById('itemWiseProfitAnalysis').innerHTML = '<div class="error-message">Failed to load profit analysis</div>';
        }
    }

    /**
     * Show custom date range modal
     */
    showCustomDateRangeModal() {
        const modal = this.createModal('Custom Date Range', `
            <div class="modal-form">
                <div class="form-group">
                    <label for="customStartDate">Start Date</label>
                    <input type="date" id="customStartDate" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="customEndDate">End Date</label>
                    <input type="date" id="customEndDate" class="form-control" required>
                </div>
            </div>
        `, [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => {
                    document.getElementById('dateRangeSelect').value = 'today';
                    this.currentDateRange = 'today';
                    this.closeModal();
                }
            },
            {
                text: 'Apply Range',
                class: 'btn-primary',
                action: () => this.applyCustomDateRange()
            }
        ]);

        this.showModal(modal);
    }

    /**
     * Apply custom date range
     */
    applyCustomDateRange() {
        try {
            const startDate = document.getElementById('customStartDate').value;
            const endDate = document.getElementById('customEndDate').value;

            if (!startDate || !endDate) {
                this.showError('Please select both start and end dates');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                this.showError('Start date cannot be after end date');
                return;
            }

            // Store custom range
            this.customDateRange = {
                start: new Date(startDate + 'T00:00:00'),
                end: new Date(endDate + 'T23:59:59')
            };

            this.closeModal();
            this.loadAllReports();

        } catch (error) {
            console.error('❌ Failed to apply custom date range:', error);
            this.showError('Failed to apply date range: ' + error.message);
        }
    }

    /**
     * Override getDateRange for custom range support
     */
    getDateRange(range) {
        if (range === 'custom' && this.customDateRange) {
            console.log('📅 Reports: Using custom date range:', this.customDateRange);
            return this.customDateRange;
        }

        const today = new Date();
        const start = new Date();
        const end = new Date();

        console.log('📅 Reports: Calculating date range for:', range);
        console.log('📅 Reports: Today is:', today.toISOString());

        switch (range) {
            case 'today':
                start.setHours(0, 0, 0, 0);
                end.setHours(23, 59, 59, 999);
                break;
            case 'yesterday':
                start.setDate(start.getDate() - 1);
                start.setHours(0, 0, 0, 0);
                end.setDate(end.getDate() - 1);
                end.setHours(23, 59, 59, 999);
                break;
            case 'week':
                const dayOfWeek = today.getDay();
                start.setDate(start.getDate() - dayOfWeek);
                start.setHours(0, 0, 0, 0);
                end.setHours(23, 59, 59, 999);
                break;
            case 'month':
                start.setDate(1);
                start.setHours(0, 0, 0, 0);
                end.setHours(23, 59, 59, 999);
                break;
        }

        const dateRange = { start, end };
        console.log('📅 Reports: Calculated date range:', {
            range: range,
            start: dateRange.start.toISOString(),
            end: dateRange.end.toISOString(),
            startLocal: dateRange.start.toLocaleString(),
            endLocal: dateRange.end.toLocaleString()
        });

        return dateRange;
    }
}

// Initialize the Enhanced Reports Page
window.enhancedReportsPage = new EnhancedReportsPage();
