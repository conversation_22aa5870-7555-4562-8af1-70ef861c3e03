/**
 * Zaiqa Restaurant Financial Calculations - Clean Architecture
 * 100% Accurate Financial Calculations for Restaurant Operations
 * Revenue, Expenses, Profit/Loss, Cash Flow Analysis
 */

class ZaiqaFinancialCalculations {
    constructor(financialEngine, databaseManager) {
        this.financialEngine = financialEngine;
        this.databaseManager = databaseManager;
        this.version = '2.0.0';
        
        // Validate dependencies
        this.validateDependencies();
        
        console.log('📊 Financial Calculations module initialized v' + this.version);
    }

    /**
     * Validate required dependencies
     */
    validateDependencies() {
        if (!this.financialEngine || !this.financialEngine.isInitialized()) {
            throw new Error('Financial Engine is required and must be initialized');
        }
        
        if (!this.databaseManager || !this.databaseManager.isInitialized()) {
            throw new Error('Database Manager is required and must be initialized');
        }
    }

    /**
     * Calculate total revenue for a specific date range
     */
    calculateRevenue(startDate, endDate) {
        try {
            console.log('💰 Starting revenue calculation for:', { startDate, endDate });

            // Get orders within date range with bulletproof error handling
            let allOrders = [];
            try {
                allOrders = this.databaseManager.read('orders', { status: 'completed' });
                if (!Array.isArray(allOrders)) {
                    console.warn('⚠️ Orders read returned non-array, using empty array');
                    allOrders = [];
                }
            } catch (readError) {
                console.warn('⚠️ Failed to read orders, using empty array:', readError.message);
                allOrders = [];
            }

            console.log('📊 Database read result:', {
                type: typeof allOrders,
                isArray: Array.isArray(allOrders),
                length: Array.isArray(allOrders) ? allOrders.length : 'N/A'
            });

            // Ensure we have an array to work with
            if (!Array.isArray(allOrders)) {
                console.warn('⚠️ No orders data available for revenue calculation');
                return { orderRevenue: 0, udharPayments: 0, totalRevenue: 0, orderCount: 0, averageOrderValue: 0 };
            }

            const orders = allOrders.filter(order => {
                if (!order || typeof order !== 'object') {
                    return false;
                }

                // Handle different date field names for compatibility
                const orderDate = new Date(order.created_at || order.createdAt || order.date);
                const start = new Date(startDate);
                const end = new Date(endDate);

                // Skip invalid dates
                if (isNaN(orderDate.getTime()) || isNaN(start.getTime()) || isNaN(end.getTime())) {
                    return false;
                }

                console.log('📅 Financial Calc: Filtering order:', {
                    orderId: order.id,
                    orderNumber: order.order_number,
                    orderDate: orderDate.toISOString(),
                    startDate: start.toISOString(),
                    endDate: end.toISOString(),
                    inRange: orderDate >= start && orderDate <= end
                });

                return orderDate >= start && orderDate <= end;
            });

            // Calculate order revenue with enhanced service type breakdown
            let orderRevenue = 0;
            let dineInRevenue = 0;
            let takeawayRevenue = 0;
            let perHeadCharges = 0;
            let additionalCharges = 0;
            let dineInOrders = 0;
            let takeawayOrders = 0;

            orders.forEach(order => {
                const orderTotal = order.totalAmount || order.total_amount || 0;
                const serviceType = (order.service_type || order.serviceType || 'takeaway').toLowerCase();

                console.log('📊 Processing order:', {
                    id: order.id,
                    orderNumber: order.order_number,
                    total: orderTotal,
                    serviceType: serviceType,
                    originalServiceType: order.service_type || order.serviceType,
                    perHeadCharge: order.per_head_charge || order.perHeadCharge,
                    tableNumber: order.table_number,
                    status: order.status
                });

                orderRevenue = this.financialEngine.calculate('add', orderRevenue, orderTotal);

                // Enhanced service type breakdown with more flexible matching
                // Check for dine-in variations including table orders
                const isDineIn = serviceType.includes('dine') || serviceType.includes('in') ||
                    serviceType === 'dine-in' || serviceType === 'dinein' ||
                    serviceType === 'dine_in' || serviceType === 'dine in' ||
                    (order.table_number && order.table_number !== 'Takeaway' &&
                     order.table_number !== 'takeaway' && order.table_number !== null &&
                     order.table_number !== undefined && order.table_number !== '');

                console.log('📊 Service type analysis:', {
                    serviceType: serviceType,
                    tableNumber: order.table_number,
                    isDineIn: isDineIn,
                    serviceTypeChecks: {
                        includesDine: serviceType.includes('dine'),
                        includesIn: serviceType.includes('in'),
                        exactMatches: [
                            serviceType === 'dine-in',
                            serviceType === 'dinein',
                            serviceType === 'dine_in',
                            serviceType === 'dine in'
                        ],
                        hasValidTable: order.table_number && order.table_number !== 'Takeaway' &&
                                      order.table_number !== 'takeaway'
                    }
                });

                if (isDineIn) {
                    dineInRevenue = this.financialEngine.calculate('add', dineInRevenue, orderTotal);
                    dineInOrders++;
                    console.log('📊 ✅ DINE-IN ORDER - Added to dine-in:', orderTotal, 'New dine-in total:', dineInRevenue);
                } else {
                    takeawayRevenue = this.financialEngine.calculate('add', takeawayRevenue, orderTotal);
                    takeawayOrders++;
                    console.log('📊 📦 TAKEAWAY ORDER - Added to takeaway:', orderTotal, 'New takeaway total:', takeawayRevenue);
                }

                // Calculate per head charges with multiple field names
                const perHeadCharge = order.per_head_charge || order.perHeadCharge || order.per_head_charges || 0;
                if (perHeadCharge && perHeadCharge > 0) {
                    perHeadCharges = this.financialEngine.calculate('add', perHeadCharges, perHeadCharge);
                    console.log('📊 Added per head charge:', perHeadCharge, 'New per head total:', perHeadCharges);
                }

                // Calculate additional charges with comprehensive handling
                if (order.additional_charges && Array.isArray(order.additional_charges)) {
                    const orderAdditionalCharges = order.additional_charges.reduce((sum, charge) => {
                        return sum + (charge.amount || 0);
                    }, 0);
                    additionalCharges = this.financialEngine.calculate('add', additionalCharges, orderAdditionalCharges);
                } else if (order.additional_charges_total && order.additional_charges_total > 0) {
                    additionalCharges = this.financialEngine.calculate('add', additionalCharges, order.additional_charges_total);
                }
            });

            console.log('📊 Revenue breakdown calculated:', {
                orderRevenue,
                dineInRevenue,
                takeawayRevenue,
                perHeadCharges,
                additionalCharges,
                dineInOrders,
                takeawayOrders
            });

            // Get Udhar payments within date range
            const allUdhars = this.databaseManager.read('udhars');
            let udharPayments = 0;

            // Ensure we have an array to work with
            if (Array.isArray(allUdhars)) {
                allUdhars.forEach(udhar => {
                    if (udhar && udhar.transactions && Array.isArray(udhar.transactions)) {
                        udhar.transactions.forEach(transaction => {
                            if (transaction && transaction.type === 'payment') {
                                const transactionDate = new Date(transaction.date);
                                const start = new Date(startDate);
                                const end = new Date(endDate);

                                if (transactionDate >= start && transactionDate <= end) {
                                    udharPayments = this.financialEngine.calculate('add', udharPayments, transaction.amount || 0);
                                }
                            }
                        });
                    }
                });
            }

            const totalRevenue = this.financialEngine.calculate('add', orderRevenue, udharPayments);

            return {
                orderRevenue: orderRevenue,
                udharPayments: udharPayments,
                totalRevenue: totalRevenue,
                orderCount: orders.length,
                averageOrderValue: orders.length > 0 ?
                    this.financialEngine.calculate('divide', orderRevenue, orders.length) : 0,
                // Service type breakdown
                dineInRevenue: dineInRevenue || 0,
                takeawayRevenue: takeawayRevenue || 0,
                // Charge breakdowns
                perHeadCharges: perHeadCharges || 0,
                additionalCharges: additionalCharges || 0,
                // Service type counts
                dineInOrders: dineInOrders || 0,
                takeawayOrders: takeawayOrders || 0
            };

        } catch (error) {
            console.error('❌ Revenue calculation failed:', error);
            return {
                orderRevenue: 0,
                udharPayments: 0,
                totalRevenue: 0,
                orderCount: 0,
                averageOrderValue: 0,
                // Service type breakdown
                dineInRevenue: 0,
                takeawayRevenue: 0,
                // Charge breakdowns
                perHeadCharges: 0,
                additionalCharges: 0,
                // Service type counts
                dineInOrders: 0,
                takeawayOrders: 0
            };
        }
    }

    /**
     * Calculate total expenses for a specific date range
     */
    calculateExpenses(startDate, endDate) {
        try {
            console.log('💸 Starting expense calculation for:', { startDate, endDate });

            // Get expenses within date range
            const allExpenses = this.databaseManager.read('expenses');

            console.log('📊 Expenses database read result:', {
                type: typeof allExpenses,
                isArray: Array.isArray(allExpenses),
                length: Array.isArray(allExpenses) ? allExpenses.length : 'N/A'
            });

            // Ensure we have an array to work with
            if (!Array.isArray(allExpenses)) {
                console.warn('⚠️ No expenses data available for calculation');
                return { totalExpenses: 0, expensesByCategory: {}, expenseCount: 0 };
            }

            const expenses = allExpenses.filter(expense => {
                if (!expense || typeof expense !== 'object') {
                    return false;
                }

                const expenseDate = new Date(expense.date);
                const start = new Date(startDate);
                const end = new Date(endDate);

                // Skip invalid dates
                if (isNaN(expenseDate.getTime()) || isNaN(start.getTime()) || isNaN(end.getTime())) {
                    return false;
                }

                return expenseDate >= start && expenseDate <= end;
            });

            // Group expenses by category
            const expensesByCategory = {};
            let totalExpenses = 0;

            console.log('💸 Processing expenses:', expenses.length, 'expenses found');

            expenses.forEach(expense => {
                const category = expense.category || expense.type || 'Other';
                const amount = expense.amount || 0;

                console.log('💸 Processing expense:', {
                    id: expense.id,
                    category: category,
                    amount: amount,
                    description: expense.description,
                    date: expense.date
                });

                if (!expensesByCategory[category]) {
                    expensesByCategory[category] = 0;
                }

                expensesByCategory[category] = this.financialEngine.calculate('add',
                    expensesByCategory[category], amount);
                totalExpenses = this.financialEngine.calculate('add', totalExpenses, amount);
            });

            console.log('💸 Expense calculation complete:', {
                totalExpenses: totalExpenses,
                expensesByCategory: expensesByCategory,
                expenseCount: expenses.length
            });

            return {
                totalExpenses: totalExpenses,
                expensesByCategory: expensesByCategory,
                expenseCount: expenses.length,
                averageExpense: expenses.length > 0 ? 
                    this.financialEngine.calculate('divide', totalExpenses, expenses.length) : 0
            };

        } catch (error) {
            console.error('❌ Expense calculation failed:', error);
            return {
                totalExpenses: 0,
                expensesByCategory: {},
                expenseCount: 0,
                averageExpense: 0
            };
        }
    }

    /**
     * Calculate profit and loss for a specific date range
     */
    calculateProfitLoss(startDate, endDate) {
        try {
            const revenue = this.calculateRevenue(startDate, endDate);
            const expenses = this.calculateExpenses(startDate, endDate);

            const grossProfit = this.financialEngine.calculate('subtract', 
                revenue.totalRevenue, expenses.totalExpenses);
            
            const profitMargin = revenue.totalRevenue > 0 ? 
                this.financialEngine.calculate('percentage', grossProfit, revenue.totalRevenue) : 0;

            return {
                revenue: revenue,
                expenses: expenses,
                grossProfit: grossProfit,
                profitMargin: profitMargin,
                isProfit: grossProfit >= 0
            };

        } catch (error) {
            console.error('❌ Profit/Loss calculation failed:', error);
            return {
                revenue: { totalRevenue: 0 },
                expenses: { totalExpenses: 0 },
                grossProfit: 0,
                profitMargin: 0,
                isProfit: false
            };
        }
    }

    /**
     * Calculate cash flow for a specific date
     */
    calculateCashFlow(date) {
        try {
            const businessDate = date || this.financialEngine.getCurrentBusinessDate();
            const dateRange = this.financialEngine.getBusinessDateRange(businessDate);

            // Ensure we have a valid date range
            if (!dateRange || !dateRange.start || !dateRange.end) {
                console.warn('⚠️ Invalid date range for cash flow calculation');
                return {
                    openingBalance: 0,
                    closingBalance: 0,
                    expectedClosing: 0,
                    variance: 0,
                    dayRevenue: { totalRevenue: 0 },
                    dayExpenses: { totalExpenses: 0 }
                };
            }

            // Get cash register data
            const allCashRegister = this.databaseManager.read('cashRegister', {
                date: businessDate
            });

            // Ensure we have an array to work with
            if (!Array.isArray(allCashRegister)) {
                console.warn('⚠️ No cash register data available');
                return {
                    openingBalance: 0,
                    closingBalance: 0,
                    expectedClosing: 0,
                    variance: 0,
                    dayRevenue: { totalRevenue: 0 },
                    dayExpenses: { totalExpenses: 0 }
                };
            }

            const cashEntry = allCashRegister.length > 0 ? allCashRegister[0] : null;

            // Calculate day's revenue and expenses
            const dayRevenue = this.calculateRevenue(dateRange.start, dateRange.end);
            const dayExpenses = this.calculateExpenses(businessDate, businessDate);

            // Calculate cash flow
            const openingBalance = cashEntry ? cashEntry.morningBalance || 0 : 0;
            const closingBalance = cashEntry ? cashEntry.eveningBalance || 0 : 0;
            
            const expectedClosing = this.financialEngine.calculate('add',
                this.financialEngine.calculate('subtract', openingBalance, dayExpenses.totalExpenses),
                dayRevenue.orderRevenue // Only cash orders, not Udhar
            );

            const cashDifference = this.financialEngine.calculate('subtract', 
                closingBalance, expectedClosing);

            return {
                date: businessDate,
                openingBalance: openingBalance,
                closingBalance: closingBalance,
                expectedClosing: expectedClosing,
                cashDifference: cashDifference,
                dayRevenue: dayRevenue.orderRevenue,
                dayExpenses: dayExpenses.totalExpenses,
                netCashFlow: this.financialEngine.calculate('subtract', 
                    dayRevenue.orderRevenue, dayExpenses.totalExpenses)
            };

        } catch (error) {
            console.error('❌ Cash flow calculation failed:', error);
            return {
                date: date || this.financialEngine.getCurrentBusinessDate(),
                openingBalance: 0,
                closingBalance: 0,
                expectedClosing: 0,
                cashDifference: 0,
                dayRevenue: 0,
                dayExpenses: 0,
                netCashFlow: 0
            };
        }
    }

    /**
     * Calculate today's business metrics
     */
    calculateTodayMetrics() {
        try {
            const businessDate = this.financialEngine.getCurrentBusinessDate();
            const dateRange = this.financialEngine.getBusinessDateRange(businessDate);

            const revenue = this.calculateRevenue(dateRange.start, dateRange.end);
            const expenses = this.calculateExpenses(businessDate, businessDate);
            const profitLoss = this.calculateProfitLoss(dateRange.start, dateRange.end);
            const cashFlow = this.calculateCashFlow(businessDate);

            return {
                businessDate: businessDate,
                revenue: revenue,
                expenses: expenses,
                profitLoss: profitLoss,
                cashFlow: cashFlow,
                calculatedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ Today metrics calculation failed:', error);
            return this.getEmptyMetrics();
        }
    }

    /**
     * Calculate metrics for a specific date range
     */
    calculatePeriodMetrics(startDate, endDate) {
        try {
            const revenue = this.calculateRevenue(startDate, endDate);
            const expenses = this.calculateExpenses(startDate, endDate);
            const profitLoss = this.calculateProfitLoss(startDate, endDate);

            // Calculate daily averages
            const daysDiff = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1;
            
            const dailyAverages = {
                revenue: daysDiff > 0 ? this.financialEngine.calculate('divide', revenue.totalRevenue, daysDiff) : 0,
                expenses: daysDiff > 0 ? this.financialEngine.calculate('divide', expenses.totalExpenses, daysDiff) : 0,
                profit: daysDiff > 0 ? this.financialEngine.calculate('divide', profitLoss.grossProfit, daysDiff) : 0
            };

            return {
                startDate: startDate,
                endDate: endDate,
                dayCount: daysDiff,
                revenue: revenue,
                expenses: expenses,
                profitLoss: profitLoss,
                dailyAverages: dailyAverages,
                calculatedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ Period metrics calculation failed:', error);
            return this.getEmptyPeriodMetrics(startDate, endDate);
        }
    }

    /**
     * Calculate top-selling items
     */
    calculateTopItems(startDate, endDate, limit = 10) {
        try {
            const orders = this.databaseManager.read('orders', {
                status: 'completed'
            }).filter(order => {
                // Handle different date field names for compatibility
                const orderDate = new Date(order.created_at || order.createdAt || order.date);
                const start = new Date(startDate);
                const end = new Date(endDate);
                return orderDate >= start && orderDate <= end;
            });

            const itemStats = {};

            orders.forEach(order => {
                if (order.items && Array.isArray(order.items)) {
                    order.items.forEach(item => {
                        const itemName = item.name;
                        if (!itemStats[itemName]) {
                            itemStats[itemName] = {
                                name: itemName,
                                quantity: 0,
                                revenue: 0,
                                orderCount: 0
                            };
                        }

                        itemStats[itemName].quantity = this.financialEngine.calculate('add',
                            itemStats[itemName].quantity, item.quantity || 0);
                        
                        const itemRevenue = this.financialEngine.calculate('multiply',
                            item.price || 0, item.quantity || 0);
                        
                        itemStats[itemName].revenue = this.financialEngine.calculate('add',
                            itemStats[itemName].revenue, itemRevenue);
                        
                        itemStats[itemName].orderCount += 1;
                    });
                }
            });

            // Sort by quantity and limit results
            const topItems = Object.values(itemStats)
                .sort((a, b) => b.quantity - a.quantity)
                .slice(0, limit);

            return topItems;

        } catch (error) {
            console.error('❌ Top items calculation failed:', error);
            return [];
        }
    }

    /**
     * Get empty metrics structure
     */
    getEmptyMetrics() {
        return {
            businessDate: this.financialEngine.getCurrentBusinessDate(),
            revenue: { totalRevenue: 0, orderRevenue: 0, udharPayments: 0, orderCount: 0, averageOrderValue: 0 },
            expenses: { totalExpenses: 0, expensesByCategory: {}, expenseCount: 0, averageExpense: 0 },
            profitLoss: { grossProfit: 0, profitMargin: 0, isProfit: false },
            cashFlow: { openingBalance: 0, closingBalance: 0, netCashFlow: 0 },
            calculatedAt: new Date().toISOString()
        };
    }

    /**
     * Get empty period metrics structure
     */
    getEmptyPeriodMetrics(startDate, endDate) {
        return {
            startDate: startDate,
            endDate: endDate,
            dayCount: 0,
            revenue: { totalRevenue: 0, orderRevenue: 0, udharPayments: 0, orderCount: 0, averageOrderValue: 0 },
            expenses: { totalExpenses: 0, expensesByCategory: {}, expenseCount: 0, averageExpense: 0 },
            profitLoss: { grossProfit: 0, profitMargin: 0, isProfit: false },
            dailyAverages: { revenue: 0, expenses: 0, profit: 0 },
            calculatedAt: new Date().toISOString()
        };
    }

    /**
     * Validate calculation results
     */
    validateResults(results) {
        try {
            // Check for NaN or infinite values
            const checkValue = (value, path = '') => {
                if (typeof value === 'number') {
                    if (!Number.isFinite(value)) {
                        console.warn(`Invalid number at ${path}:`, value);
                        return 0;
                    }
                }
                return value;
            };

            // Recursively validate all numeric values
            const validateObject = (obj, path = '') => {
                if (typeof obj === 'object' && obj !== null) {
                    for (const [key, value] of Object.entries(obj)) {
                        const currentPath = path ? `${path}.${key}` : key;
                        if (typeof value === 'object') {
                            validateObject(value, currentPath);
                        } else {
                            obj[key] = checkValue(value, currentPath);
                        }
                    }
                }
            };

            validateObject(results);
            return results;

        } catch (error) {
            console.error('❌ Results validation failed:', error);
            return results;
        }
    }

    /**
     * Get financial calculations status
     */
    getStatus() {
        return {
            version: this.version,
            financialEngineStatus: this.financialEngine.getStatus(),
            databaseManagerStatus: this.databaseManager.getStatus(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Calculate Udhar summary
     */
    calculateUdharSummary() {
        try {
            const udhars = this.databaseManager.read('udhars');

            let totalUdharAmount = 0;
            let totalPaidAmount = 0;
            let totalRemainingAmount = 0;
            let activeCustomers = 0;

            udhars.forEach(udhar => {
                totalUdharAmount = this.financialEngine.calculate('add', totalUdharAmount, udhar.totalAmount || 0);
                totalPaidAmount = this.financialEngine.calculate('add', totalPaidAmount, udhar.paidAmount || 0);
                totalRemainingAmount = this.financialEngine.calculate('add', totalRemainingAmount, udhar.remainingAmount || 0);

                if (udhar.remainingAmount > 0) {
                    activeCustomers++;
                }
            });

            return {
                totalUdharAmount: totalUdharAmount,
                totalPaidAmount: totalPaidAmount,
                totalRemainingAmount: totalRemainingAmount,
                activeCustomers: activeCustomers,
                totalCustomers: udhars.length,
                recoveryRate: totalUdharAmount > 0 ?
                    this.financialEngine.calculate('percentage', totalPaidAmount, totalUdharAmount) : 0
            };

        } catch (error) {
            console.error('❌ Udhar summary calculation failed:', error);
            return {
                totalUdharAmount: 0,
                totalPaidAmount: 0,
                totalRemainingAmount: 0,
                activeCustomers: 0,
                totalCustomers: 0,
                recoveryRate: 0
            };
        }
    }

    /**
     * Calculate inventory value
     */
    calculateInventoryValue() {
        try {
            const inventory = this.databaseManager.read('inventory');

            let totalValue = 0;
            let lowStockItems = 0;
            let outOfStockItems = 0;

            inventory.forEach(item => {
                const itemValue = this.financialEngine.calculate('multiply',
                    item.quantity || 0, item.unitPrice || 0);
                totalValue = this.financialEngine.calculate('add', totalValue, itemValue);

                if (item.quantity <= 0) {
                    outOfStockItems++;
                } else if (item.quantity <= (item.lowStockThreshold || 0)) {
                    lowStockItems++;
                }
            });

            return {
                totalValue: totalValue,
                totalItems: inventory.length,
                lowStockItems: lowStockItems,
                outOfStockItems: outOfStockItems,
                inStockItems: inventory.length - outOfStockItems
            };

        } catch (error) {
            console.error('❌ Inventory value calculation failed:', error);
            return {
                totalValue: 0,
                totalItems: 0,
                lowStockItems: 0,
                outOfStockItems: 0,
                inStockItems: 0
            };
        }
    }

    /**
     * Calculate total per head charges for a specific date range
     */
    calculateTotalPerHeadCharges(startDate, endDate) {
        try {
            console.log('💰 Calculating total per head charges for:', { startDate, endDate });

            // Get completed orders within date range
            const allOrders = this.databaseManager.read('orders', { status: 'completed' });
            if (!Array.isArray(allOrders)) {
                console.warn('⚠️ No orders found or invalid orders data');
                return 0;
            }

            const orders = allOrders.filter(order => {
                if (!order || !order.createdAt) return false;

                const orderDate = new Date(order.createdAt);
                const start = new Date(startDate);
                const end = new Date(endDate);

                return orderDate >= start && orderDate <= end;
            });

            console.log('📊 Found orders for per head charges calculation:', orders.length);

            let totalPerHeadCharges = 0;

            orders.forEach(order => {
                // Check multiple possible field names for per head charges
                const perHeadCharge = order.per_head_charge ||
                                    order.perHeadCharge ||
                                    order.per_head_charges ||
                                    order.perHeadCharges ||
                                    0;

                if (perHeadCharge && perHeadCharge > 0) {
                    totalPerHeadCharges = this.financialEngine.calculate('add', totalPerHeadCharges, perHeadCharge);
                    console.log('📊 Added per head charge:', perHeadCharge, 'from order:', order.id);
                }
            });

            console.log('💰 Total per head charges calculated:', totalPerHeadCharges);
            return totalPerHeadCharges;

        } catch (error) {
            console.error('❌ Per head charges calculation failed:', error);
            return 0;
        }
    }

    /**
     * Calculate total daily wages for present staff on a specific date
     */
    calculateDailyWagesForPresentStaff(date) {
        try {
            console.log('👥 Calculating daily wages for present staff on:', date);

            // Get staff data from localStorage
            const staffData = JSON.parse(localStorage.getItem('staffMembers') || '[]');
            console.log('👥 Staff data loaded:', {
                count: staffData.length,
                staffNames: staffData.map(s => s.name),
                sampleStaff: staffData[0]
            });

            if (!Array.isArray(staffData) || staffData.length === 0) {
                console.warn('⚠️ No staff data found');
                return 0;
            }

            const targetDate = new Date(date).toISOString().split('T')[0];
            let totalDailyWages = 0;
            let presentStaffCount = 0;
            let debugInfo = [];

            staffData.forEach(staff => {
                console.log('👥 Checking staff:', staff.name, {
                    dailyWage: staff.dailyWage,
                    monthlySalary: staff.monthlySalary,
                    lastAttendanceDate: staff.lastAttendanceDate,
                    attendanceHistory: staff.attendanceHistory,
                    status: staff.status
                });

                // Check if staff is present today
                const isPresent = this.isStaffPresentOnDate(staff, targetDate);
                console.log('👥 Staff presence check for', staff.name, ':', isPresent);

                if (isPresent) {
                    const dailyWage = staff.dailyWage || staff.daily_wage ||
                                    (staff.monthlySalary ? staff.monthlySalary / 30 : 0) ||
                                    (staff.monthly_salary ? staff.monthly_salary / 30 : 0) || 0;

                    console.log('👥 Calculated daily wage for', staff.name, ':', dailyWage);

                    if (dailyWage > 0) {
                        totalDailyWages = this.financialEngine.calculate('add', totalDailyWages, dailyWage);
                        presentStaffCount++;
                        debugInfo.push({
                            name: staff.name,
                            wage: dailyWage,
                            source: staff.dailyWage ? 'dailyWage' : 'monthlySalary/30'
                        });
                        console.log('👥 Added daily wage:', dailyWage, 'for staff:', staff.name);
                    } else {
                        console.warn('👥 No valid wage found for present staff:', staff.name);
                    }
                } else {
                    console.log('👥 Staff not present:', staff.name);
                }
            });

            console.log('👥 Daily wages calculation complete:', {
                totalWages: totalDailyWages,
                presentStaffCount: presentStaffCount,
                targetDate: targetDate,
                debugInfo: debugInfo
            });

            return totalDailyWages;

        } catch (error) {
            console.error('❌ Daily wages calculation failed:', error);
            return 0;
        }
    }

    /**
     * Check if staff member is present on a specific date
     */
    isStaffPresentOnDate(staff, date) {
        try {
            console.log('👥 Checking attendance for', staff.name, 'on', date);

            // Check attendance history
            if (staff.attendanceHistory && Array.isArray(staff.attendanceHistory)) {
                console.log('👥 Checking attendanceHistory:', staff.attendanceHistory);
                const attendanceRecord = staff.attendanceHistory.find(record =>
                    record.date === date && record.status === 'present'
                );
                if (attendanceRecord) {
                    console.log('👥 Found in attendanceHistory:', attendanceRecord);
                    return true;
                }
            }

            // Check lastAttendanceDate
            if (staff.lastAttendanceDate === date) {
                console.log('👥 Found in lastAttendanceDate:', staff.lastAttendanceDate);
                return true;
            }

            // Check attendance object (alternative format)
            if (staff.attendance && staff.attendance[date] && staff.attendance[date].present) {
                console.log('👥 Found in attendance object:', staff.attendance[date]);
                return true;
            }

            // For testing purposes, if staff is active and has no attendance data, assume present
            // This helps with debugging when attendance hasn't been properly set up
            if (staff.status === 'active' && (!staff.attendanceHistory || staff.attendanceHistory.length === 0)) {
                console.log('👥 Active staff with no attendance history - assuming present for testing');
                return true;
            }

            console.log('👥 Staff not present on', date);
            return false;

        } catch (error) {
            console.error('❌ Failed to check staff attendance:', error);
            return false;
        }
    }
}

// Export the financial calculations for global use
window.ZaiqaFinancialCalculations = ZaiqaFinancialCalculations;
