/**
 * Financial Health Dashboard Debug Tool
 * This script helps debug the Financial Health Dashboard issues
 */

class FinancialDashboardDebugger {
    constructor() {
        this.init();
    }

    init() {
        console.log('🔧 Financial Dashboard Debugger initialized');
        this.addDebugButton();
        this.setupConsoleLogging();
    }

    addDebugButton() {
        // Remove existing debug buttons if present
        const existingButton = document.getElementById('financialDebugButton');
        if (existingButton) {
            existingButton.remove();
        }
        const existingTriggerButton = document.getElementById('financialTriggerButton');
        if (existingTriggerButton) {
            existingTriggerButton.remove();
        }

        // Main debug button
        const debugButton = document.createElement('button');
        debugButton.id = 'financialDebugButton';
        debugButton.innerHTML = '<i class="fas fa-bug"></i> Debug Financial Dashboard';
        debugButton.className = 'btn btn-warning btn-sm';
        debugButton.style.position = 'fixed';
        debugButton.style.bottom = '70px';
        debugButton.style.right = '20px';
        debugButton.style.zIndex = '9999';
        debugButton.onclick = () => this.runFullDiagnostic();
        document.body.appendChild(debugButton);

        // Trigger dashboard button
        const triggerButton = document.createElement('button');
        triggerButton.id = 'financialTriggerButton';
        triggerButton.innerHTML = '<i class="fas fa-chart-pie"></i> Load Dashboard';
        triggerButton.className = 'btn btn-success btn-sm';
        triggerButton.style.position = 'fixed';
        triggerButton.style.bottom = '110px';
        triggerButton.style.right = '20px';
        triggerButton.style.zIndex = '9999';
        triggerButton.onclick = () => this.triggerDashboard();
        document.body.appendChild(triggerButton);
    }

    triggerDashboard() {
        console.log('🔧 Manually triggering Financial Health Dashboard...');

        if (window.enhancedReportsPage && typeof window.enhancedReportsPage.triggerFinancialHealthDashboard === 'function') {
            window.enhancedReportsPage.triggerFinancialHealthDashboard();
        } else {
            console.error('❌ Enhanced Reports Page or trigger function not available');
        }
    }

    setupConsoleLogging() {
        console.log('🔧 Setting up enhanced console logging for Financial Dashboard');
    }

    runFullDiagnostic() {
        console.clear();
        console.log('🔧 ===== FINANCIAL DASHBOARD FULL DIAGNOSTIC =====');
        
        this.checkLocalStorageData();
        this.checkFinancialCalculations();
        this.checkDashboardElements();
        this.testCalculationFunctions();
        this.generateTestData();
        
        console.log('🔧 ===== DIAGNOSTIC COMPLETE =====');
    }

    checkLocalStorageData() {
        console.log('🔧 1. CHECKING LOCALSTORAGE DATA:');
        
        // Check orders
        const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
        console.log('📊 Orders:', {
            count: orders.length,
            sample: orders.slice(0, 3),
            serviceTypes: orders.map(o => o.service_type || o.serviceType),
            tableNumbers: orders.map(o => o.table_number),
            totals: orders.map(o => o.total_amount || o.totalAmount)
        });

        // Check staff
        const staff = JSON.parse(localStorage.getItem('staffMembers') || '[]');
        console.log('👥 Staff:', {
            count: staff.length,
            sample: staff.slice(0, 2),
            wages: staff.map(s => ({ name: s.name, dailyWage: s.dailyWage, monthlySalary: s.monthlySalary })),
            attendance: staff.map(s => ({ name: s.name, lastAttendanceDate: s.lastAttendanceDate, attendanceHistory: s.attendanceHistory }))
        });

        // Check expenses
        const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
        console.log('💸 Expenses:', {
            count: expenses.length,
            sample: expenses.slice(0, 3),
            categories: expenses.map(e => e.category),
            amounts: expenses.map(e => e.amount)
        });
    }

    checkFinancialCalculations() {
        console.log('🔧 2. CHECKING FINANCIAL CALCULATIONS:');
        
        try {
            if (typeof ZaiqaFinancialCalculations !== 'undefined' && window.zaiqaFinancialEngine && window.zaiqaDatabaseManager) {
                const financialCalc = new ZaiqaFinancialCalculations(window.zaiqaFinancialEngine, window.zaiqaDatabaseManager);
                const today = new Date().toISOString().split('T')[0];
                
                // Test revenue calculation
                console.log('📊 Testing revenue calculation...');
                const revenue = financialCalc.calculateRevenue(today, today);
                console.log('📊 Revenue result:', revenue);
                
                // Test daily wages calculation
                console.log('👥 Testing daily wages calculation...');
                const dailyWages = financialCalc.calculateDailyWagesForPresentStaff(today);
                console.log('👥 Daily wages result:', dailyWages);
                
                // Test expense calculation
                console.log('💸 Testing expense calculation...');
                const expenses = financialCalc.calculateExpenses(today, today);
                console.log('💸 Expenses result:', expenses);
                
            } else {
                console.error('❌ Financial calculation classes not available');
            }
        } catch (error) {
            console.error('❌ Error in financial calculations:', error);
        }
    }

    checkDashboardElements() {
        console.log('🔧 3. CHECKING DASHBOARD ELEMENTS:');
        
        const elements = [
            'financialDineInRevenue',
            'financialTakeawayRevenue', 
            'financialPerHeadCharges',
            'financialTotalRevenue',
            'financialOperationalCosts',
            'financialSupplierPayments',
            'financialStaffCosts',
            'financialOwnerWithdrawals',
            'financialTotalExpenses'
        ];
        
        elements.forEach(elementId => {
            const element = document.getElementById(elementId);
            console.log(`🔧 Element ${elementId}:`, {
                exists: !!element,
                value: element ? element.textContent : 'N/A',
                visible: element ? element.offsetParent !== null : false
            });
        });
    }

    testCalculationFunctions() {
        console.log('🔧 4. TESTING CALCULATION FUNCTIONS:');

        try {
            if (window.enhancedReportsPage) {
                console.log('📊 Enhanced Reports Page available');

                // Try the new trigger function first
                if (typeof window.enhancedReportsPage.triggerFinancialHealthDashboard === 'function') {
                    console.log('📊 Using triggerFinancialHealthDashboard...');
                    window.enhancedReportsPage.triggerFinancialHealthDashboard();
                } else if (typeof window.enhancedReportsPage.loadFinancialHealthDashboard === 'function') {
                    console.log('📊 Using loadFinancialHealthDashboard...');
                    window.enhancedReportsPage.loadFinancialHealthDashboard();
                } else {
                    console.error('❌ No Financial Health Dashboard functions available');
                }
            } else {
                console.error('❌ Enhanced Reports Page not available');
            }
        } catch (error) {
            console.error('❌ Error testing calculation functions:', error);
        }
    }

    generateTestData() {
        console.log('🔧 5. GENERATING FRESH TEST DATA:');
        
        const today = new Date().toISOString().split('T')[0];
        
        // Clear existing test data
        this.clearTestData();
        
        // Generate test orders
        const testOrders = [
            {
                id: 'debug_dine_1',
                order_number: 'DBG001',
                total_amount: 1000,
                per_head_charge: 100,
                service_type: 'dine_in',
                table_number: 'Table 1',
                status: 'completed',
                created_at: new Date().toISOString(),
                createdAt: new Date().toISOString()
            },
            {
                id: 'debug_dine_2', 
                order_number: 'DBG002',
                total_amount: 1500,
                per_head_charge: 150,
                service_type: 'dine-in',
                table_number: 'Table 2',
                status: 'completed',
                created_at: new Date().toISOString(),
                createdAt: new Date().toISOString()
            },
            {
                id: 'debug_takeaway_1',
                order_number: 'DBG003',
                total_amount: 800,
                per_head_charge: 0,
                service_type: 'takeaway',
                table_number: 'Takeaway',
                status: 'completed',
                created_at: new Date().toISOString(),
                createdAt: new Date().toISOString()
            }
        ];
        
        // Generate test staff
        const testStaff = [
            {
                id: 'debug_staff_1',
                name: 'Debug Staff 1',
                dailyWage: 1500,
                status: 'active',
                lastAttendanceDate: today,
                attendanceHistory: [{ date: today, status: 'present' }]
            },
            {
                id: 'debug_staff_2',
                name: 'Debug Staff 2', 
                dailyWage: 2000,
                status: 'active',
                lastAttendanceDate: today,
                attendanceHistory: [{ date: today, status: 'present' }]
            }
        ];
        
        // Generate test expenses
        const testExpenses = [
            {
                id: 'debug_expense_1',
                category: 'Supplier Payment',
                amount: 3000,
                date: today,
                created_at: new Date().toISOString()
            },
            {
                id: 'debug_expense_2',
                category: 'Staff Wages',
                amount: 3500,
                date: today,
                created_at: new Date().toISOString()
            }
        ];
        
        // Save to localStorage
        const existingOrders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
        const existingStaff = JSON.parse(localStorage.getItem('staffMembers') || '[]');
        const existingExpenses = JSON.parse(localStorage.getItem('expenses') || '[]');
        
        localStorage.setItem('restaurantOrders', JSON.stringify([...existingOrders, ...testOrders]));
        localStorage.setItem('staffMembers', JSON.stringify([...existingStaff, ...testStaff]));
        localStorage.setItem('expenses', JSON.stringify([...existingExpenses, ...testExpenses]));
        
        console.log('🔧 Test data generated successfully');
        
        // Refresh dashboard
        setTimeout(() => {
            this.testCalculationFunctions();
        }, 1000);
    }

    clearTestData() {
        const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
        const staff = JSON.parse(localStorage.getItem('staffMembers') || '[]');
        const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
        
        const filteredOrders = orders.filter(o => !o.id.startsWith('debug_') && !o.id.startsWith('test_'));
        const filteredStaff = staff.filter(s => !s.id.startsWith('debug_') && !s.id.startsWith('test_'));
        const filteredExpenses = expenses.filter(e => !e.id.startsWith('debug_') && !e.id.startsWith('test_'));
        
        localStorage.setItem('restaurantOrders', JSON.stringify(filteredOrders));
        localStorage.setItem('staffMembers', JSON.stringify(filteredStaff));
        localStorage.setItem('expenses', JSON.stringify(filteredExpenses));
        
        console.log('🔧 Test data cleared');
    }
}

// Initialize debugger when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.financialDashboardDebugger = new FinancialDashboardDebugger();
    }, 3000);
});
