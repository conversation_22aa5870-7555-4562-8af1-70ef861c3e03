/**
 * Keyboard Shortcuts Diagnostic Tool
 * Helps identify and fix initialization issues
 */

class KeyboardShortcutsDiagnostic {
    constructor() {
        this.version = '1.0.0';
        this.checks = [];
    }

    /**
     * Run comprehensive diagnostic
     */
    runDiagnostic() {
        console.log('🔍 KEYBOARD SHORTCUTS DIAGNOSTIC v' + this.version);
        console.log('=' .repeat(60));

        this.checks = [];

        // Check 1: Environment
        this.checkEnvironment();

        // Check 2: Script Loading
        this.checkScriptLoading();

        // Check 3: Class Definition
        this.checkClassDefinition();

        // Check 4: Instance Creation
        this.checkInstanceCreation();

        // Check 5: Initialization Status
        this.checkInitializationStatus();

        // Check 6: Event Listeners
        this.checkEventListeners();

        // Check 7: DOM State
        this.checkDOMState();

        // Generate report
        this.generateReport();

        // Provide recommendations
        this.provideRecommendations();

        return this.checks;
    }

    /**
     * Check 1: Environment
     */
    checkEnvironment() {
        const check = {
            name: 'Environment Check',
            passed: false,
            details: {}
        };

        try {
            check.details.hasWindow = typeof window !== 'undefined';
            check.details.hasDocument = typeof document !== 'undefined';
            check.details.hasLocalStorage = typeof localStorage !== 'undefined';
            check.details.hasConsole = typeof console !== 'undefined';
            check.details.domReadyState = document.readyState;
            check.details.windowLoaded = document.readyState === 'complete';

            check.passed = check.details.hasWindow && check.details.hasDocument && check.details.hasLocalStorage;

            console.log('✅ Environment Check:', check.passed ? 'PASSED' : 'FAILED');
            console.log('   - Window:', check.details.hasWindow);
            console.log('   - Document:', check.details.hasDocument);
            console.log('   - LocalStorage:', check.details.hasLocalStorage);
            console.log('   - DOM Ready State:', check.details.domReadyState);

        } catch (error) {
            check.error = error.message;
            console.error('❌ Environment Check failed:', error);
        }

        this.checks.push(check);
    }

    /**
     * Check 2: Script Loading
     */
    checkScriptLoading() {
        const check = {
            name: 'Script Loading Check',
            passed: false,
            details: {}
        };

        try {
            // Check if keyboard shortcuts script is loaded
            const scripts = Array.from(document.querySelectorAll('script'));
            const keyboardScript = scripts.find(script => 
                script.src && script.src.includes('keyboard-shortcuts.js')
            );

            check.details.scriptTagExists = !!keyboardScript;
            check.details.scriptSrc = keyboardScript ? keyboardScript.src : 'Not found';
            check.details.scriptLoaded = keyboardScript ? !keyboardScript.hasAttribute('async') || keyboardScript.readyState === 'complete' : false;

            // Check CSS loading
            const links = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
            const cssLink = links.find(link => 
                link.href && link.href.includes('keyboard-shortcuts.css')
            );

            check.details.cssExists = !!cssLink;
            check.details.cssHref = cssLink ? cssLink.href : 'Not found';

            check.passed = check.details.scriptTagExists;

            console.log('✅ Script Loading Check:', check.passed ? 'PASSED' : 'FAILED');
            console.log('   - Script tag exists:', check.details.scriptTagExists);
            console.log('   - Script source:', check.details.scriptSrc);
            console.log('   - CSS exists:', check.details.cssExists);

        } catch (error) {
            check.error = error.message;
            console.error('❌ Script Loading Check failed:', error);
        }

        this.checks.push(check);
    }

    /**
     * Check 3: Class Definition
     */
    checkClassDefinition() {
        const check = {
            name: 'Class Definition Check',
            passed: false,
            details: {}
        };

        try {
            check.details.classExists = typeof ZaiqaKeyboardShortcuts !== 'undefined';
            check.details.isFunction = typeof ZaiqaKeyboardShortcuts === 'function';
            check.details.hasPrototype = check.details.classExists && ZaiqaKeyboardShortcuts.prototype !== undefined;

            if (check.details.classExists) {
                const prototype = ZaiqaKeyboardShortcuts.prototype;
                check.details.methods = {
                    init: typeof prototype.init === 'function',
                    loadDefaultShortcuts: typeof prototype.loadDefaultShortcuts === 'function',
                    setupEventListeners: typeof prototype.setupEventListeners === 'function',
                    handleKeyDown: typeof prototype.handleKeyDown === 'function'
                };
            }

            check.passed = check.details.classExists && check.details.isFunction;

            console.log('✅ Class Definition Check:', check.passed ? 'PASSED' : 'FAILED');
            console.log('   - Class exists:', check.details.classExists);
            console.log('   - Is function:', check.details.isFunction);
            if (check.details.methods) {
                console.log('   - Methods:', check.details.methods);
            }

        } catch (error) {
            check.error = error.message;
            console.error('❌ Class Definition Check failed:', error);
        }

        this.checks.push(check);
    }

    /**
     * Check 4: Instance Creation
     */
    checkInstanceCreation() {
        const check = {
            name: 'Instance Creation Check',
            passed: false,
            details: {}
        };

        try {
            check.details.instanceExists = typeof window.keyboardShortcuts !== 'undefined';
            check.details.isObject = check.details.instanceExists && typeof window.keyboardShortcuts === 'object';
            check.details.hasVersion = check.details.instanceExists && !!window.keyboardShortcuts.version;

            if (check.details.instanceExists) {
                check.details.instanceType = typeof window.keyboardShortcuts;
                check.details.constructor = window.keyboardShortcuts.constructor ? window.keyboardShortcuts.constructor.name : 'Unknown';
            }

            check.passed = check.details.instanceExists && check.details.isObject;

            console.log('✅ Instance Creation Check:', check.passed ? 'PASSED' : 'FAILED');
            console.log('   - Instance exists:', check.details.instanceExists);
            console.log('   - Is object:', check.details.isObject);
            console.log('   - Has version:', check.details.hasVersion);

        } catch (error) {
            check.error = error.message;
            console.error('❌ Instance Creation Check failed:', error);
        }

        this.checks.push(check);
    }

    /**
     * Check 5: Initialization Status
     */
    checkInitializationStatus() {
        const check = {
            name: 'Initialization Status Check',
            passed: false,
            details: {}
        };

        try {
            if (window.keyboardShortcuts) {
                check.details.initialized = window.keyboardShortcuts.initialized;
                check.details.hasShortcuts = window.keyboardShortcuts.shortcuts && Object.keys(window.keyboardShortcuts.shortcuts).length > 0;
                check.details.hasCustomShortcuts = window.keyboardShortcuts.customShortcuts && Object.keys(window.keyboardShortcuts.customShortcuts).length >= 0;
                check.details.hasGetStatus = typeof window.keyboardShortcuts.getStatus === 'function';

                if (check.details.hasGetStatus) {
                    try {
                        check.details.status = window.keyboardShortcuts.getStatus();
                    } catch (statusError) {
                        check.details.statusError = statusError.message;
                    }
                }

                check.passed = check.details.initialized && check.details.hasShortcuts;
            } else {
                check.details.noInstance = true;
            }

            console.log('✅ Initialization Status Check:', check.passed ? 'PASSED' : 'FAILED');
            if (window.keyboardShortcuts) {
                console.log('   - Initialized:', check.details.initialized);
                console.log('   - Has shortcuts:', check.details.hasShortcuts);
                console.log('   - Has custom shortcuts:', check.details.hasCustomShortcuts);
                if (check.details.status) {
                    console.log('   - Status:', check.details.status);
                }
            } else {
                console.log('   - No instance found');
            }

        } catch (error) {
            check.error = error.message;
            console.error('❌ Initialization Status Check failed:', error);
        }

        this.checks.push(check);
    }

    /**
     * Check 6: Event Listeners
     */
    checkEventListeners() {
        const check = {
            name: 'Event Listeners Check',
            passed: false,
            details: {}
        };

        try {
            // This is harder to check directly, but we can test if keydown events work
            check.details.documentExists = !!document;
            check.details.canAddEventListener = typeof document.addEventListener === 'function';

            // Check if there are any keydown listeners (approximate)
            check.details.hasKeydownListeners = true; // We can't easily check this

            check.passed = check.details.documentExists && check.details.canAddEventListener;

            console.log('✅ Event Listeners Check:', check.passed ? 'PASSED' : 'FAILED');
            console.log('   - Document exists:', check.details.documentExists);
            console.log('   - Can add event listeners:', check.details.canAddEventListener);

        } catch (error) {
            check.error = error.message;
            console.error('❌ Event Listeners Check failed:', error);
        }

        this.checks.push(check);
    }

    /**
     * Check 7: DOM State
     */
    checkDOMState() {
        const check = {
            name: 'DOM State Check',
            passed: false,
            details: {}
        };

        try {
            check.details.readyState = document.readyState;
            check.details.bodyExists = !!document.body;
            check.details.headExists = !!document.head;
            check.details.scriptsCount = document.querySelectorAll('script').length;
            check.details.linksCount = document.querySelectorAll('link').length;

            check.passed = check.details.readyState !== 'loading' && check.details.bodyExists;

            console.log('✅ DOM State Check:', check.passed ? 'PASSED' : 'FAILED');
            console.log('   - Ready state:', check.details.readyState);
            console.log('   - Body exists:', check.details.bodyExists);
            console.log('   - Scripts count:', check.details.scriptsCount);
            console.log('   - Links count:', check.details.linksCount);

        } catch (error) {
            check.error = error.message;
            console.error('❌ DOM State Check failed:', error);
        }

        this.checks.push(check);
    }

    /**
     * Generate diagnostic report
     */
    generateReport() {
        console.log('\n📋 DIAGNOSTIC REPORT');
        console.log('=' .repeat(40));

        const passed = this.checks.filter(check => check.passed).length;
        const total = this.checks.length;

        console.log(`Checks Passed: ${passed}/${total}`);
        console.log(`Success Rate: ${Math.round((passed / total) * 100)}%`);

        console.log('\nFailed Checks:');
        this.checks.filter(check => !check.passed).forEach(check => {
            console.log(`❌ ${check.name}`);
            if (check.error) {
                console.log(`   Error: ${check.error}`);
            }
        });
    }

    /**
     * Provide recommendations
     */
    provideRecommendations() {
        console.log('\n💡 RECOMMENDATIONS');
        console.log('=' .repeat(40));

        const failedChecks = this.checks.filter(check => !check.passed);

        if (failedChecks.length === 0) {
            console.log('✅ All checks passed! Keyboard shortcuts should be working.');
            return;
        }

        failedChecks.forEach(check => {
            switch (check.name) {
                case 'Environment Check':
                    console.log('🔧 Environment issues detected:');
                    console.log('   - Ensure you\'re running in a browser environment');
                    console.log('   - Check if localStorage is available');
                    break;

                case 'Script Loading Check':
                    console.log('🔧 Script loading issues detected:');
                    console.log('   - Verify keyboard-shortcuts.js is included in HTML');
                    console.log('   - Check script path and ensure file exists');
                    console.log('   - Try: <script src="assets/js/keyboard-shortcuts.js"></script>');
                    break;

                case 'Class Definition Check':
                    console.log('🔧 Class definition issues detected:');
                    console.log('   - Script may not have loaded properly');
                    console.log('   - Check for JavaScript errors in console');
                    console.log('   - Try refreshing the page');
                    break;

                case 'Instance Creation Check':
                    console.log('🔧 Instance creation issues detected:');
                    console.log('   - Try manually: window.forceInitKeyboardShortcuts()');
                    console.log('   - Check initialization timing');
                    break;

                case 'Initialization Status Check':
                    console.log('🔧 Initialization issues detected:');
                    console.log('   - Try: window.initKeyboardShortcuts()');
                    console.log('   - Check for initialization errors');
                    break;
            }
        });

        console.log('\n🛠️ Quick Fixes:');
        console.log('1. Try: window.debugKeyboardShortcuts()');
        console.log('2. Try: window.forceInitKeyboardShortcuts()');
        console.log('3. Refresh the page and wait for full load');
        console.log('4. Check browser console for errors');
    }
}

// Global diagnostic function
window.diagnoseKeyboardShortcuts = function() {
    const diagnostic = new KeyboardShortcutsDiagnostic();
    return diagnostic.runDiagnostic();
};

// Auto-run diagnostic if keyboard shortcuts fail to load
setTimeout(() => {
    if (!window.keyboardShortcuts || !window.keyboardShortcuts.initialized) {
        console.log('⚠️ Keyboard shortcuts not initialized, running diagnostic...');
        window.diagnoseKeyboardShortcuts();
    }
}, 10000); // Wait 10 seconds before running diagnostic
