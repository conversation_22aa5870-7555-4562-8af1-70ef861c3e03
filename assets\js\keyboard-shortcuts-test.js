/**
 * Keyboard Shortcuts System Test Suite
 * Comprehensive testing for the keyboard shortcuts functionality
 */

class KeyboardShortcutsTest {
    constructor() {
        this.testResults = {};
        this.version = '1.0.0';
    }

    /**
     * Run all keyboard shortcuts tests
     */
    async runAllTests() {
        console.log('⌨️ Starting Keyboard Shortcuts Test Suite v' + this.version);
        console.log('=' .repeat(60));

        try {
            // Test 1: System Initialization
            await this.testSystemInitialization();

            // Test 2: Default Shortcuts Loading
            await this.testDefaultShortcuts();

            // Test 3: Custom Shortcuts Functionality
            await this.testCustomShortcuts();

            // Test 4: POS Shortcuts
            await this.testPOSShortcuts();

            // Test 5: Modal Controls
            await this.testModalControls();

            // Test 6: Navigation Shortcuts
            await this.testNavigationShortcuts();

            // Test 7: Help System
            await this.testHelpSystem();

            // Generate test report
            this.generateTestReport();

        } catch (error) {
            console.error('❌ Keyboard shortcuts test suite failed:', error);
        }
    }

    /**
     * Test 1: System Initialization
     */
    async testSystemInitialization() {
        console.log('\n1️⃣ Testing System Initialization...');

        try {
            // Check if the class exists
            const classExists = typeof ZaiqaKeyboardShortcuts !== 'undefined';
            console.log('⌨️ ZaiqaKeyboardShortcuts class exists:', classExists);

            // Check if the instance exists
            const systemExists = typeof window.keyboardShortcuts !== 'undefined';
            console.log('⌨️ Keyboard shortcuts system exists:', systemExists);

            // Check initialization status
            const isInitialized = systemExists && window.keyboardShortcuts.initialized;
            console.log('⌨️ System initialized:', isInitialized);

            // Check version
            const hasVersion = systemExists && window.keyboardShortcuts.version;
            console.log('⌨️ System version:', hasVersion);

            // Additional debug info
            console.log('⌨️ DOM ready state:', document.readyState);
            console.log('⌨️ Window loaded:', document.readyState === 'complete');

            if (systemExists) {
                try {
                    const status = window.keyboardShortcuts.getStatus();
                    console.log('⌨️ System status:', status);

                    // Run debug if available
                    if (typeof window.keyboardShortcuts.debug === 'function') {
                        console.log('⌨️ Running system debug...');
                        window.keyboardShortcuts.debug();
                    }
                } catch (statusError) {
                    console.warn('⚠️ Failed to get system status:', statusError);
                }
            } else {
                // Run global debug function
                if (typeof window.debugKeyboardShortcuts === 'function') {
                    console.log('⌨️ Running global debug...');
                    window.debugKeyboardShortcuts();
                }
            }

            this.testResults.systemInitialization = {
                passed: systemExists && isInitialized,
                classExists: classExists,
                systemExists: systemExists,
                isInitialized: isInitialized,
                hasVersion: !!hasVersion,
                status: systemExists ? (window.keyboardShortcuts.getStatus ? window.keyboardShortcuts.getStatus() : 'No getStatus method') : null
            };

            console.log('✅ System initialization test completed');

        } catch (error) {
            console.error('❌ System initialization test failed:', error);
            this.testResults.systemInitialization = { passed: false, error: error.message };
        }
    }

    /**
     * Test 2: Default Shortcuts Loading
     */
    async testDefaultShortcuts() {
        console.log('\n2️⃣ Testing Default Shortcuts...');
        
        try {
            if (!window.keyboardShortcuts) {
                throw new Error('Keyboard shortcuts system not available');
            }

            const allShortcuts = window.keyboardShortcuts.getAllShortcuts();
            const shortcutCount = Object.keys(allShortcuts).length;

            console.log('⌨️ Total shortcuts loaded:', shortcutCount);

            // Test specific important shortcuts
            const importantShortcuts = [
                'KeyP', // POS
                'KeyC', // Calculator
                'Escape', // Close modal
                'F1', // Help
                'KeyD', // Dashboard
                'KeyR' // Reports
            ];

            const availableImportant = importantShortcuts.filter(key => allShortcuts[key]);
            console.log('⌨️ Important shortcuts available:', availableImportant.length, '/', importantShortcuts.length);

            // Test shortcut categories
            const categories = {};
            Object.values(allShortcuts).forEach(shortcut => {
                const category = shortcut.category || 'Other';
                categories[category] = (categories[category] || 0) + 1;
            });

            console.log('⌨️ Shortcut categories:', Object.keys(categories));

            this.testResults.defaultShortcuts = {
                passed: shortcutCount > 20 && availableImportant.length >= 5,
                totalShortcuts: shortcutCount,
                importantShortcuts: availableImportant.length,
                categories: Object.keys(categories).length,
                categoryBreakdown: categories
            };

            console.log('✅ Default shortcuts test completed');

        } catch (error) {
            console.error('❌ Default shortcuts test failed:', error);
            this.testResults.defaultShortcuts = { passed: false, error: error.message };
        }
    }

    /**
     * Test 3: Custom Shortcuts Functionality
     */
    async testCustomShortcuts() {
        console.log('\n3️⃣ Testing Custom Shortcuts...');
        
        try {
            if (!window.keyboardShortcuts) {
                throw new Error('Keyboard shortcuts system not available');
            }

            // Test custom shortcut methods
            const hasLoadCustom = typeof window.keyboardShortcuts.loadCustomShortcuts === 'function';
            const hasSaveCustom = typeof window.keyboardShortcuts.saveCustomShortcuts === 'function';
            const hasAddCustom = typeof window.keyboardShortcuts.addCustomShortcut === 'function';

            console.log('⌨️ Custom shortcuts methods:');
            console.log('  - loadCustomShortcuts:', hasLoadCustom);
            console.log('  - saveCustomShortcuts:', hasSaveCustom);
            console.log('  - addCustomShortcut:', hasAddCustom);

            // Test custom shortcuts storage
            const customShortcuts = window.keyboardShortcuts.customShortcuts || {};
            const customCount = Object.keys(customShortcuts).length;

            console.log('⌨️ Current custom shortcuts:', customCount);

            this.testResults.customShortcuts = {
                passed: hasLoadCustom && hasSaveCustom && hasAddCustom,
                hasLoadCustom: hasLoadCustom,
                hasSaveCustom: hasSaveCustom,
                hasAddCustom: hasAddCustom,
                customCount: customCount
            };

            console.log('✅ Custom shortcuts test completed');

        } catch (error) {
            console.error('❌ Custom shortcuts test failed:', error);
            this.testResults.customShortcuts = { passed: false, error: error.message };
        }
    }

    /**
     * Test 4: POS Shortcuts
     */
    async testPOSShortcuts() {
        console.log('\n4️⃣ Testing POS Shortcuts...');
        
        try {
            if (!window.keyboardShortcuts) {
                throw new Error('Keyboard shortcuts system not available');
            }

            const allShortcuts = window.keyboardShortcuts.getAllShortcuts();
            
            // Find POS-specific shortcuts
            const posShortcuts = Object.entries(allShortcuts).filter(([key, shortcut]) => 
                shortcut.context === 'pos' || shortcut.category === 'POS'
            );

            console.log('⌨️ POS shortcuts found:', posShortcuts.length);

            // Test POS action methods
            const posActionMethods = [
                'executePOSAction',
                'selectPOSCategory',
                'addSelectedItemToCart',
                'clearPOSCart',
                'navigatePOSItems',
                'adjustPOSQuantity'
            ];

            const availablePOSMethods = posActionMethods.filter(method => 
                typeof window.keyboardShortcuts[method] === 'function'
            );

            console.log('⌨️ POS action methods available:', availablePOSMethods.length, '/', posActionMethods.length);

            this.testResults.posShortcuts = {
                passed: posShortcuts.length >= 10 && availablePOSMethods.length >= 5,
                posShortcutsCount: posShortcuts.length,
                posMethodsCount: availablePOSMethods.length,
                availableMethods: availablePOSMethods
            };

            console.log('✅ POS shortcuts test completed');

        } catch (error) {
            console.error('❌ POS shortcuts test failed:', error);
            this.testResults.posShortcuts = { passed: false, error: error.message };
        }
    }

    /**
     * Test 5: Modal Controls
     */
    async testModalControls() {
        console.log('\n5️⃣ Testing Modal Controls...');
        
        try {
            if (!window.keyboardShortcuts) {
                throw new Error('Keyboard shortcuts system not available');
            }

            // Test modal control methods
            const modalMethods = [
                'openModal',
                'closeCurrentModal',
                'confirmCurrentModal',
                'openCalculatorModal',
                'openGlobalSearchModal'
            ];

            const availableModalMethods = modalMethods.filter(method => 
                typeof window.keyboardShortcuts[method] === 'function'
            );

            console.log('⌨️ Modal control methods available:', availableModalMethods.length, '/', modalMethods.length);

            // Test modal shortcuts
            const allShortcuts = window.keyboardShortcuts.getAllShortcuts();
            const modalShortcuts = Object.entries(allShortcuts).filter(([key, shortcut]) => 
                shortcut.action === 'openModal' || shortcut.action === 'closeModal' || shortcut.category === 'Modals'
            );

            console.log('⌨️ Modal shortcuts found:', modalShortcuts.length);

            this.testResults.modalControls = {
                passed: availableModalMethods.length >= 4 && modalShortcuts.length >= 5,
                modalMethodsCount: availableModalMethods.length,
                modalShortcutsCount: modalShortcuts.length,
                availableMethods: availableModalMethods
            };

            console.log('✅ Modal controls test completed');

        } catch (error) {
            console.error('❌ Modal controls test failed:', error);
            this.testResults.modalControls = { passed: false, error: error.message };
        }
    }

    /**
     * Test 6: Navigation Shortcuts
     */
    async testNavigationShortcuts() {
        console.log('\n6️⃣ Testing Navigation Shortcuts...');
        
        try {
            if (!window.keyboardShortcuts) {
                throw new Error('Keyboard shortcuts system not available');
            }

            const allShortcuts = window.keyboardShortcuts.getAllShortcuts();
            
            // Find navigation shortcuts
            const navigationShortcuts = Object.entries(allShortcuts).filter(([key, shortcut]) => 
                shortcut.action === 'navigateTo' || shortcut.category === 'Navigation'
            );

            console.log('⌨️ Navigation shortcuts found:', navigationShortcuts.length);

            // Test navigation method
            const hasNavigateMethod = typeof window.keyboardShortcuts.navigateToPage === 'function';
            console.log('⌨️ Navigation method available:', hasNavigateMethod);

            // Test important navigation shortcuts
            const importantPages = ['KeyD', 'KeyT', 'KeyM', 'KeyI', 'KeyB', 'KeyR', 'KeyS'];
            const availablePageShortcuts = importantPages.filter(key => allShortcuts[key]);

            console.log('⌨️ Important page shortcuts available:', availablePageShortcuts.length, '/', importantPages.length);

            this.testResults.navigationShortcuts = {
                passed: navigationShortcuts.length >= 5 && hasNavigateMethod && availablePageShortcuts.length >= 6,
                navigationShortcutsCount: navigationShortcuts.length,
                hasNavigateMethod: hasNavigateMethod,
                pageShortcutsCount: availablePageShortcuts.length
            };

            console.log('✅ Navigation shortcuts test completed');

        } catch (error) {
            console.error('❌ Navigation shortcuts test failed:', error);
            this.testResults.navigationShortcuts = { passed: false, error: error.message };
        }
    }

    /**
     * Test 7: Help System
     */
    async testHelpSystem() {
        console.log('\n7️⃣ Testing Help System...');
        
        try {
            if (!window.keyboardShortcuts) {
                throw new Error('Keyboard shortcuts system not available');
            }

            // Test help methods
            const helpMethods = [
                'showShortcutsHelp',
                'generateShortcutsHelpContent',
                'formatKeyForDisplay',
                'openShortcutsSettings'
            ];

            const availableHelpMethods = helpMethods.filter(method => 
                typeof window.keyboardShortcuts[method] === 'function'
            );

            console.log('⌨️ Help system methods available:', availableHelpMethods.length, '/', helpMethods.length);

            // Test F1 help shortcut
            const allShortcuts = window.keyboardShortcuts.getAllShortcuts();
            const hasF1Help = allShortcuts['F1'] && allShortcuts['F1'].action === 'showHelp';

            console.log('⌨️ F1 help shortcut available:', hasF1Help);

            this.testResults.helpSystem = {
                passed: availableHelpMethods.length >= 3 && hasF1Help,
                helpMethodsCount: availableHelpMethods.length,
                hasF1Help: hasF1Help,
                availableMethods: availableHelpMethods
            };

            console.log('✅ Help system test completed');

        } catch (error) {
            console.error('❌ Help system test failed:', error);
            this.testResults.helpSystem = { passed: false, error: error.message };
        }
    }

    /**
     * Generate comprehensive test report
     */
    generateTestReport() {
        console.log('\n📋 KEYBOARD SHORTCUTS TEST REPORT');
        console.log('=' .repeat(60));

        const tests = [
            { name: 'System Initialization', key: 'systemInitialization' },
            { name: 'Default Shortcuts', key: 'defaultShortcuts' },
            { name: 'Custom Shortcuts', key: 'customShortcuts' },
            { name: 'POS Shortcuts', key: 'posShortcuts' },
            { name: 'Modal Controls', key: 'modalControls' },
            { name: 'Navigation Shortcuts', key: 'navigationShortcuts' },
            { name: 'Help System', key: 'helpSystem' }
        ];

        let passedTests = 0;
        let totalTests = tests.length;

        tests.forEach((test, index) => {
            const result = this.testResults[test.key];
            const status = result && result.passed ? '✅ PASSED' : '❌ FAILED';
            
            if (result && result.passed) {
                passedTests++;
            }

            console.log(`${index + 1}. ${test.name}: ${status}`);
            
            if (result && result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });

        console.log('\n📊 SUMMARY:');
        console.log(`Tests Passed: ${passedTests}/${totalTests}`);
        console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

        if (passedTests === totalTests) {
            console.log('🎉 All keyboard shortcuts are working correctly!');
            console.log('\n💡 USAGE INSTRUCTIONS:');
            console.log('• Press F1 to view all shortcuts');
            console.log('• Press P to open POS system');
            console.log('• Press C to open calculator');
            console.log('• Press Esc to close modals');
            console.log('• Use D/T/M/I/B/R/S for page navigation');
            console.log('• Go to Settings to customize shortcuts');
        } else {
            console.log('⚠️ Some shortcuts need attention. Check the detailed results above.');
        }

        console.log('\n📋 Detailed Results:');
        console.log(JSON.stringify(this.testResults, null, 2));

        return {
            passed: passedTests,
            total: totalTests,
            successRate: Math.round((passedTests / totalTests) * 100),
            results: this.testResults
        };
    }
}

// Export for global use
window.KeyboardShortcutsTest = KeyboardShortcutsTest;

// Manual test function
window.testKeyboardShortcuts = function() {
    waitForKeyboardShortcuts(() => {
        const testSuite = new KeyboardShortcutsTest();
        return testSuite.runAllTests();
    });
};

// Wait for keyboard shortcuts system to be ready
function waitForKeyboardShortcuts(callback, maxAttempts = 10, attempt = 1) {
    if (window.keyboardShortcuts && window.keyboardShortcuts.initialized) {
        callback();
    } else if (attempt < maxAttempts) {
        console.log(`⌨️ Waiting for keyboard shortcuts system... (attempt ${attempt}/${maxAttempts})`);
        setTimeout(() => {
            waitForKeyboardShortcuts(callback, maxAttempts, attempt + 1);
        }, 1000);
    } else {
        console.warn('⚠️ Keyboard shortcuts system not ready after maximum attempts');
        callback(); // Run tests anyway to show what's missing
    }
}

// Listen for keyboard shortcuts ready event
window.addEventListener('keyboardShortcutsReady', () => {
    console.log('⌨️ Keyboard shortcuts system ready event received');
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setTimeout(() => {
            console.log('⌨️ Auto-running Keyboard Shortcuts Test Suite...');
            const testSuite = new KeyboardShortcutsTest();
            testSuite.runAllTests();
        }, 1000);
    }
});

// Auto-run test if in development mode (fallback)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // Run tests after page load with proper waiting
    window.addEventListener('load', () => {
        setTimeout(() => {
            waitForKeyboardShortcuts(() => {
                console.log('⌨️ Auto-running Keyboard Shortcuts Test Suite...');
                const testSuite = new KeyboardShortcutsTest();
                testSuite.runAllTests();
            });
        }, 3000);
    });
}
