/**
 * Zaiqa Restaurant Keyboard Shortcuts System
 * Comprehensive keyboard shortcuts for enhanced productivity
 */

class ZaiqaKeyboardShortcuts {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        this.shortcuts = {};
        this.customShortcuts = {};
        this.isRecording = false;
        this.recordingCallback = null;
        
        this.init();
    }

    /**
     * Initialize the keyboard shortcuts system
     */
    init() {
        try {
            console.log('⌨️ Initializing Keyboard Shortcuts System v' + this.version);

            // Check if we're in a valid environment
            if (typeof document === 'undefined' || typeof window === 'undefined') {
                throw new Error('Invalid environment: document or window not available');
            }

            // Load default shortcuts
            this.loadDefaultShortcuts();
            console.log('✅ Default shortcuts loaded:', Object.keys(this.shortcuts).length);

            // Load custom shortcuts from localStorage
            this.loadCustomShortcuts();
            console.log('✅ Custom shortcuts loaded:', Object.keys(this.customShortcuts).length);

            // Set up event listeners
            this.setupEventListeners();
            console.log('✅ Event listeners set up');

            // Shortcuts help overlay will be implemented later
            console.log('ℹ️ Shortcuts help overlay - feature coming soon');

            // Create floating reference (after a delay to ensure DOM is ready)
            setTimeout(() => {
                try {
                    this.createFloatingReference();
                    console.log('✅ Floating reference created');
                } catch (refError) {
                    console.warn('⚠️ Failed to create floating reference:', refError);
                }
            }, 1000);

            this.initialized = true;
            console.log('✅ Keyboard Shortcuts System initialized successfully');

            // Update settings status if available
            setTimeout(() => {
                if (window.app && typeof window.app.updateShortcutsStatus === 'function') {
                    window.app.updateShortcutsStatus();
                }
            }, 2000);

        } catch (error) {
            console.error('❌ Keyboard Shortcuts System initialization failed:', error);
            this.initialized = false;
            throw error; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Load default keyboard shortcuts
     */
    loadDefaultShortcuts() {
        this.shortcuts = {
            // Global Navigation
            'KeyD': { action: 'navigateTo', params: ['dashboard'], description: 'Go to Dashboard', category: 'Navigation' },
            'KeyT': { action: 'navigateTo', params: ['tables'], description: 'Go to Tables', category: 'Navigation' },
            'KeyM': { action: 'navigateTo', params: ['menu'], description: 'Go to Menu', category: 'Navigation' },
            'KeyI': { action: 'navigateTo', params: ['inventory'], description: 'Go to Inventory', category: 'Navigation' },
            'KeyB': { action: 'navigateTo', params: ['billing'], description: 'Go to Billing', category: 'Navigation' },
            'KeyR': { action: 'navigateTo', params: ['reports'], description: 'Go to Reports', category: 'Navigation' },
            'KeyS': { action: 'navigateTo', params: ['settings'], description: 'Go to Settings', category: 'Navigation' },
            
            // Modals and Actions
            'KeyP': { action: 'openModal', params: ['pos'], description: 'Open POS System', category: 'Modals' },
            'KeyC': { action: 'openModal', params: ['calculator'], description: 'Open Calculator', category: 'Modals' },
            'KeyH': { action: 'openModal', params: ['cashInHand'], description: 'Open Cash in Hand', category: 'Modals' },
            'KeyU': { action: 'openModal', params: ['udhar'], description: 'Open Udhar Management', category: 'Modals' },
            'KeyE': { action: 'openModal', params: ['expenses'], description: 'Add New Expense', category: 'Modals' },
            'KeyN': { action: 'openModal', params: ['newOrder'], description: 'Create New Order', category: 'Modals' },
            
            // Modal Controls
            'Escape': { action: 'closeModal', params: [], description: 'Close Current Modal', category: 'Modal Controls' },
            'Enter': { action: 'confirmModal', params: [], description: 'Confirm/Submit Modal', category: 'Modal Controls' },
            
            // POS Shortcuts (when POS is open)
            'Digit1': { action: 'posAction', params: ['selectCategory', 0], description: 'Select Category 1', category: 'POS', context: 'pos' },
            'Digit2': { action: 'posAction', params: ['selectCategory', 1], description: 'Select Category 2', category: 'POS', context: 'pos' },
            'Digit3': { action: 'posAction', params: ['selectCategory', 2], description: 'Select Category 3', category: 'POS', context: 'pos' },
            'Digit4': { action: 'posAction', params: ['selectCategory', 3], description: 'Select Category 4', category: 'POS', context: 'pos' },
            'Digit5': { action: 'posAction', params: ['selectCategory', 4], description: 'Select Category 5', category: 'POS', context: 'pos' },
            
            // POS Cart Actions
            'KeyA': { action: 'posAction', params: ['addToCart'], description: 'Add Selected Item to Cart', category: 'POS', context: 'pos' },
            'KeyX': { action: 'posAction', params: ['clearCart'], description: 'Clear Cart', category: 'POS', context: 'pos' },
            'KeyF': { action: 'posAction', params: ['finalizeOrder'], description: 'Finalize Order', category: 'POS', context: 'pos' },
            'KeyQ': { action: 'posAction', params: ['quickPay'], description: 'Quick Cash Payment', category: 'POS', context: 'pos' },
            'KeyV': { action: 'posAction', params: ['viewCart'], description: 'View Cart Details', category: 'POS', context: 'pos' },
            'KeyZ': { action: 'posAction', params: ['removeLastItem'], description: 'Remove Last Item from Cart', category: 'POS', context: 'pos' },
            'KeyG': { action: 'posAction', params: ['applyDiscount'], description: 'Apply Discount', category: 'POS', context: 'pos' },
            'KeyT': { action: 'posAction', params: ['selectTable'], description: 'Select Table', category: 'POS', context: 'pos' },

            // POS Navigation
            'ArrowUp': { action: 'posAction', params: ['navigateUp'], description: 'Navigate Up in Items', category: 'POS', context: 'pos' },
            'ArrowDown': { action: 'posAction', params: ['navigateDown'], description: 'Navigate Down in Items', category: 'POS', context: 'pos' },
            'ArrowLeft': { action: 'posAction', params: ['navigateLeft'], description: 'Navigate Left in Categories', category: 'POS', context: 'pos' },
            'ArrowRight': { action: 'posAction', params: ['navigateRight'], description: 'Navigate Right in Categories', category: 'POS', context: 'pos' },

            // POS Quantity Controls
            'Equal': { action: 'posAction', params: ['increaseQuantity'], description: 'Increase Item Quantity', category: 'POS', context: 'pos' },
            'Minus': { action: 'posAction', params: ['decreaseQuantity'], description: 'Decrease Item Quantity', category: 'POS', context: 'pos' },
            
            // Quick Actions
            'F1': { action: 'showHelp', params: [], description: 'Show Keyboard Shortcuts Help', category: 'Help' },
            'F2': { action: 'openModal', params: ['search'], description: 'Global Search', category: 'Quick Actions' },
            'F3': { action: 'toggleTheme', params: [], description: 'Toggle Dark/Light Theme', category: 'Quick Actions' },
            'F4': { action: 'openModal', params: ['backup'], description: 'Quick Backup', category: 'Quick Actions' },
            
            // Modifier Combinations (Ctrl+Key)
            'ctrl+KeyS': { action: 'saveData', params: [], description: 'Save Current Data', category: 'System' },
            'ctrl+KeyZ': { action: 'undo', params: [], description: 'Undo Last Action', category: 'System' },
            'ctrl+KeyY': { action: 'redo', params: [], description: 'Redo Last Action', category: 'System' },
            'ctrl+KeyF': { action: 'openModal', params: ['search'], description: 'Find/Search', category: 'System' },
            'ctrl+KeyP': { action: 'printCurrent', params: [], description: 'Print Current Page/Modal', category: 'System' },
            
            // Alt Combinations (Alt+Key)
            'alt+KeyM': { action: 'openModal', params: ['menu'], description: 'Quick Menu Access', category: 'Quick Access' },
            'alt+KeyI': { action: 'openModal', params: ['inventory'], description: 'Quick Inventory Access', category: 'Quick Access' },
            'alt+KeyR': { action: 'openModal', params: ['reports'], description: 'Quick Reports Access', category: 'Quick Access' }
        };
    }

    /**
     * Load custom shortcuts from localStorage
     */
    loadCustomShortcuts() {
        try {
            const saved = localStorage.getItem('zaiqaCustomShortcuts');
            if (saved) {
                this.customShortcuts = JSON.parse(saved);
                console.log('⌨️ Loaded custom shortcuts:', Object.keys(this.customShortcuts).length);
            }
        } catch (error) {
            console.error('❌ Failed to load custom shortcuts:', error);
            this.customShortcuts = {};
        }
    }

    /**
     * Save custom shortcuts to localStorage
     */
    saveCustomShortcuts() {
        try {
            localStorage.setItem('zaiqaCustomShortcuts', JSON.stringify(this.customShortcuts));
            console.log('✅ Custom shortcuts saved');
        } catch (error) {
            console.error('❌ Failed to save custom shortcuts:', error);
        }
    }

    /**
     * Set up keyboard event listeners
     */
    setupEventListeners() {
        document.addEventListener('keydown', (event) => {
            this.handleKeyDown(event);
        });

        document.addEventListener('keyup', (event) => {
            this.handleKeyUp(event);
        });
    }

    /**
     * Handle keydown events
     */
    handleKeyDown(event) {
        // Don't process shortcuts if user is typing in input fields
        if (this.isTypingInInput(event.target)) {
            return;
        }

        // Don't process if recording shortcuts
        if (this.isRecording) {
            this.recordShortcut(event);
            return;
        }

        const shortcutKey = this.getShortcutKey(event);
        const shortcut = this.getShortcut(shortcutKey);

        if (shortcut) {
            event.preventDefault();
            event.stopPropagation();
            this.showShortcutActivation(shortcut);
            this.executeShortcut(shortcut, event);
        }
    }

    /**
     * Handle keyup events
     */
    handleKeyUp(event) {
        // Handle any keyup-specific logic here
    }

    /**
     * Check if user is typing in an input field
     */
    isTypingInInput(target) {
        const inputTypes = ['INPUT', 'TEXTAREA', 'SELECT'];
        const editableTypes = ['text', 'password', 'email', 'number', 'search', 'tel', 'url'];
        
        if (inputTypes.includes(target.tagName)) {
            if (target.tagName === 'INPUT') {
                return editableTypes.includes(target.type.toLowerCase());
            }
            return true;
        }
        
        return target.contentEditable === 'true';
    }

    /**
     * Get shortcut key string from event
     */
    getShortcutKey(event) {
        let key = event.code;
        
        if (event.ctrlKey) key = 'ctrl+' + key;
        if (event.altKey) key = 'alt+' + key;
        if (event.shiftKey) key = 'shift+' + key;
        if (event.metaKey) key = 'meta+' + key;
        
        return key;
    }

    /**
     * Get shortcut configuration
     */
    getShortcut(key) {
        // Check custom shortcuts first
        if (this.customShortcuts[key]) {
            return this.customShortcuts[key];
        }
        
        // Then check default shortcuts
        return this.shortcuts[key];
    }

    /**
     * Execute a shortcut action
     */
    executeShortcut(shortcut, event) {
        try {
            console.log('⌨️ Executing shortcut:', shortcut.description);
            
            // Check context if specified
            if (shortcut.context && !this.isInContext(shortcut.context)) {
                console.log('⌨️ Shortcut not available in current context');
                return;
            }

            switch (shortcut.action) {
                case 'navigateTo':
                    this.navigateToPage(shortcut.params[0]);
                    break;
                    
                case 'openModal':
                    this.openModal(shortcut.params[0]);
                    break;
                    
                case 'closeModal':
                    this.closeCurrentModal();
                    break;
                    
                case 'confirmModal':
                    this.confirmCurrentModal();
                    break;
                    
                case 'posAction':
                    this.executePOSAction(shortcut.params[0], shortcut.params[1]);
                    break;
                    
                case 'showHelp':
                    this.showShortcutsHelp();
                    break;
                    
                case 'toggleTheme':
                    this.toggleTheme();
                    break;
                    
                case 'saveData':
                    this.saveCurrentData();
                    break;
                    
                case 'printCurrent':
                    this.printCurrentView();
                    break;
                    
                default:
                    console.warn('⌨️ Unknown shortcut action:', shortcut.action);
            }
            
        } catch (error) {
            console.error('❌ Failed to execute shortcut:', error);
        }
    }

    /**
     * Check if we're in the specified context
     */
    isInContext(context) {
        switch (context) {
            case 'pos':
                return document.querySelector('.pos-modal') !== null;
            case 'modal':
                return document.querySelector('.modal-overlay') !== null;
            default:
                return true;
        }
    }

    /**
     * Show POS shortcuts help when POS modal opens
     */
    showPOSShortcutsHelp() {
        if (!this.isInContext('pos')) return;

        const posShortcuts = [
            { key: 'F', desc: 'Finalize Order' },
            { key: 'X', desc: 'Clear Cart' },
            { key: 'Q', desc: 'Quick Cash Payment' },
            { key: 'A', desc: 'Add Selected Item' },
            { key: '1-5', desc: 'Select Category' },
            { key: '↑↓', desc: 'Navigate Items' },
            { key: '←→', desc: 'Navigate Categories' },
            { key: '+/-', desc: 'Adjust Quantity' },
            { key: 'Esc', desc: 'Close POS' }
        ];

        const helpHTML = `
            <div class="pos-shortcuts-help" id="posShortcutsHelp">
                <div class="pos-shortcuts-header">
                    <span>⌨️ POS Shortcuts</span>
                    <button onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="pos-shortcuts-list">
                    ${posShortcuts.map(shortcut => `
                        <div class="pos-shortcut-item">
                            <span class="pos-shortcut-key">${shortcut.key}</span>
                            <span class="pos-shortcut-desc">${shortcut.desc}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        // Remove existing help if present
        const existingHelp = document.getElementById('posShortcutsHelp');
        if (existingHelp) {
            existingHelp.remove();
        }

        // Add to POS modal
        const posModal = document.querySelector('.pos-modal-content');
        if (posModal) {
            posModal.insertAdjacentHTML('beforeend', helpHTML);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                const help = document.getElementById('posShortcutsHelp');
                if (help) {
                    help.style.opacity = '0.7';
                }
            }, 5000);
        }
    }

    /**
     * Navigate to a page
     */
    navigateToPage(pageId) {
        const navLink = document.querySelector(`[data-page="${pageId}"]`);
        if (navLink) {
            navLink.click();
        } else if (window.app && typeof window.app.showPage === 'function') {
            window.app.showPage(pageId);
        }
    }

    /**
     * Open a modal
     */
    openModal(modalType) {
        if (!window.app) return;

        switch (modalType) {
            case 'pos':
                if (typeof window.app.openPOS === 'function') {
                    window.app.openPOS();
                    // Show POS shortcuts help after a brief delay
                    setTimeout(() => {
                        this.showPOSShortcutsHelp();
                    }, 1000);
                }
                break;
                
            case 'calculator':
                this.openCalculatorModal();
                break;
                
            case 'cashInHand':
                if (typeof window.app.showCashInHandModal === 'function') {
                    window.app.showCashInHandModal();
                }
                break;
                
            case 'udhar':
                if (typeof window.app.showUdharModal === 'function') {
                    window.app.showUdharModal();
                }
                break;
                
            case 'expenses':
                if (typeof window.app.showAddExpenseModal === 'function') {
                    window.app.showAddExpenseModal();
                }
                break;
                
            case 'search':
                this.openGlobalSearchModal();
                break;
                
            default:
                console.warn('⌨️ Unknown modal type:', modalType);
        }
    }

    /**
     * Close current modal
     */
    closeCurrentModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            const closeBtn = modal.querySelector('.modal-close');
            if (closeBtn) {
                closeBtn.click();
            } else {
                modal.remove();
            }
        }
    }

    /**
     * Confirm current modal
     */
    confirmCurrentModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            const confirmBtn = modal.querySelector('.btn-primary, .btn-success, [type="submit"]');
            if (confirmBtn && !confirmBtn.disabled) {
                confirmBtn.click();
            }
        }
    }

    /**
     * Execute POS-specific actions
     */
    executePOSAction(action, param) {
        if (!this.isInContext('pos')) {
            console.log('⌨️ POS shortcuts only available when POS is open');
            return;
        }

        switch (action) {
            case 'selectCategory':
                this.selectPOSCategory(param);
                break;

            case 'addToCart':
                this.addSelectedItemToCart();
                break;

            case 'clearCart':
                this.clearPOSCart();
                break;

            case 'finalizeOrder':
                this.finalizePOSOrder();
                break;

            case 'quickPay':
                this.quickCashPayment();
                break;

            case 'viewCart':
                this.viewPOSCart();
                break;

            case 'removeLastItem':
                this.removeLastCartItem();
                break;

            case 'applyDiscount':
                this.applyPOSDiscount();
                break;

            case 'selectTable':
                this.selectPOSTable();
                break;

            case 'navigateUp':
                this.navigatePOSItems('up');
                break;

            case 'navigateDown':
                this.navigatePOSItems('down');
                break;

            case 'navigateLeft':
                this.navigatePOSCategories('left');
                break;

            case 'navigateRight':
                this.navigatePOSCategories('right');
                break;

            case 'increaseQuantity':
                this.adjustPOSQuantity(1);
                break;

            case 'decreaseQuantity':
                this.adjustPOSQuantity(-1);
                break;

            default:
                console.warn('⌨️ Unknown POS action:', action);
        }
    }

    /**
     * Select POS category by index
     */
    selectPOSCategory(index) {
        // Look for category tabs in POS modal
        const categories = document.querySelectorAll('.tab-btn[data-category]');
        if (categories[index]) {
            categories[index].click();
            console.log(`⌨️ POS category ${index + 1} selected`);
        } else {
            console.log(`⌨️ POS category ${index + 1} not found`);
        }
    }

    /**
     * Add selected item to cart
     */
    addSelectedItemToCart() {
        // Look for selected menu item
        const selectedItem = document.querySelector('.menu-item.selected, .menu-item-card.selected');
        if (selectedItem) {
            // Find the add button within the selected item
            const addBtn = selectedItem.querySelector('.add-to-cart-btn, .btn-add, [onclick*="addToCart"]');
            if (addBtn) {
                addBtn.click();
                console.log('⌨️ Selected item added to cart');
            } else {
                // Try clicking the item itself
                selectedItem.click();
                console.log('⌨️ Item clicked (fallback)');
            }
        } else {
            // If no item selected, select and add first visible item
            const firstItem = document.querySelector('.menu-item-card, .menu-item');
            if (firstItem) {
                // First select the item
                this.selectMenuItem(firstItem);
                // Then add it to cart
                setTimeout(() => {
                    const addBtn = firstItem.querySelector('.add-to-cart-btn, .btn-add, [onclick*="addToCart"]');
                    if (addBtn) {
                        addBtn.click();
                        console.log('⌨️ First item selected and added to cart');
                    }
                }, 100);
            }
        }
    }

    /**
     * Helper method to select a menu item
     */
    selectMenuItem(item) {
        // Remove selection from all items
        document.querySelectorAll('.menu-item, .menu-item-card').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection to the specified item
        item.classList.add('selected');
        item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    /**
     * Clear POS cart
     */
    clearPOSCart() {
        // Look for the actual clear order button in POS modal
        const clearBtn = document.querySelector('.clear-order-btn');
        if (clearBtn) {
            clearBtn.click();
            console.log('⌨️ POS cart cleared via keyboard shortcut');
        } else {
            // Fallback: call the app method directly
            if (window.app && typeof window.app.clearPOSCart === 'function') {
                window.app.clearPOSCart();
                console.log('⌨️ POS cart cleared via app method');
            }
        }
    }

    /**
     * Finalize POS order
     */
    finalizePOSOrder() {
        // Look for the process payment button (Complete Order)
        const finalizeBtn = document.querySelector('.process-payment-btn');
        if (finalizeBtn && !finalizeBtn.disabled) {
            finalizeBtn.click();
            console.log('⌨️ POS order finalized via keyboard shortcut');
        } else {
            // Fallback: call the app method directly
            if (window.app && typeof window.app.processPOSPayment === 'function') {
                window.app.processPOSPayment();
                console.log('⌨️ POS order finalized via app method');
            }
        }
    }

    /**
     * Quick cash payment
     */
    quickCashPayment() {
        // First ensure cash payment method is selected
        const cashBtn = document.querySelector('.payment-btn[onclick*="cash"]');
        if (cashBtn) {
            cashBtn.click();
            console.log('⌨️ Cash payment method selected');

            // Then process the payment
            setTimeout(() => {
                this.finalizePOSOrder();
            }, 100);
        } else {
            // Fallback: select cash payment via app method
            if (window.app && typeof window.app.selectPOSPayment === 'function') {
                window.app.selectPOSPayment('cash');
                setTimeout(() => {
                    this.finalizePOSOrder();
                }, 100);
                console.log('⌨️ Cash payment selected and order finalized');
            }
        }
    }

    /**
     * View POS cart details
     */
    viewPOSCart() {
        const cartBtn = document.querySelector('.view-cart-btn, .cart-summary');
        if (cartBtn) {
            cartBtn.click();
        }
    }

    /**
     * Remove last item from cart
     */
    removeLastCartItem() {
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems.length > 0) {
            const lastItem = cartItems[cartItems.length - 1];
            const removeBtn = lastItem.querySelector('.remove-item-btn, .delete-btn');
            if (removeBtn) {
                removeBtn.click();
            }
        }
    }

    /**
     * Apply discount in POS
     */
    applyPOSDiscount() {
        const discountBtn = document.querySelector('.discount-btn, .apply-discount-btn');
        if (discountBtn) {
            discountBtn.click();
        }
    }

    /**
     * Select table in POS
     */
    selectPOSTable() {
        const tableBtn = document.querySelector('.select-table-btn, .table-selection-btn');
        if (tableBtn) {
            tableBtn.click();
        }
    }

    /**
     * Navigate POS items
     */
    navigatePOSItems(direction) {
        const items = document.querySelectorAll('.menu-item-card, .menu-item');
        const selectedItem = document.querySelector('.menu-item-card.selected, .menu-item.selected');

        if (items.length === 0) {
            console.log('⌨️ No menu items found for navigation');
            return;
        }

        let currentIndex = selectedItem ? Array.from(items).indexOf(selectedItem) : -1;

        if (direction === 'up') {
            // Navigate up (previous item)
            currentIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        } else if (direction === 'down') {
            // Navigate down (next item)
            currentIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        }

        // Remove previous selection
        items.forEach(item => item.classList.remove('selected'));

        // Add new selection
        if (items[currentIndex]) {
            this.selectMenuItem(items[currentIndex]);
            console.log(`⌨️ Navigated to item ${currentIndex + 1}/${items.length}`);
        }
    }

    /**
     * Navigate POS categories
     */
    navigatePOSCategories(direction) {
        const categories = document.querySelectorAll('.tab-btn[data-category]');
        const activeCategory = document.querySelector('.tab-btn.active');

        if (categories.length === 0) {
            console.log('⌨️ No categories found for navigation');
            return;
        }

        let currentIndex = activeCategory ? Array.from(categories).indexOf(activeCategory) : -1;

        if (direction === 'left') {
            currentIndex = currentIndex > 0 ? currentIndex - 1 : categories.length - 1;
        } else if (direction === 'right') {
            currentIndex = currentIndex < categories.length - 1 ? currentIndex + 1 : 0;
        }

        if (categories[currentIndex]) {
            categories[currentIndex].click();
            console.log(`⌨️ Navigated to category ${currentIndex + 1}/${categories.length}`);
        }
    }

    /**
     * Adjust POS item quantity
     */
    adjustPOSQuantity(change) {
        // Look for quantity controls in the cart or selected item
        const cartItems = document.querySelectorAll('.cart-item');

        if (cartItems.length > 0) {
            // Adjust quantity of the last item in cart
            const lastCartItem = cartItems[cartItems.length - 1];
            const quantityInput = lastCartItem.querySelector('.quantity-input, input[type="number"]');
            const increaseBtn = lastCartItem.querySelector('.quantity-increase, .btn-increase');
            const decreaseBtn = lastCartItem.querySelector('.quantity-decrease, .btn-decrease');

            if (change > 0 && increaseBtn) {
                increaseBtn.click();
                console.log('⌨️ Quantity increased');
            } else if (change < 0 && decreaseBtn) {
                decreaseBtn.click();
                console.log('⌨️ Quantity decreased');
            } else if (quantityInput) {
                const currentValue = parseInt(quantityInput.value) || 1;
                const newValue = Math.max(1, currentValue + change);
                quantityInput.value = newValue;

                // Trigger change event
                const event = new Event('change', { bubbles: true });
                quantityInput.dispatchEvent(event);
                console.log(`⌨️ Quantity set to ${newValue}`);
            }
        } else {
            console.log('⌨️ No cart items found to adjust quantity');
        }
    }

    /**
     * View POS cart details
     */
    viewPOSCart() {
        // The cart is already visible in POS modal, just scroll to it
        const cartSection = document.querySelector('.cart-section, .order-summary');
        if (cartSection) {
            cartSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            console.log('⌨️ Scrolled to cart section');
        }
    }

    /**
     * Remove last item from cart
     */
    removeLastCartItem() {
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems.length > 0) {
            const lastItem = cartItems[cartItems.length - 1];
            const removeBtn = lastItem.querySelector('.remove-item-btn, .btn-remove, [onclick*="remove"]');
            if (removeBtn) {
                removeBtn.click();
                console.log('⌨️ Last cart item removed');
            }
        } else {
            console.log('⌨️ No cart items to remove');
        }
    }

    /**
     * Apply discount in POS
     */
    applyPOSDiscount() {
        const discountBtn = document.querySelector('.discount-btn, .apply-discount-btn, [onclick*="discount"]');
        if (discountBtn) {
            discountBtn.click();
            console.log('⌨️ Discount dialog opened');
        } else {
            console.log('⌨️ Discount button not found');
        }
    }

    /**
     * Select table in POS
     */
    selectPOSTable() {
        const tableBtn = document.querySelector('.select-table-btn, .table-selection-btn, [onclick*="table"]');
        if (tableBtn) {
            tableBtn.click();
            console.log('⌨️ Table selection opened');
        } else {
            console.log('⌨️ Table selection button not found');
        }
    }

    /**
     * Open calculator modal
     */
    openCalculatorModal() {
        const calculatorHTML = `
            <div class="modal-overlay" id="calculatorModal">
                <div class="modal-content calculator-modal">
                    <div class="modal-header">
                        <h3>Calculator</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="calculator">
                            <div class="calculator-display">
                                <input type="text" id="calculatorDisplay" readonly value="0">
                            </div>
                            <div class="calculator-buttons">
                                <button onclick="window.keyboardShortcuts.calculatorClear()">C</button>
                                <button onclick="window.keyboardShortcuts.calculatorClearEntry()">CE</button>
                                <button onclick="window.keyboardShortcuts.calculatorBackspace()">⌫</button>
                                <button onclick="window.keyboardShortcuts.calculatorOperation('/')">/</button>

                                <button onclick="window.keyboardShortcuts.calculatorNumber('7')">7</button>
                                <button onclick="window.keyboardShortcuts.calculatorNumber('8')">8</button>
                                <button onclick="window.keyboardShortcuts.calculatorNumber('9')">9</button>
                                <button onclick="window.keyboardShortcuts.calculatorOperation('*')">×</button>

                                <button onclick="window.keyboardShortcuts.calculatorNumber('4')">4</button>
                                <button onclick="window.keyboardShortcuts.calculatorNumber('5')">5</button>
                                <button onclick="window.keyboardShortcuts.calculatorNumber('6')">6</button>
                                <button onclick="window.keyboardShortcuts.calculatorOperation('-')">-</button>

                                <button onclick="window.keyboardShortcuts.calculatorNumber('1')">1</button>
                                <button onclick="window.keyboardShortcuts.calculatorNumber('2')">2</button>
                                <button onclick="window.keyboardShortcuts.calculatorNumber('3')">3</button>
                                <button onclick="window.keyboardShortcuts.calculatorOperation('+')">+</button>

                                <button onclick="window.keyboardShortcuts.calculatorNumber('0')" class="calculator-zero">0</button>
                                <button onclick="window.keyboardShortcuts.calculatorDecimal()">.</button>
                                <button onclick="window.keyboardShortcuts.calculatorEquals()" class="calculator-equals">=</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', calculatorHTML);
        this.initCalculator();
    }

    /**
     * Initialize calculator functionality
     */
    initCalculator() {
        this.calculatorState = {
            display: '0',
            previousValue: null,
            operation: null,
            waitingForNewValue: false
        };
        this.updateCalculatorDisplay();
    }

    /**
     * Calculator number input
     */
    calculatorNumber(num) {
        if (this.calculatorState.waitingForNewValue) {
            this.calculatorState.display = num;
            this.calculatorState.waitingForNewValue = false;
        } else {
            this.calculatorState.display = this.calculatorState.display === '0' ? num : this.calculatorState.display + num;
        }
        this.updateCalculatorDisplay();
    }

    /**
     * Calculator operation
     */
    calculatorOperation(op) {
        const inputValue = parseFloat(this.calculatorState.display);

        if (this.calculatorState.previousValue === null) {
            this.calculatorState.previousValue = inputValue;
        } else if (this.calculatorState.operation) {
            const currentValue = this.calculatorState.previousValue || 0;
            const newValue = this.calculate(currentValue, inputValue, this.calculatorState.operation);

            this.calculatorState.display = String(newValue);
            this.calculatorState.previousValue = newValue;
        }

        this.calculatorState.waitingForNewValue = true;
        this.calculatorState.operation = op;
        this.updateCalculatorDisplay();
    }

    /**
     * Calculator equals
     */
    calculatorEquals() {
        const inputValue = parseFloat(this.calculatorState.display);

        if (this.calculatorState.previousValue !== null && this.calculatorState.operation) {
            const newValue = this.calculate(this.calculatorState.previousValue, inputValue, this.calculatorState.operation);
            this.calculatorState.display = String(newValue);
            this.calculatorState.previousValue = null;
            this.calculatorState.operation = null;
            this.calculatorState.waitingForNewValue = true;
            this.updateCalculatorDisplay();
        }
    }

    /**
     * Perform calculation
     */
    calculate(firstValue, secondValue, operation) {
        switch (operation) {
            case '+': return firstValue + secondValue;
            case '-': return firstValue - secondValue;
            case '*': return firstValue * secondValue;
            case '/': return secondValue !== 0 ? firstValue / secondValue : 0;
            default: return secondValue;
        }
    }

    /**
     * Calculator clear
     */
    calculatorClear() {
        this.calculatorState = {
            display: '0',
            previousValue: null,
            operation: null,
            waitingForNewValue: false
        };
        this.updateCalculatorDisplay();
    }

    /**
     * Calculator clear entry
     */
    calculatorClearEntry() {
        this.calculatorState.display = '0';
        this.updateCalculatorDisplay();
    }

    /**
     * Calculator backspace
     */
    calculatorBackspace() {
        if (this.calculatorState.display.length > 1) {
            this.calculatorState.display = this.calculatorState.display.slice(0, -1);
        } else {
            this.calculatorState.display = '0';
        }
        this.updateCalculatorDisplay();
    }

    /**
     * Calculator decimal
     */
    calculatorDecimal() {
        if (this.calculatorState.waitingForNewValue) {
            this.calculatorState.display = '0.';
            this.calculatorState.waitingForNewValue = false;
        } else if (this.calculatorState.display.indexOf('.') === -1) {
            this.calculatorState.display += '.';
        }
        this.updateCalculatorDisplay();
    }

    /**
     * Update calculator display
     */
    updateCalculatorDisplay() {
        const display = document.getElementById('calculatorDisplay');
        if (display) {
            display.value = this.calculatorState.display;
        }
    }

    /**
     * Show shortcuts help
     */
    showShortcutsHelp() {
        const helpHTML = `
            <div class="modal-overlay shortcuts-help-modal" id="shortcutsHelpModal">
                <div class="modal-content shortcuts-help-content">
                    <div class="modal-header">
                        <h2>⌨️ Keyboard Shortcuts</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="shortcuts-tabs">
                            <button class="shortcuts-tab active" onclick="window.keyboardShortcuts.showShortcutsTab('all')">All Shortcuts</button>
                            <button class="shortcuts-tab" onclick="window.keyboardShortcuts.showShortcutsTab('customize')">Customize</button>
                        </div>
                        <div id="shortcutsTabContent">
                            ${this.generateShortcutsHelpContent()}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="window.keyboardShortcuts.openShortcutsSettings()">
                            Customize Shortcuts
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', helpHTML);
    }

    /**
     * Generate shortcuts help content
     */
    generateShortcutsHelpContent() {
        const allShortcuts = { ...this.shortcuts, ...this.customShortcuts };
        const categories = {};

        // Group shortcuts by category
        Object.entries(allShortcuts).forEach(([key, shortcut]) => {
            const category = shortcut.category || 'Other';
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push({ key, ...shortcut });
        });

        let html = '<div class="shortcuts-help-list">';

        Object.entries(categories).forEach(([category, shortcuts]) => {
            html += `
                <div class="shortcuts-category">
                    <h3>${category}</h3>
                    <div class="shortcuts-grid">
            `;

            shortcuts.forEach(shortcut => {
                const keyDisplay = this.formatKeyForDisplay(shortcut.key);
                html += `
                    <div class="shortcut-item">
                        <div class="shortcut-key">${keyDisplay}</div>
                        <div class="shortcut-description">${shortcut.description}</div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * Format key for display
     */
    formatKeyForDisplay(key) {
        return key
            .replace('ctrl+', 'Ctrl + ')
            .replace('alt+', 'Alt + ')
            .replace('shift+', 'Shift + ')
            .replace('meta+', 'Cmd + ')
            .replace('Key', '')
            .replace('Digit', '')
            .replace('Arrow', '')
            .toUpperCase();
    }

    /**
     * Show shortcuts tab
     */
    showShortcutsTab(tab) {
        const tabs = document.querySelectorAll('.shortcuts-tab');
        tabs.forEach(t => t.classList.remove('active'));

        const activeTab = document.querySelector(`[onclick="window.keyboardShortcuts.showShortcutsTab('${tab}')"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }

        const content = document.getElementById('shortcutsTabContent');
        if (content) {
            if (tab === 'all') {
                content.innerHTML = this.generateShortcutsHelpContent();
            } else if (tab === 'customize') {
                content.innerHTML = this.generateCustomizeContent();
            }
        }
    }

    /**
     * Generate customize content
     */
    generateCustomizeContent() {
        return `
            <div class="shortcuts-customize">
                <div class="customize-header">
                    <h4>Customize Keyboard Shortcuts</h4>
                    <p>Click on any shortcut to change it, or add new custom shortcuts.</p>
                </div>

                <div class="customize-actions">
                    <button class="btn btn-success" onclick="window.keyboardShortcuts.addCustomShortcut()">
                        <i class="fas fa-plus"></i> Add Custom Shortcut
                    </button>
                    <button class="btn btn-warning" onclick="window.keyboardShortcuts.resetToDefaults()">
                        <i class="fas fa-undo"></i> Reset to Defaults
                    </button>
                </div>

                <div id="customizeShortcutsList">
                    ${this.generateCustomizeList()}
                </div>
            </div>
        `;
    }

    /**
     * Generate customize list
     */
    generateCustomizeList() {
        const allShortcuts = { ...this.shortcuts, ...this.customShortcuts };
        let html = '<div class="customize-shortcuts-list">';

        Object.entries(allShortcuts).forEach(([key, shortcut]) => {
            const isCustom = this.customShortcuts[key] !== undefined;
            const keyDisplay = this.formatKeyForDisplay(key);

            html += `
                <div class="customize-shortcut-item ${isCustom ? 'custom' : 'default'}">
                    <div class="shortcut-info">
                        <div class="shortcut-key-display">${keyDisplay}</div>
                        <div class="shortcut-desc">${shortcut.description}</div>
                        <div class="shortcut-category">${shortcut.category || 'Other'}</div>
                    </div>
                    <div class="shortcut-actions">
                        <button class="btn btn-sm btn-primary" onclick="window.keyboardShortcuts.editShortcut('${key}')">
                            Edit
                        </button>
                        ${isCustom ? `
                            <button class="btn btn-sm btn-danger" onclick="window.keyboardShortcuts.deleteCustomShortcut('${key}')">
                                Delete
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * Open shortcuts settings
     */
    openShortcutsSettings() {
        this.showShortcutsTab('customize');
    }

    /**
     * Add custom shortcut
     */
    addCustomShortcut() {
        const customShortcutHTML = `
            <div class="modal-overlay" id="addCustomShortcutModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Add Custom Shortcut</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Shortcut Key</label>
                            <input type="text" id="customShortcutKey" placeholder="Press keys..." readonly>
                            <button class="btn btn-secondary" onclick="window.keyboardShortcuts.recordShortcut('customShortcutKey')">
                                Record Key
                            </button>
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <input type="text" id="customShortcutDescription" placeholder="What does this shortcut do?">
                        </div>
                        <div class="form-group">
                            <label>Action</label>
                            <select id="customShortcutAction">
                                <option value="navigateTo">Navigate to Page</option>
                                <option value="openModal">Open Modal</option>
                                <option value="custom">Custom JavaScript</option>
                            </select>
                        </div>
                        <div class="form-group" id="customShortcutParams">
                            <label>Parameters</label>
                            <input type="text" id="customShortcutParamsInput" placeholder="e.g., dashboard, pos, etc.">
                        </div>
                        <div class="form-group">
                            <label>Category</label>
                            <input type="text" id="customShortcutCategory" placeholder="Custom" value="Custom">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="window.keyboardShortcuts.saveCustomShortcut()">
                            Save Shortcut
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', customShortcutHTML);
    }

    /**
     * Record shortcut key
     */
    recordShortcut(inputId) {
        const input = document.getElementById(inputId);
        if (!input) return;

        input.value = 'Press any key combination...';
        input.style.background = '#ffffcc';

        this.isRecording = true;
        this.recordingCallback = (key) => {
            input.value = this.formatKeyForDisplay(key);
            input.dataset.shortcutKey = key;
            input.style.background = '';
            this.isRecording = false;
            this.recordingCallback = null;
        };
    }

    /**
     * Record shortcut from event
     */
    recordShortcut(event) {
        if (!this.isRecording || !this.recordingCallback) return;

        event.preventDefault();
        event.stopPropagation();

        const key = this.getShortcutKey(event);
        this.recordingCallback(key);
    }

    /**
     * Save custom shortcut
     */
    saveCustomShortcut() {
        const keyInput = document.getElementById('customShortcutKey');
        const descInput = document.getElementById('customShortcutDescription');
        const actionSelect = document.getElementById('customShortcutAction');
        const paramsInput = document.getElementById('customShortcutParamsInput');
        const categoryInput = document.getElementById('customShortcutCategory');

        if (!keyInput.dataset.shortcutKey || !descInput.value) {
            alert('Please provide both a key combination and description.');
            return;
        }

        const shortcutKey = keyInput.dataset.shortcutKey;
        const params = paramsInput.value ? paramsInput.value.split(',').map(p => p.trim()) : [];

        // Check if shortcut already exists
        if (this.shortcuts[shortcutKey] || this.customShortcuts[shortcutKey]) {
            if (!confirm('This shortcut already exists. Do you want to override it?')) {
                return;
            }
        }

        this.customShortcuts[shortcutKey] = {
            action: actionSelect.value,
            params: params,
            description: descInput.value,
            category: categoryInput.value || 'Custom'
        };

        this.saveCustomShortcuts();

        // Close modal and refresh customize list
        document.getElementById('addCustomShortcutModal').remove();
        this.showShortcutsTab('customize');

        console.log('✅ Custom shortcut saved:', shortcutKey);
    }

    /**
     * Edit existing shortcut
     */
    editShortcut(key) {
        const shortcut = this.getShortcut(key);
        if (!shortcut) return;

        const editHTML = `
            <div class="modal-overlay" id="editShortcutModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Edit Shortcut</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Current Key</label>
                            <input type="text" value="${this.formatKeyForDisplay(key)}" readonly>
                        </div>
                        <div class="form-group">
                            <label>New Shortcut Key</label>
                            <input type="text" id="editShortcutKey" placeholder="Press keys..." readonly>
                            <button class="btn btn-secondary" onclick="window.keyboardShortcuts.recordShortcut('editShortcutKey')">
                                Record New Key
                            </button>
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <input type="text" id="editShortcutDescription" value="${shortcut.description}">
                        </div>
                        <div class="form-group">
                            <label>Action</label>
                            <select id="editShortcutAction">
                                <option value="navigateTo" ${shortcut.action === 'navigateTo' ? 'selected' : ''}>Navigate to Page</option>
                                <option value="openModal" ${shortcut.action === 'openModal' ? 'selected' : ''}>Open Modal</option>
                                <option value="custom" ${shortcut.action === 'custom' ? 'selected' : ''}>Custom JavaScript</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Parameters</label>
                            <input type="text" id="editShortcutParams" value="${shortcut.params ? shortcut.params.join(', ') : ''}">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="window.keyboardShortcuts.saveEditedShortcut('${key}')">
                            Save Changes
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', editHTML);
    }

    /**
     * Save edited shortcut
     */
    saveEditedShortcut(originalKey) {
        const keyInput = document.getElementById('editShortcutKey');
        const descInput = document.getElementById('editShortcutDescription');
        const actionSelect = document.getElementById('editShortcutAction');
        const paramsInput = document.getElementById('editShortcutParams');

        const newKey = keyInput.dataset.shortcutKey || originalKey;
        const params = paramsInput.value ? paramsInput.value.split(',').map(p => p.trim()) : [];

        // If key changed, remove old key
        if (newKey !== originalKey) {
            delete this.customShortcuts[originalKey];
        }

        this.customShortcuts[newKey] = {
            action: actionSelect.value,
            params: params,
            description: descInput.value,
            category: this.getShortcut(originalKey).category || 'Custom'
        };

        this.saveCustomShortcuts();

        // Close modal and refresh
        document.getElementById('editShortcutModal').remove();
        this.showShortcutsTab('customize');

        console.log('✅ Shortcut updated:', newKey);
    }

    /**
     * Delete custom shortcut
     */
    deleteCustomShortcut(key) {
        if (confirm('Are you sure you want to delete this custom shortcut?')) {
            delete this.customShortcuts[key];
            this.saveCustomShortcuts();
            this.showShortcutsTab('customize');
            console.log('🗑️ Custom shortcut deleted:', key);
        }
    }

    /**
     * Reset to default shortcuts
     */
    resetToDefaults() {
        if (confirm('This will remove all custom shortcuts and reset to defaults. Continue?')) {
            this.customShortcuts = {};
            this.saveCustomShortcuts();
            this.showShortcutsTab('customize');
            console.log('🔄 Shortcuts reset to defaults');
        }
    }

    /**
     * Toggle theme
     */
    toggleTheme() {
        const body = document.body;
        const isDark = body.classList.contains('dark-theme');

        if (isDark) {
            body.classList.remove('dark-theme');
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-theme');
            localStorage.setItem('theme', 'dark');
        }

        console.log('🎨 Theme toggled to:', isDark ? 'light' : 'dark');
    }

    /**
     * Save current data
     */
    saveCurrentData() {
        if (window.app && typeof window.app.saveAllData === 'function') {
            window.app.saveAllData();
            console.log('💾 Data saved via keyboard shortcut');
        }
    }

    /**
     * Print current view
     */
    printCurrentView() {
        window.print();
    }

    /**
     * Get all shortcuts (default + custom)
     */
    getAllShortcuts() {
        return { ...this.shortcuts, ...this.customShortcuts };
    }

    /**
     * Check if shortcut exists
     */
    shortcutExists(key) {
        return !!(this.shortcuts[key] || this.customShortcuts[key]);
    }

    /**
     * Get shortcut info
     */
    getShortcutInfo(key) {
        return this.getShortcut(key);
    }

    /**
     * Enable/disable shortcuts system
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        console.log('⌨️ Keyboard shortcuts', enabled ? 'enabled' : 'disabled');
    }

    /**
     * Get system status
     */
    getStatus() {
        return {
            initialized: this.initialized,
            enabled: this.enabled !== false,
            totalShortcuts: Object.keys(this.getAllShortcuts()).length,
            customShortcuts: Object.keys(this.customShortcuts).length,
            version: this.version
        };
    }

    /**
     * Test POS shortcuts functionality
     */
    testPOSShortcuts() {
        console.log('🧪 TESTING POS SHORTCUTS');
        console.log('=' .repeat(40));

        if (!this.isInContext('pos')) {
            console.log('❌ POS modal not open. Please open POS first with "P" key.');
            return false;
        }

        const tests = [
            {
                name: 'Clear Cart Button',
                selector: '.clear-order-btn',
                shortcut: 'X'
            },
            {
                name: 'Process Payment Button',
                selector: '.process-payment-btn',
                shortcut: 'F'
            },
            {
                name: 'Cash Payment Button',
                selector: '.payment-btn[onclick*="cash"]',
                shortcut: 'Q'
            },
            {
                name: 'Category Tabs',
                selector: '.tab-btn[data-category]',
                shortcut: '1-5'
            },
            {
                name: 'Menu Items',
                selector: '.menu-item-card, .menu-item',
                shortcut: 'Arrow Keys'
            }
        ];

        let passedTests = 0;
        tests.forEach(test => {
            const element = document.querySelector(test.selector);
            const found = !!element;

            console.log(`${found ? '✅' : '❌'} ${test.name} (${test.shortcut}): ${found ? 'Found' : 'Not Found'}`);

            if (found) {
                passedTests++;
            }
        });

        console.log(`\n📊 Test Results: ${passedTests}/${tests.length} elements found`);

        if (passedTests === tests.length) {
            console.log('🎉 All POS shortcut elements are available!');
            console.log('\n💡 Try these shortcuts:');
            console.log('• Press F to finalize order');
            console.log('• Press X to clear cart');
            console.log('• Press Q for quick cash payment');
            console.log('• Press 1-5 to select categories');
            console.log('• Use arrow keys to navigate items');
        } else {
            console.log('⚠️ Some POS elements are missing. Shortcuts may not work properly.');
        }

        return passedTests === tests.length;
    }

    /**
     * Debug function to check system state
     */
    debug() {
        console.log('🔍 KEYBOARD SHORTCUTS DEBUG INFO');
        console.log('=' .repeat(50));
        console.log('System exists:', typeof window.keyboardShortcuts !== 'undefined');
        console.log('Initialized:', this.initialized);
        console.log('Version:', this.version);
        console.log('Default shortcuts count:', Object.keys(this.shortcuts).length);
        console.log('Custom shortcuts count:', Object.keys(this.customShortcuts).length);
        console.log('Total shortcuts:', Object.keys(this.getAllShortcuts()).length);
        console.log('Event listeners active:', !!this.setupEventListeners);
        console.log('DOM ready state:', document.readyState);
        console.log('Window loaded:', document.readyState === 'complete');

        // Test a few key shortcuts
        const testKeys = ['KeyP', 'KeyC', 'Escape', 'F1'];
        console.log('Test shortcuts:');
        testKeys.forEach(key => {
            const shortcut = this.getShortcut(key);
            console.log(`  ${key}:`, shortcut ? shortcut.description : 'NOT FOUND');
        });

        return this.getStatus();
    }

    /**
     * Show visual feedback when shortcut is activated
     */
    showShortcutActivation(shortcut) {
        // Create or update the shortcut notification
        let notification = document.getElementById('shortcutNotification');

        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'shortcutNotification';
            notification.className = 'shortcut-notification';
            document.body.appendChild(notification);
        }

        const keyDisplay = this.formatKeyForDisplay(Object.keys(this.getAllShortcuts()).find(key =>
            this.getAllShortcuts()[key] === shortcut
        ) || '');

        notification.innerHTML = `
            <div class="shortcut-key">${keyDisplay}</div>
            <div class="shortcut-desc">${shortcut.description}</div>
        `;

        notification.classList.add('show');

        // Hide after 2 seconds
        setTimeout(() => {
            notification.classList.remove('show');
        }, 2000);
    }

    /**
     * Create floating shortcuts reference
     */
    createFloatingReference() {
        const referenceHTML = `
            <div class="floating-shortcuts-reference" id="floatingShortcutsRef">
                <div class="reference-header">
                    <span>⌨️ Quick Shortcuts</span>
                    <button class="reference-toggle" onclick="window.keyboardShortcuts.toggleFloatingReference()">−</button>
                </div>
                <div class="reference-content">
                    <div class="reference-item">
                        <span class="ref-key">P</span>
                        <span class="ref-desc">POS</span>
                    </div>
                    <div class="reference-item">
                        <span class="ref-key">C</span>
                        <span class="ref-desc">Calculator</span>
                    </div>
                    <div class="reference-item">
                        <span class="ref-key">Esc</span>
                        <span class="ref-desc">Close Modal</span>
                    </div>
                    <div class="reference-item">
                        <span class="ref-key">F1</span>
                        <span class="ref-desc">Help</span>
                    </div>
                    <div class="reference-item">
                        <span class="ref-key">D</span>
                        <span class="ref-desc">Dashboard</span>
                    </div>
                    <div class="reference-item">
                        <span class="ref-key">R</span>
                        <span class="ref-desc">Reports</span>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', referenceHTML);
    }

    /**
     * Toggle floating reference visibility
     */
    toggleFloatingReference() {
        const reference = document.getElementById('floatingShortcutsRef');
        const content = reference.querySelector('.reference-content');
        const toggle = reference.querySelector('.reference-toggle');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            toggle.textContent = '−';
            reference.classList.remove('collapsed');
        } else {
            content.style.display = 'none';
            toggle.textContent = '+';
            reference.classList.add('collapsed');
        }
    }

    /**
     * Show/hide floating reference
     */
    setFloatingReferenceVisible(visible) {
        const reference = document.getElementById('floatingShortcutsRef');
        if (reference) {
            reference.style.display = visible ? 'block' : 'none';
        }
    }
}

// Global debug function
window.debugKeyboardShortcuts = function() {
    if (window.keyboardShortcuts) {
        return window.keyboardShortcuts.debug();
    } else {
        console.log('❌ Keyboard shortcuts system not available');
        console.log('DOM ready state:', document.readyState);
        console.log('Scripts loaded:', {
            keyboardShortcuts: typeof ZaiqaKeyboardShortcuts !== 'undefined',
            app: typeof window.app !== 'undefined'
        });
        return null;
    }
};

// Initialize the keyboard shortcuts system with proper timing
let initializationAttempts = 0;
const maxInitializationAttempts = 5;

function initializeKeyboardShortcuts() {
    initializationAttempts++;

    try {
        console.log(`🔄 Initializing Keyboard Shortcuts System... (attempt ${initializationAttempts}/${maxInitializationAttempts})`);

        // Check if class is available
        if (typeof ZaiqaKeyboardShortcuts === 'undefined') {
            throw new Error('ZaiqaKeyboardShortcuts class not available');
        }

        window.keyboardShortcuts = new ZaiqaKeyboardShortcuts();
        console.log('✅ Keyboard Shortcuts System ready');

        // Dispatch custom event to notify other systems
        window.dispatchEvent(new CustomEvent('keyboardShortcutsReady', {
            detail: { system: window.keyboardShortcuts }
        }));

        // Reset attempt counter on success
        initializationAttempts = 0;

    } catch (error) {
        console.error(`❌ Failed to initialize Keyboard Shortcuts System (attempt ${initializationAttempts}):`, error);

        // Retry after a delay if we haven't exceeded max attempts
        if (initializationAttempts < maxInitializationAttempts) {
            const delay = initializationAttempts * 1000; // Increasing delay
            setTimeout(() => {
                console.log(`🔄 Retrying Keyboard Shortcuts initialization in ${delay}ms...`);
                initializeKeyboardShortcuts();
            }, delay);
        } else {
            console.error('❌ Maximum initialization attempts reached. Keyboard shortcuts system failed to initialize.');

            // Create a minimal fallback system
            window.keyboardShortcuts = {
                initialized: false,
                getStatus: () => ({ initialized: false, error: 'Failed to initialize' }),
                debug: () => console.log('❌ Keyboard shortcuts system failed to initialize')
            };
        }
    }
}

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeKeyboardShortcuts);
} else {
    // DOM is already ready
    setTimeout(initializeKeyboardShortcuts, 100);
}

// Also initialize when window loads (fallback)
window.addEventListener('load', () => {
    if (!window.keyboardShortcuts) {
        console.log('🔄 Fallback initialization of Keyboard Shortcuts...');
        initializeKeyboardShortcuts();
    }
});

// Manual initialization function for troubleshooting
window.initKeyboardShortcuts = function() {
    console.log('🔧 Manual keyboard shortcuts initialization requested...');
    initializeKeyboardShortcuts();
};

// Force initialization function (bypasses checks)
window.forceInitKeyboardShortcuts = function() {
    console.log('🔧 Force initializing keyboard shortcuts...');
    try {
        window.keyboardShortcuts = new ZaiqaKeyboardShortcuts();
        console.log('✅ Force initialization successful');
        return true;
    } catch (error) {
        console.error('❌ Force initialization failed:', error);
        return false;
    }
};

// Test POS shortcuts function
window.testPOSShortcuts = function() {
    if (window.keyboardShortcuts && typeof window.keyboardShortcuts.testPOSShortcuts === 'function') {
        return window.keyboardShortcuts.testPOSShortcuts();
    } else {
        console.log('❌ Keyboard shortcuts system not available');
        return false;
    }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ZaiqaKeyboardShortcuts;
}

