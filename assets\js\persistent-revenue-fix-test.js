/**
 * Persistent Revenue Fix Test
 * Tests the fix for manually added revenue that persists after data clearing
 */

class PersistentRevenueFixTest {
    constructor() {
        this.testResults = {};
        this.version = '1.0.0';
    }

    /**
     * Run comprehensive test for persistent revenue fix
     */
    async runTest() {
        console.log('🧪 Starting Persistent Revenue Fix Test v' + this.version);
        console.log('=' .repeat(60));

        try {
            // Test 1: Check if diagnostic function exists
            await this.testDiagnosticFunction();

            // Test 2: Check if clear persistent revenue function exists
            await this.testClearPersistentRevenueFunction();

            // Test 3: Test enhanced clearAllData function
            await this.testEnhancedClearAllData();

            // Test 4: Simulate persistent revenue scenario
            await this.testPersistentRevenueScenario();

            // Generate test report
            this.generateTestReport();

        } catch (error) {
            console.error('❌ Test suite failed:', error);
        }
    }

    /**
     * Test 1: Check if diagnostic function exists
     */
    async testDiagnosticFunction() {
        console.log('\n1️⃣ Testing Diagnostic Function...');
        
        try {
            const diagnosticExists = window.app && typeof window.app.diagnosePersistentRevenueData === 'function';
            console.log('🔍 Diagnostic function exists:', diagnosticExists);

            if (diagnosticExists) {
                // Test the diagnostic function
                console.log('🔍 Running diagnostic function...');
                const result = window.app.diagnosePersistentRevenueData();
                console.log('🔍 Diagnostic completed, found keys:', result ? result.length : 0);
            }

            this.testResults.diagnosticFunction = {
                passed: diagnosticExists,
                exists: diagnosticExists
            };

            console.log('✅ Diagnostic function test completed');

        } catch (error) {
            console.error('❌ Diagnostic function test failed:', error);
            this.testResults.diagnosticFunction = { passed: false, error: error.message };
        }
    }

    /**
     * Test 2: Check if clear persistent revenue function exists
     */
    async testClearPersistentRevenueFunction() {
        console.log('\n2️⃣ Testing Clear Persistent Revenue Function...');
        
        try {
            const clearFunctionExists = window.app && typeof window.app.clearPersistentRevenueData === 'function';
            console.log('🗑️ Clear persistent revenue function exists:', clearFunctionExists);

            this.testResults.clearPersistentRevenueFunction = {
                passed: clearFunctionExists,
                exists: clearFunctionExists
            };

            console.log('✅ Clear persistent revenue function test completed');

        } catch (error) {
            console.error('❌ Clear persistent revenue function test failed:', error);
            this.testResults.clearPersistentRevenueFunction = { passed: false, error: error.message };
        }
    }

    /**
     * Test 3: Test enhanced clearAllData function
     */
    async testEnhancedClearAllData() {
        console.log('\n3️⃣ Testing Enhanced Clear All Data Function...');
        
        try {
            const clearAllDataExists = window.app && typeof window.app.clearAllData === 'function';
            console.log('🗑️ Clear all data function exists:', clearAllDataExists);

            // Check if the function has been enhanced by looking at its string representation
            if (clearAllDataExists) {
                const functionString = window.app.clearAllData.toString();
                const hasRevenueKeywords = functionString.includes('cashTransactions') && 
                                         functionString.includes('currentCashInHand') && 
                                         functionString.includes('businessDays');
                
                console.log('🔍 Function includes revenue-specific keys:', hasRevenueKeywords);
                
                this.testResults.enhancedClearAllData = {
                    passed: clearAllDataExists && hasRevenueKeywords,
                    exists: clearAllDataExists,
                    enhanced: hasRevenueKeywords
                };
            } else {
                this.testResults.enhancedClearAllData = {
                    passed: false,
                    exists: false,
                    enhanced: false
                };
            }

            console.log('✅ Enhanced clear all data function test completed');

        } catch (error) {
            console.error('❌ Enhanced clear all data function test failed:', error);
            this.testResults.enhancedClearAllData = { passed: false, error: error.message };
        }
    }

    /**
     * Test 4: Simulate persistent revenue scenario
     */
    async testPersistentRevenueScenario() {
        console.log('\n4️⃣ Testing Persistent Revenue Scenario...');
        
        try {
            // Create some test revenue data
            console.log('📝 Creating test revenue data...');
            
            const testData = {
                'testRevenue': JSON.stringify([{ amount: 1000, description: 'Test Revenue' }]),
                'testCashTransactions': JSON.stringify([{ amount: 500, type: 'inflow' }]),
                'testCurrentCashInHand': '2000',
                'testManualRevenue': JSON.stringify({ total: 1500 })
            };

            // Store test data
            Object.keys(testData).forEach(key => {
                localStorage.setItem(key, testData[key]);
            });

            console.log('📝 Test data created:', Object.keys(testData));

            // Run diagnostic to see if it detects the test data
            if (window.app && typeof window.app.diagnosePersistentRevenueData === 'function') {
                console.log('🔍 Running diagnostic on test data...');
                const diagnosticResult = window.app.diagnosePersistentRevenueData();
                const detectedTestKeys = diagnosticResult.filter(item => item.key.startsWith('test'));
                console.log('🔍 Detected test keys:', detectedTestKeys.length);
            }

            // Test clearing the persistent data
            if (window.app && typeof window.app.clearPersistentRevenueData === 'function') {
                console.log('🗑️ Testing clear persistent revenue data...');
                window.app.clearPersistentRevenueData();
                
                // Check if test data was cleared
                const remainingTestKeys = Object.keys(testData).filter(key => localStorage.getItem(key) !== null);
                console.log('🔍 Remaining test keys after clearing:', remainingTestKeys.length);
                
                this.testResults.persistentRevenueScenario = {
                    passed: remainingTestKeys.length === 0,
                    testDataCreated: Object.keys(testData).length,
                    testDataRemaining: remainingTestKeys.length,
                    remainingKeys: remainingTestKeys
                };
            } else {
                this.testResults.persistentRevenueScenario = {
                    passed: false,
                    error: 'Clear persistent revenue function not available'
                };
            }

            console.log('✅ Persistent revenue scenario test completed');

        } catch (error) {
            console.error('❌ Persistent revenue scenario test failed:', error);
            this.testResults.persistentRevenueScenario = { passed: false, error: error.message };
        }
    }

    /**
     * Generate comprehensive test report
     */
    generateTestReport() {
        console.log('\n📋 PERSISTENT REVENUE FIX TEST REPORT');
        console.log('=' .repeat(60));

        const tests = [
            { name: 'Diagnostic Function', key: 'diagnosticFunction' },
            { name: 'Clear Persistent Revenue Function', key: 'clearPersistentRevenueFunction' },
            { name: 'Enhanced Clear All Data Function', key: 'enhancedClearAllData' },
            { name: 'Persistent Revenue Scenario', key: 'persistentRevenueScenario' }
        ];

        let passedTests = 0;
        let totalTests = tests.length;

        tests.forEach((test, index) => {
            const result = this.testResults[test.key];
            const status = result && result.passed ? '✅ PASSED' : '❌ FAILED';
            
            if (result && result.passed) {
                passedTests++;
            }

            console.log(`${index + 1}. ${test.name}: ${status}`);
            
            if (result && result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });

        console.log('\n📊 SUMMARY:');
        console.log(`Tests Passed: ${passedTests}/${totalTests}`);
        console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

        if (passedTests === totalTests) {
            console.log('🎉 All persistent revenue fixes are working correctly!');
            console.log('\n💡 USAGE INSTRUCTIONS:');
            console.log('1. Go to Settings page');
            console.log('2. Click "Diagnose Revenue Data" to see what data exists');
            console.log('3. Click "Clear Persistent Revenue" to remove persistent revenue');
            console.log('4. Use "Clear All Data" for complete system reset');
        } else {
            console.log('⚠️ Some fixes need attention. Check the detailed results above.');
        }

        console.log('\n📋 Detailed Results:');
        console.log(JSON.stringify(this.testResults, null, 2));

        return {
            passed: passedTests,
            total: totalTests,
            successRate: Math.round((passedTests / totalTests) * 100),
            results: this.testResults
        };
    }
}

// Export for global use
window.PersistentRevenueFixTest = PersistentRevenueFixTest;

// Manual test function
window.testPersistentRevenueFix = function() {
    const testSuite = new PersistentRevenueFixTest();
    return testSuite.runTest();
};

// Auto-run test if in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // Run tests after page load
    window.addEventListener('load', () => {
        setTimeout(() => {
            console.log('🧪 Auto-running Persistent Revenue Fix Test...');
            const testSuite = new PersistentRevenueFixTest();
            testSuite.runTest();
        }, 4000);
    });
}
