/**
 * Restaurant Analytics Engine
 * Comprehensive analytics system for restaurant management
 */

class RestaurantAnalytics {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        this.eventListeners = new Map();
        this.isInitialized = false;
        this.dbManager = window.dbManager || null;

        console.log('📊 Restaurant Analytics initialized with database connection:', !!this.dbManager);

        // Test database connection if available
        if (this.dbManager) {
            this.testDatabaseConnection();
        }

        this.init();
    }

    /**
     * Test database connection
     */
    async testDatabaseConnection() {
        try {
            console.log('🧪 Testing database connection...');
            const testOrders = await this.dbManager.getOrders({ limit: 1 });
            console.log('✅ Database test successful. Sample orders:', testOrders);

            if (!Array.isArray(testOrders)) {
                console.error('❌ Database returned non-array:', typeof testOrders, testOrders);
            }
        } catch (error) {
            console.error('❌ Database test failed:', error);
        }
    }

    /**
     * Initialize the analytics engine
     */
    init() {
        try {
            console.log('🚀 Initializing Restaurant Analytics Engine...');
            
            // Set up data structure if not exists
            this.initializeDataStructure();
            
            // Set up event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ Restaurant Analytics Engine initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize analytics engine:', error);
        }
    }

    /**
     * Initialize data structure in localStorage
     */
    initializeDataStructure() {
        const defaultStructure = {
            businessDays: [],
            orders: [],
            expenses: [],
            cashTransactions: [],
            staffShifts: [],
            inventoryUsage: [],
            settings: {
                autoRefresh: true,
                refreshInterval: 30000,
                defaultDateRange: 'today',
                currency: 'PKR'
            },
            cache: {}
        };

        // Initialize if not exists
        Object.keys(defaultStructure).forEach(key => {
            if (!localStorage.getItem(key)) {
                localStorage.setItem(key, JSON.stringify(defaultStructure[key]));
            }
        });
    }

    /**
     * Set up event listeners for real-time updates
     */
    setupEventListeners() {
        // Listen for storage changes
        window.addEventListener('storage', (e) => {
            if (['orders', 'expenses', 'cashTransactions'].includes(e.key)) {
                this.clearCache();
                this.emit('dataUpdated', { key: e.key, newValue: e.newValue });
            }
        });
    }

    /**
     * Event emitter functionality
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => callback(data));
        }
    }

    /**
     * Cache management
     */
    getCached(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        return null;
    }

    setCached(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    clearCache() {
        this.cache.clear();
    }

    /**
     * Date utility functions
     */
    getDateRange(range, customStart = null, customEnd = null) {
        const today = new Date();
        let start, end;

        switch (range) {
            case 'today':
                start = new Date(today);
                start.setHours(0, 0, 0, 0);
                end = new Date(today);
                end.setHours(23, 59, 59, 999);
                break;
            
            case 'yesterday':
                start = new Date(today);
                start.setDate(start.getDate() - 1);
                start.setHours(0, 0, 0, 0);
                end = new Date(today);
                end.setDate(end.getDate() - 1);
                end.setHours(23, 59, 59, 999);
                break;
            
            case 'week':
                start = new Date(today);
                start.setDate(start.getDate() - today.getDay());
                start.setHours(0, 0, 0, 0);
                end = new Date(today);
                end.setHours(23, 59, 59, 999);
                break;
            
            case 'month':
                start = new Date(today.getFullYear(), today.getMonth(), 1);
                end = new Date(today);
                end.setHours(23, 59, 59, 999);
                break;
            
            case 'last7days':
                start = new Date(today);
                start.setDate(start.getDate() - 6);
                start.setHours(0, 0, 0, 0);
                end = new Date(today);
                end.setHours(23, 59, 59, 999);
                break;
            
            case 'last30days':
                start = new Date(today);
                start.setDate(start.getDate() - 29);
                start.setHours(0, 0, 0, 0);
                end = new Date(today);
                end.setHours(23, 59, 59, 999);
                break;
            
            case 'custom':
                start = new Date(customStart);
                end = new Date(customEnd);
                break;
            
            default:
                start = new Date(today);
                start.setHours(0, 0, 0, 0);
                end = new Date(today);
                end.setHours(23, 59, 59, 999);
        }

        return { start, end };
    }

    /**
     * Data retrieval methods
     */
    async getOrders(dateRange = 'today', filters = {}) {
        console.log('🔧 getOrders called with:', { dateRange, filters });

        const cacheKey = `orders_${dateRange}_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached && Array.isArray(cached)) {
            console.log('📋 Returning cached orders:', cached.length);
            return cached;
        }

        let orders = [];

        try {
            if (this.dbManager) {
                // Use database manager
                console.log('🔧 Using database manager for orders...');
                const { start, end } = this.getDateRange(dateRange, filters.startDate, filters.endDate);
                const dbFilters = {
                    startDate: start.toISOString().split('T')[0],
                    endDate: end.toISOString().split('T')[0],
                    status: filters.status,
                    limit: filters.limit
                };

                try {
                    const dbResult = await this.dbManager.getOrders(dbFilters);
                    console.log('🔧 Database manager returned:', typeof dbResult, dbResult);

                    // Multiple validation layers
                    if (dbResult === null || dbResult === undefined) {
                        console.warn('⚠️ Database returned null/undefined');
                        orders = [];
                    } else if (!Array.isArray(dbResult)) {
                        console.warn('⚠️ Database returned non-array:', typeof dbResult, dbResult);
                        orders = [];
                    } else {
                        orders = dbResult;
                        console.log('📊 Successfully loaded orders from database:', orders.length);
                    }
                } catch (dbError) {
                    console.error('❌ Database error:', dbError);
                    orders = [];
                }
            }

            // If no orders from database, try localStorage
            if (!Array.isArray(orders) || orders.length === 0) {
                console.log('🔧 Falling back to localStorage for orders...');
                try {
                    const localData = localStorage.getItem('orders');
                    const parsedOrders = localData ? JSON.parse(localData) : [];

                    if (Array.isArray(parsedOrders)) {
                        orders = parsedOrders;
                        console.log('📦 Loaded orders from localStorage:', orders.length);
                    } else {
                        console.warn('⚠️ localStorage orders is not an array');
                        orders = [];
                    }
                } catch (parseError) {
                    console.error('❌ Error parsing localStorage orders:', parseError);
                    orders = [];
                }
            }
        } catch (error) {
            console.error('❌ Critical error in getOrders:', error);
            orders = [];
        }

        // CRITICAL: Final validation before processing
        if (!Array.isArray(orders)) {
            console.error('❌ CRITICAL: Orders is still not an array after all attempts!');
            console.error('❌ Orders value:', orders);
            orders = [];
        }

        console.log('🔧 About to filter', orders.length, 'orders...');

        let filteredOrders = [];
        try {
            const { start, end } = this.getDateRange(dateRange, filters.startDate, filters.endDate);

            filteredOrders = orders.filter(order => {
                // Safety check for each order
                if (!order || typeof order !== 'object') {
                    return false;
                }

                const orderDate = new Date(order.createdAt || order.date || order.order_date);
                const inDateRange = orderDate >= start && orderDate <= end;

                if (!inDateRange) return false;

                // Apply additional filters
                if (filters.status && order.status !== filters.status) return false;
                if (filters.paymentMethod && order.paymentMethod !== filters.paymentMethod) return false;
                if (filters.orderType && order.orderType !== filters.orderType) return false;
                if (filters.minAmount && order.totalAmount < filters.minAmount) return false;
                if (filters.maxAmount && order.totalAmount > filters.maxAmount) return false;

                return true;
            });

            console.log('🔧 Successfully filtered orders:', filteredOrders.length);
        } catch (filterError) {
            console.error('❌ Error during filtering:', filterError);
            filteredOrders = [];
        }

        // ULTIMATE safety check
        if (!Array.isArray(filteredOrders)) {
            console.error('❌ CRITICAL: filteredOrders is not an array after filtering!');
            console.error('❌ filteredOrders value:', filteredOrders);
            filteredOrders = [];
        }

        this.setCached(cacheKey, filteredOrders);
        console.log('✅ getOrders returning validated array with', filteredOrders.length, 'items');
        return filteredOrders;
    }

    async getExpenses(dateRange = 'today', filters = {}) {
        const cacheKey = `expenses_${dateRange}_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        let expenses = [];

        if (this.dbManager) {
            // Use database manager
            const { start, end } = this.getDateRange(dateRange, filters.startDate, filters.endDate);
            const dbFilters = {
                startDate: start.toISOString().split('T')[0],
                endDate: end.toISOString().split('T')[0],
                category: filters.category,
                limit: filters.limit
            };

            try {
                expenses = await this.dbManager.getExpenses(dbFilters);
                console.log('📊 Loaded expenses from database:', expenses.length);
            } catch (error) {
                console.error('❌ Failed to load expenses from database:', error);
                expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            }
        } else {
            // Fallback to localStorage
            expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            console.log('📦 Loaded expenses from localStorage:', expenses.length);
        }

        const { start, end } = this.getDateRange(dateRange, filters.startDate, filters.endDate);

        // Ensure expenses is an array
        if (!Array.isArray(expenses)) {
            console.warn('⚠️ Expenses is not an array:', expenses);
            expenses = [];
        }

        let filteredExpenses = expenses.filter(expense => {
            // Handle different date field names
            const expenseDate = new Date(expense.expense_date || expense.date || expense.createdAt);
            const inDateRange = expenseDate >= start && expenseDate <= end;

            if (!inDateRange) return false;

            // Apply additional filters
            if (filters.category && filters.category !== 'all' && expense.category !== filters.category) return false;
            if (filters.minAmount && expense.amount < filters.minAmount) return false;
            if (filters.maxAmount && expense.amount > filters.maxAmount) return false;

            return true;
        });

        console.log('📊 Filtered expenses:', filteredExpenses);
        this.setCached(cacheKey, filteredExpenses);
        return filteredExpenses;
    }

    /**
     * Core analytics calculations
     */
    async calculateQuickStats(dateRange = 'today', filters = {}) {
        const cacheKey = `quickStats_${dateRange}_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const orders = await this.getOrders(dateRange, filters);
        const expenses = await this.getExpenses(dateRange, filters);
        
        // Calculate totals with debugging
        console.log('💰 Calculating revenue from orders:', {
            totalOrders: orders.length,
            completedOrders: orders.filter(order => order.status === 'completed').length,
            sampleOrder: orders[0] || 'No orders'
        });

        const completedOrders = orders.filter(order => order.status === 'completed');
        const totalRevenue = completedOrders
            .reduce((sum, order) => {
                const amount = parseFloat(order.totalAmount || order.total_amount || order.total || 0);
                console.log('💰 Order revenue:', {
                    orderId: order.id,
                    totalAmount: order.totalAmount,
                    total_amount: order.total_amount,
                    total: order.total,
                    parsedAmount: amount
                });
                return sum + amount;
            }, 0);

        console.log('💰 Final revenue calculation:', {
            completedOrdersCount: completedOrders.length,
            totalRevenue: totalRevenue
        });
        
        const totalExpenses = expenses
            .reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
        
        const netProfit = totalRevenue - totalExpenses;
        const orderCount = orders.filter(order => order.status === 'completed').length;
        const avgOrderValue = orderCount > 0 ? totalRevenue / orderCount : 0;
        
        // Calculate trends (compare with previous period)
        const previousPeriod = this.getPreviousPeriod(dateRange);
        const prevOrders = await this.getOrders('custom', {
            startDate: previousPeriod.start,
            endDate: previousPeriod.end
        });
        const prevExpenses = await this.getExpenses('custom', {
            startDate: previousPeriod.start,
            endDate: previousPeriod.end
        });
        
        const prevRevenue = prevOrders
            .filter(order => order.status === 'completed')
            .reduce((sum, order) => sum + (parseFloat(order.totalAmount) || 0), 0);
        
        const prevTotalExpenses = prevExpenses
            .reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
        
        const revenueTrend = this.calculateTrend(totalRevenue, prevRevenue);
        const expenseTrend = this.calculateTrend(totalExpenses, prevTotalExpenses);
        const profitTrend = this.calculateTrend(netProfit, prevRevenue - prevTotalExpenses);
        
        const stats = {
            totalRevenue,
            totalExpenses,
            netProfit,
            orderCount,
            avgOrderValue,
            profitMargin: totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0,
            trends: {
                revenue: revenueTrend,
                expenses: expenseTrend,
                profit: profitTrend
            },
            currentCash: this.getCurrentCash(),
            lastUpdated: new Date().toISOString()
        };

        this.setCached(cacheKey, stats);
        return stats;
    }

    /**
     * Calculate trend percentage
     */
    calculateTrend(current, previous) {
        if (previous === 0) {
            return { percentage: 0, direction: 'neutral' };
        }
        
        const percentage = ((current - previous) / previous) * 100;
        const direction = percentage > 0 ? 'up' : percentage < 0 ? 'down' : 'neutral';
        
        return { percentage: Math.abs(percentage), direction };
    }

    /**
     * Get previous period for trend comparison
     */
    getPreviousPeriod(dateRange) {
        const current = this.getDateRange(dateRange);
        const duration = current.end - current.start;

        return {
            start: new Date(current.start.getTime() - duration),
            end: new Date(current.end.getTime() - duration)
        };
    }

    /**
     * Top Selling Items Analysis
     */
    async getTopSellingItems(dateRange = 'today', limit = 10, filters = {}) {
        const cacheKey = `topItems_${dateRange}_${limit}_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const orders = await this.getOrders(dateRange, filters);
        const itemSales = new Map();

        orders.filter(order => order.status === 'completed').forEach(order => {
            if (order.items && Array.isArray(order.items)) {
                order.items.forEach(item => {
                    const key = item.name || item.itemName;
                    if (!itemSales.has(key)) {
                        itemSales.set(key, {
                            name: key,
                            quantity: 0,
                            revenue: 0,
                            orders: 0,
                            avgPrice: 0
                        });
                    }

                    const current = itemSales.get(key);
                    current.quantity += item.quantity || 1;
                    current.revenue += (item.price || 0) * (item.quantity || 1);
                    current.orders += 1;
                    current.avgPrice = current.revenue / current.quantity;
                });
            }
        });

        const topItems = Array.from(itemSales.values())
            .sort((a, b) => b.quantity - a.quantity)
            .slice(0, limit);

        this.setCached(cacheKey, topItems);
        return topItems;
    }

    /**
     * Item-wise Profit Analysis
     */
    async getItemProfitAnalysis(dateRange = 'today', filters = {}) {
        const cacheKey = `profitAnalysis_${dateRange}_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const orders = await this.getOrders(dateRange, filters);
        const menuItems = JSON.parse(localStorage.getItem('menuItems') || '[]');
        const itemProfits = new Map();

        orders.filter(order => order.status === 'completed').forEach(order => {
            if (order.items && Array.isArray(order.items)) {
                order.items.forEach(item => {
                    const key = item.name || item.itemName;
                    const menuItem = menuItems.find(m => m.name === key);
                    const costPrice = menuItem ? (menuItem.cost || 0) : 0;
                    const sellingPrice = item.price || 0;
                    const quantity = item.quantity || 1;

                    if (!itemProfits.has(key)) {
                        itemProfits.set(key, {
                            name: key,
                            totalRevenue: 0,
                            totalCost: 0,
                            totalProfit: 0,
                            quantity: 0,
                            profitMargin: 0,
                            avgSellingPrice: 0,
                            avgCostPrice: costPrice
                        });
                    }

                    const current = itemProfits.get(key);
                    current.quantity += quantity;
                    current.totalRevenue += sellingPrice * quantity;
                    current.totalCost += costPrice * quantity;
                    current.totalProfit = current.totalRevenue - current.totalCost;
                    current.profitMargin = current.totalRevenue > 0 ?
                        (current.totalProfit / current.totalRevenue) * 100 : 0;
                    current.avgSellingPrice = current.totalRevenue / current.quantity;
                });
            }
        });

        const profitAnalysis = Array.from(itemProfits.values())
            .sort((a, b) => b.totalProfit - a.totalProfit);

        this.setCached(cacheKey, profitAnalysis);
        return profitAnalysis;
    }

    /**
     * Sales Trends Analysis
     */
    async getSalesTrends(dateRange = 'last7days', groupBy = 'day') {
        const cacheKey = `salesTrends_${dateRange}_${groupBy}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const orders = await this.getOrders(dateRange);
        const trends = new Map();

        orders.filter(order => order.status === 'completed').forEach(order => {
            const orderDate = new Date(order.createdAt || order.date);
            let key;

            switch (groupBy) {
                case 'hour':
                    key = `${orderDate.getHours()}:00`;
                    break;
                case 'day':
                    key = orderDate.toISOString().split('T')[0];
                    break;
                case 'week':
                    const weekStart = new Date(orderDate);
                    weekStart.setDate(orderDate.getDate() - orderDate.getDay());
                    key = weekStart.toISOString().split('T')[0];
                    break;
                case 'month':
                    key = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;
                    break;
                default:
                    key = orderDate.toISOString().split('T')[0];
            }

            if (!trends.has(key)) {
                trends.set(key, {
                    period: key,
                    revenue: 0,
                    orders: 0,
                    avgOrderValue: 0
                });
            }

            const current = trends.get(key);
            current.revenue += parseFloat(order.totalAmount) || 0;
            current.orders += 1;
            current.avgOrderValue = current.revenue / current.orders;
        });

        const salesTrends = Array.from(trends.values())
            .sort((a, b) => a.period.localeCompare(b.period));

        this.setCached(cacheKey, salesTrends);
        return salesTrends;
    }

    /**
     * Peak Hours Analysis
     */
    async getPeakHoursAnalysis(dateRange = 'today') {
        const cacheKey = `peakHours_${dateRange}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const orders = await this.getOrders(dateRange);
        const hourlyData = new Array(24).fill(0).map((_, hour) => ({
            hour: `${hour}:00`,
            orders: 0,
            revenue: 0,
            avgOrderValue: 0
        }));

        orders.filter(order => order.status === 'completed').forEach(order => {
            const orderTime = new Date(order.createdAt || order.date);
            const hour = orderTime.getHours();

            hourlyData[hour].orders += 1;
            hourlyData[hour].revenue += parseFloat(order.totalAmount) || 0;
            hourlyData[hour].avgOrderValue = hourlyData[hour].orders > 0 ?
                hourlyData[hour].revenue / hourlyData[hour].orders : 0;
        });

        // Find peak hours
        const peakHour = hourlyData.reduce((peak, current) =>
            current.orders > peak.orders ? current : peak
        );

        const analysis = {
            hourlyData: hourlyData.filter(h => h.orders > 0),
            peakHour: peakHour,
            totalOrders: hourlyData.reduce((sum, h) => sum + h.orders, 0),
            totalRevenue: hourlyData.reduce((sum, h) => sum + h.revenue, 0)
        };

        this.setCached(cacheKey, analysis);
        return analysis;
    }

    /**
     * Expense Analysis
     */
    async getExpenseAnalysis(dateRange = 'today', filters = {}) {
        const cacheKey = `expenseAnalysis_${dateRange}_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        const expenses = await this.getExpenses(dateRange, filters);
        const categoryTotals = new Map();
        let totalExpenses = 0;

        expenses.forEach(expense => {
            const category = expense.category || 'Other';
            const amount = parseFloat(expense.amount) || 0;

            totalExpenses += amount;

            if (!categoryTotals.has(category)) {
                categoryTotals.set(category, {
                    category: category,
                    amount: 0,
                    count: 0,
                    percentage: 0,
                    avgExpense: 0
                });
            }

            const current = categoryTotals.get(category);
            current.amount += amount;
            current.count += 1;
            current.avgExpense = current.amount / current.count;
        });

        // Calculate percentages
        categoryTotals.forEach(category => {
            category.percentage = totalExpenses > 0 ?
                (category.amount / totalExpenses) * 100 : 0;
        });

        const analysis = {
            totalExpenses,
            categoryBreakdown: Array.from(categoryTotals.values())
                .sort((a, b) => b.amount - a.amount),
            expenseCount: expenses.length,
            avgExpenseAmount: expenses.length > 0 ? totalExpenses / expenses.length : 0
        };

        this.setCached(cacheKey, analysis);
        return analysis;
    }

    /**
     * Get current cash from multiple sources with debugging
     */
    getCurrentCash() {
        console.log('💰 getCurrentCash called');

        // Check multiple possible sources
        const currentCashInHand = localStorage.getItem('currentCashInHand');
        const businessDaysCash = this.getTodayBusinessDayCash();

        console.log('💰 Cash sources:', {
            currentCashInHand: currentCashInHand,
            businessDaysCash: businessDaysCash
        });

        // Primary source: currentCashInHand
        let cash = parseFloat(currentCashInHand || '0');

        // If no cash in primary source, try business days
        if (cash === 0 && businessDaysCash > 0) {
            cash = businessDaysCash;
            console.log('💰 Using business days cash:', cash);
        }

        console.log('💰 Final cash value:', cash);
        return cash;
    }

    /**
     * Get today's cash from business days
     */
    getTodayBusinessDayCash() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const businessDays = JSON.parse(localStorage.getItem('businessDays') || '[]');
            const todayEntry = businessDays.find(entry => entry.date === today);

            if (todayEntry) {
                return parseFloat(todayEntry.opening_balance || todayEntry.closing_balance || '0');
            }
            return 0;
        } catch (error) {
            console.error('❌ Error getting business day cash:', error);
            return 0;
        }
    }
}

// Export for global use
window.RestaurantAnalytics = RestaurantAnalytics;
