/**
 * Sample Data Generator for Restaurant Analytics
 * Creates realistic sample data for testing and demonstration
 */

class SampleDataGenerator {
    constructor() {
        this.menuItems = [
            { name: 'Chicken Biryani', price: 450, cost: 200, category: 'Main Course' },
            { name: '<PERSON><PERSON>', price: 650, cost: 350, category: 'Main Course' },
            { name: 'Chicken Tikka', price: 380, cost: 180, category: 'BBQ' },
            { name: 'Fish Fry', price: 320, cost: 150, category: 'Seafood' },
            { name: '<PERSON>', price: 280, cost: 120, category: 'Vegetarian' },
            { name: '<PERSON><PERSON>', price: 80, cost: 30, category: 'Bread' },
            { name: '<PERSON><PERSON><PERSON>', price: 150, cost: 60, category: 'Rice' },
            { name: 'Fresh Lime Soda', price: 120, cost: 40, category: 'Beverages' },
            { name: '<PERSON><PERSON>', price: 150, cost: 60, category: 'Beverages' },
            { name: 'Gulab Jamun', price: 180, cost: 70, category: 'Desserts' }
        ];
        
        this.expenseCategories = [
            'ingredients', 'staff', 'utilities', 'rent', 'maintenance', 'marketing', 'other'
        ];
    }

    /**
     * Generate sample data if none exists - DISABLED FOR REAL DATA
     */
    generateSampleDataIfNeeded() {
        try {
            console.log('🎲 Sample data generation DISABLED - using real data only');

            // Only ensure menu items exist (these are needed for the system to function)
            const menuItems = JSON.parse(localStorage.getItem('menuItems') || '[]');
            if (menuItems.length === 0) {
                console.log('🍽️ Creating default menu items...');
                localStorage.setItem('menuItems', JSON.stringify(this.menuItems));
            }

            // Set initial cash ONLY if not set (don't override user's morning balance)
            if (!localStorage.getItem('currentCashInHand')) {
                console.log('💰 Setting initial cash to 0 (user can set morning balance)');
                localStorage.setItem('currentCashInHand', '0');
            }

            console.log('✅ Real data mode - no sample data generated');

        } catch (error) {
            console.error('❌ Failed to initialize real data mode:', error);
        }
    }

    /**
     * Generate sample orders
     */
    generateSampleOrders(count = 30) {
        const orders = JSON.parse(localStorage.getItem('orders') || '[]');
        const today = new Date();
        
        for (let i = 0; i < count; i++) {
            // Generate orders for the last 7 days
            const orderDate = new Date(today);
            orderDate.setDate(today.getDate() - Math.floor(Math.random() * 7));
            
            // Random time during business hours (10 AM to 10 PM)
            const hour = 10 + Math.floor(Math.random() * 12);
            const minute = Math.floor(Math.random() * 60);
            orderDate.setHours(hour, minute, 0, 0);
            
            const order = this.generateSingleOrder(orderDate, i + 1);
            orders.push(order);
        }
        
        localStorage.setItem('orders', JSON.stringify(orders));
    }

    /**
     * Generate a single order
     */
    generateSingleOrder(date, orderNumber) {
        const orderTypes = ['dine_in', 'takeaway', 'delivery'];
        const paymentMethods = ['cash', 'card'];
        
        // Select 1-4 random items
        const itemCount = 1 + Math.floor(Math.random() * 4);
        const selectedItems = [];
        const usedItems = new Set();
        
        let subtotal = 0;
        
        for (let i = 0; i < itemCount; i++) {
            let item;
            do {
                item = this.menuItems[Math.floor(Math.random() * this.menuItems.length)];
            } while (usedItems.has(item.name));
            
            usedItems.add(item.name);
            
            const quantity = 1 + Math.floor(Math.random() * 3);
            const itemTotal = item.price * quantity;
            
            selectedItems.push({
                name: item.name,
                itemName: item.name,
                price: item.price,
                quantity: quantity,
                total: itemTotal
            });
            
            subtotal += itemTotal;
        }
        
        const tax = subtotal * 0.05; // 5% tax
        const total = subtotal + tax;
        
        return {
            id: 'order_' + Date.now() + '_' + orderNumber,
            orderNumber: 'ORD-' + String(orderNumber).padStart(4, '0'),
            customerName: this.generateCustomerName(),
            customerPhone: this.generatePhoneNumber(),
            tableNumber: Math.floor(Math.random() * 20) + 1,
            orderType: orderTypes[Math.floor(Math.random() * orderTypes.length)],
            items: selectedItems,
            subtotal: subtotal,
            taxAmount: tax,
            discountAmount: 0,
            totalAmount: total,
            paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
            status: 'completed',
            orderDate: date.toISOString().split('T')[0],
            orderTime: date.toTimeString().split(' ')[0],
            date: date.toISOString().split('T')[0],
            createdAt: date.toISOString(),
            completedAt: new Date(date.getTime() + (15 + Math.random() * 30) * 60000).toISOString()
        };
    }

    /**
     * Generate sample expenses
     */
    generateSampleExpenses(count = 15) {
        const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
        const today = new Date();
        
        const sampleExpenses = [
            { description: 'Chicken Purchase', category: 'ingredients', amount: 2500 },
            { description: 'Rice Purchase', category: 'ingredients', amount: 1200 },
            { description: 'Vegetables Purchase', category: 'ingredients', amount: 800 },
            { description: 'Spices Purchase', category: 'ingredients', amount: 600 },
            { description: 'Cook Salary', category: 'staff', amount: 1500 },
            { description: 'Waiter Salary', category: 'staff', amount: 1200 },
            { description: 'Electricity Bill', category: 'utilities', amount: 3500 },
            { description: 'Gas Bill', category: 'utilities', amount: 2200 },
            { description: 'Restaurant Rent', category: 'rent', amount: 15000 },
            { description: 'Equipment Maintenance', category: 'maintenance', amount: 800 },
            { description: 'Social Media Ads', category: 'marketing', amount: 1000 },
            { description: 'Cleaning Supplies', category: 'other', amount: 400 },
            { description: 'Paper Napkins', category: 'other', amount: 300 },
            { description: 'Delivery Bags', category: 'other', amount: 500 },
            { description: 'Kitchen Utensils', category: 'other', amount: 700 }
        ];
        
        for (let i = 0; i < Math.min(count, sampleExpenses.length); i++) {
            const expenseTemplate = sampleExpenses[i];
            
            // Generate expenses for the last 7 days
            const expenseDate = new Date(today);
            expenseDate.setDate(today.getDate() - Math.floor(Math.random() * 7));
            
            const expense = {
                id: 'expense_' + Date.now() + '_' + i,
                description: expenseTemplate.description,
                amount: expenseTemplate.amount + (Math.random() * 200 - 100), // Add some variation
                category: expenseTemplate.category,
                expense_date: expenseDate.toISOString().split('T')[0],
                expense_time: expenseDate.toTimeString().split(' ')[0],
                payment_method: 'cash',
                date: expenseDate.toISOString().split('T')[0],
                createdAt: expenseDate.toISOString(),
                created_by: 'admin'
            };
            
            expenses.push(expense);
        }
        
        localStorage.setItem('expenses', JSON.stringify(expenses));
    }

    /**
     * Generate random customer name
     */
    generateCustomerName() {
        const firstNames = ['Ahmed', 'Ali', 'Hassan', 'Fatima', 'Aisha', 'Omar', 'Sara', 'Zain', 'Maryam', 'Usman'];
        const lastNames = ['Khan', 'Sheikh', 'Ahmad', 'Ali', 'Hassan', 'Malik', 'Qureshi', 'Siddiqui', 'Butt', 'Chaudhry'];
        
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        
        return `${firstName} ${lastName}`;
    }

    /**
     * Generate random phone number
     */
    generatePhoneNumber() {
        const prefixes = ['0300', '0301', '0302', '0303', '0304', '0305'];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const number = Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
        
        return prefix + number;
    }

    /**
     * Clear all sample data and reset to real data mode
     */
    clearSampleData() {
        console.log('🗑️ Clearing fake/sample data to enable real data mode...');

        // Clear potentially fake data
        const keys = ['orders', 'expenses'];
        keys.forEach(key => {
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            console.log(`🗑️ Clearing ${key}: ${data.length} items`);
            localStorage.removeItem(key);
        });

        // Reset to empty arrays for real data
        localStorage.setItem('orders', '[]');
        localStorage.setItem('expenses', '[]');

        // Reset cash to 0 (user can set morning balance)
        localStorage.setItem('currentCashInHand', '0');

        console.log('✅ Sample data cleared - ready for real data');
    }

    /**
     * Initialize real data mode (clear fake data)
     */
    initializeRealDataMode() {
        console.log('🔄 Initializing real data mode...');

        // Check if we have fake data (orders with specific patterns)
        const orders = JSON.parse(localStorage.getItem('orders') || '[]');
        const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');

        // Check for sample data patterns
        const hasFakeOrders = orders.some(order =>
            order.id && order.id.includes('_') && order.orderNumber && order.orderNumber.startsWith('ORD-')
        );

        const hasFakeExpenses = expenses.some(expense =>
            expense.id && expense.id.includes('_') && expense.created_by === 'admin'
        );

        if (hasFakeOrders || hasFakeExpenses) {
            console.log('🗑️ Detected fake data, clearing...');
            this.clearSampleData();
        } else {
            console.log('✅ No fake data detected, keeping existing real data');
        }

        // Ensure menu items exist
        const menuItems = JSON.parse(localStorage.getItem('menuItems') || '[]');
        if (menuItems.length === 0) {
            console.log('🍽️ Adding default menu items...');
            localStorage.setItem('menuItems', JSON.stringify(this.menuItems));
        }

        console.log('✅ Real data mode initialized');
    }

    /**
     * Reset to fresh sample data
     */
    resetToSampleData() {
        this.clearSampleData();
        this.generateSampleDataIfNeeded();
        console.log('🔄 Reset to fresh sample data');
    }
}

// DISABLED: Auto-generate sample data on load
// Using real data only - no automatic sample data generation
document.addEventListener('DOMContentLoaded', () => {
    console.log('📊 Sample data auto-generation DISABLED - using real data only');
    // const generator = new SampleDataGenerator();
    // generator.generateSampleDataIfNeeded();
});

// Export for global use
window.SampleDataGenerator = SampleDataGenerator;
