/**
 * Zaiqa Staff Management System - Enhanced Dehari (Daily Wage) System
 * Comprehensive staff management with account balances and attendance tracking
 */

class ZaiqaStaffManagement {
    constructor() {
        this.version = '2.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize the staff management system
     */
    init() {
        try {
            console.log('👥 Initializing Enhanced Staff Management v' + this.version);
            
            // Initialize staff data structure
            this.initializeStaffData();
            
            // Set up staff page integration
            this.setupStaffPageIntegration();
            
            // Initialize attendance tracking
            this.initializeAttendanceTracking();
            
            this.initialized = true;
            console.log('✅ Enhanced Staff Management initialized successfully');
            
        } catch (error) {
            console.error('❌ Staff Management initialization failed:', error);
        }
    }

    /**
     * Initialize staff data structure
     */
    initializeStaffData() {
        try {
            // Ensure staff data has proper structure
            const existingStaff = JSON.parse(localStorage.getItem('staff') || '[]');
            
            // Upgrade existing staff records to new format
            const upgradedStaff = existingStaff.map(staff => {
                return {
                    id: staff.id || 'staff_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                    name: staff.name || '',
                    position: staff.position || '',
                    phone: staff.phone || '',
                    address: staff.address || '',
                    isActive: staff.isActive !== undefined ? staff.isActive : true,
                    
                    // Payment structure
                    paymentType: staff.paymentType || 'daily', // 'daily' or 'monthly'
                    dailyWage: staff.dailyWage || staff.monthlySalary ? (staff.monthlySalary / 30) : 0,
                    monthlySalary: staff.monthlySalary || (staff.dailyWage * 30) || 0,
                    
                    // Account balance system
                    accountBalance: staff.accountBalance || 0, // Amount owed to staff
                    totalEarned: staff.totalEarned || 0, // Total earned to date
                    totalAdvances: staff.totalAdvances || 0, // Total advances given
                    
                    // Attendance tracking
                    attendanceHistory: staff.attendanceHistory || [],
                    lastAttendanceDate: staff.lastAttendanceDate || null,
                    
                    // Metadata
                    createdAt: staff.createdAt || new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
            });

            localStorage.setItem('staff', JSON.stringify(upgradedStaff));
            console.log('✅ Staff data structure initialized');

        } catch (error) {
            console.error('❌ Failed to initialize staff data:', error);
        }
    }

    /**
     * Set up staff page integration
     */
    setupStaffPageIntegration() {
        try {
            // Override the staff page loading function
            if (window.app && typeof window.app.loadStaffPage === 'function') {
                window.app.originalLoadStaffPage = window.app.loadStaffPage;
                
                window.app.loadStaffPage = (pageElement) => {
                    this.renderEnhancedStaffPage(pageElement);
                };
            }

        } catch (error) {
            console.error('❌ Failed to setup staff page integration:', error);
        }
    }

    /**
     * Render enhanced staff page
     */
    renderEnhancedStaffPage(pageElement) {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            
            pageElement.innerHTML = `
                <div class="staff-management-container">
                    <div class="page-header">
                        <h1><i class="fas fa-users"></i> Staff Management</h1>
                        <div class="header-actions">
                            <button onclick="window.staffManager.showAddStaffModal()" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Staff
                            </button>
                            <button onclick="window.staffManager.showAttendanceModal()" class="btn btn-secondary">
                                <i class="fas fa-calendar-check"></i> Mark Attendance
                            </button>
                        </div>
                    </div>
                    
                    <div class="staff-summary">
                        ${this.renderStaffSummary(staff)}
                    </div>
                    
                    <div class="staff-sections">
                        <div class="active-staff-section">
                            <h2>Active Staff Members</h2>
                            <div class="staff-grid">
                                ${this.renderStaffGrid(staff.filter(s => s.isActive))}
                            </div>
                        </div>
                        
                        ${staff.filter(s => !s.isActive).length > 0 ? `
                            <div class="inactive-staff-section">
                                <h2>Inactive Staff Members</h2>
                                <div class="staff-grid">
                                    ${this.renderStaffGrid(staff.filter(s => !s.isActive))}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

        } catch (error) {
            console.error('❌ Failed to render enhanced staff page:', error);
            pageElement.innerHTML = '<div class="error">Failed to load staff management</div>';
        }
    }

    /**
     * Render staff summary
     */
    renderStaffSummary(staff) {
        const activeStaff = staff.filter(s => s.isActive);
        const totalOwed = activeStaff.reduce((sum, s) => sum + (s.accountBalance || 0), 0);
        const totalDailyWages = activeStaff.reduce((sum, s) => sum + (s.dailyWage || 0), 0);
        
        return `
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-content">
                        <h3>Active Staff</h3>
                        <div class="card-value">${activeStaff.length}</div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="card-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="card-content">
                        <h3>Total Owed</h3>
                        <div class="card-value">PKR ${totalOwed.toLocaleString()}</div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="card-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="card-content">
                        <h3>Daily Wages</h3>
                        <div class="card-value">PKR ${totalDailyWages.toLocaleString()}</div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="card-content">
                        <h3>Monthly Cost</h3>
                        <div class="card-value">PKR ${(totalDailyWages * 30).toLocaleString()}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render staff grid
     */
    renderStaffGrid(staff) {
        if (staff.length === 0) {
            return '<div class="no-staff">No staff members found</div>';
        }

        return staff.map(member => `
            <div class="staff-card ${member.isActive ? 'active' : 'inactive'}">
                <div class="staff-header">
                    <div class="staff-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="staff-info">
                        <h3>${member.name}</h3>
                        <p class="position">${member.position}</p>
                        <p class="phone">${member.phone}</p>
                    </div>
                    <div class="staff-status">
                        <span class="status-badge ${member.isActive ? 'active' : 'inactive'}">
                            ${member.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
                
                <div class="staff-payment">
                    <div class="payment-info">
                        <div class="payment-item">
                            <span class="label">Payment Type:</span>
                            <span class="value">${member.paymentType === 'daily' ? 'Daily Wage (Dehari)' : 'Monthly Salary'}</span>
                        </div>
                        <div class="payment-item">
                            <span class="label">Daily Rate:</span>
                            <span class="value">PKR ${(member.dailyWage || 0).toLocaleString()}</span>
                        </div>
                        <div class="payment-item">
                            <span class="label">Monthly Equivalent:</span>
                            <span class="value">PKR ${(member.monthlySalary || 0).toLocaleString()}</span>
                        </div>
                    </div>
                </div>
                
                <div class="staff-balance">
                    <div class="balance-info">
                        <div class="balance-item">
                            <span class="label">Account Balance:</span>
                            <span class="value balance ${(member.accountBalance || 0) >= 0 ? 'positive' : 'negative'}">
                                PKR ${Math.abs(member.accountBalance || 0).toLocaleString()}
                                ${(member.accountBalance || 0) >= 0 ? '(Owed)' : '(Advance)'}
                            </span>
                        </div>
                        <div class="balance-item">
                            <span class="label">Total Earned:</span>
                            <span class="value">PKR ${(member.totalEarned || 0).toLocaleString()}</span>
                        </div>
                    </div>
                </div>
                
                <div class="staff-actions">
                    <button onclick="window.staffManager.markAttendance('${member.id}')" class="btn btn-sm btn-success">
                        <i class="fas fa-check"></i> Mark Present
                    </button>
                    <button onclick="window.staffManager.showAdvanceModal('${member.id}')" class="btn btn-sm btn-warning">
                        <i class="fas fa-hand-holding-usd"></i> Give Advance
                    </button>
                    <button onclick="window.staffManager.showEditStaffModal('${member.id}')" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button onclick="window.staffManager.toggleStaffStatus('${member.id}')" class="btn btn-sm ${member.isActive ? 'btn-danger' : 'btn-success'}">
                        <i class="fas ${member.isActive ? 'fa-user-times' : 'fa-user-check'}"></i>
                        ${member.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * Show add staff modal
     */
    showAddStaffModal() {
        try {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-user-plus"></i> Add New Staff Member</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    
                    <div class="modal-body">
                        <form id="addStaffForm" class="staff-form">
                            <div class="form-section">
                                <h3>Personal Information</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Full Name *</label>
                                        <input type="text" id="staffName" required placeholder="Enter full name">
                                    </div>
                                    <div class="form-group">
                                        <label>Position *</label>
                                        <input type="text" id="staffPosition" required placeholder="e.g., Chef, Waiter, Cleaner">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Phone Number</label>
                                        <input type="tel" id="staffPhone" placeholder="Enter phone number">
                                    </div>
                                    <div class="form-group">
                                        <label>Address</label>
                                        <input type="text" id="staffAddress" placeholder="Enter address">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h3>Payment Structure</h3>
                                <div class="payment-type-selection">
                                    <label class="radio-label">
                                        <input type="radio" name="paymentType" value="daily" checked onchange="window.staffManager.togglePaymentType('daily')">
                                        <span>Daily Wage (Dehari) - دہاڑی</span>
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="paymentType" value="monthly" onchange="window.staffManager.togglePaymentType('monthly')">
                                        <span>Monthly Salary - ماہانہ تنخواہ</span>
                                    </label>
                                </div>
                                
                                <div class="payment-inputs">
                                    <div class="form-group" id="dailyWageGroup">
                                        <label>Daily Wage (PKR) *</label>
                                        <input type="number" id="dailyWage" required placeholder="Enter daily wage amount" onchange="window.staffManager.calculateMonthlyFromDaily()">
                                    </div>
                                    <div class="form-group" id="monthlyGroup" style="display: none;">
                                        <label>Monthly Salary (PKR) *</label>
                                        <input type="number" id="monthlySalary" placeholder="Enter monthly salary" onchange="window.staffManager.calculateDailyFromMonthly()">
                                    </div>
                                    <div class="calculated-display">
                                        <span id="calculatedAmount">Monthly equivalent will be calculated automatically</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button onclick="window.staffManager.saveNewStaff()" class="btn btn-primary">
                            <i class="fas fa-save"></i> Add Staff Member
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);

        } catch (error) {
            console.error('❌ Failed to show add staff modal:', error);
        }
    }

    /**
     * Toggle payment type in form
     */
    togglePaymentType(type) {
        try {
            const dailyGroup = document.getElementById('dailyWageGroup');
            const monthlyGroup = document.getElementById('monthlyGroup');
            const dailyInput = document.getElementById('dailyWage');
            const monthlyInput = document.getElementById('monthlySalary');

            if (type === 'daily') {
                dailyGroup.style.display = 'block';
                monthlyGroup.style.display = 'none';
                dailyInput.required = true;
                monthlyInput.required = false;
            } else {
                dailyGroup.style.display = 'none';
                monthlyGroup.style.display = 'block';
                dailyInput.required = false;
                monthlyInput.required = true;
            }

        } catch (error) {
            console.error('❌ Failed to toggle payment type:', error);
        }
    }

    /**
     * Calculate monthly from daily wage
     */
    calculateMonthlyFromDaily() {
        try {
            const dailyWage = parseFloat(document.getElementById('dailyWage')?.value || 0);
            const monthlyEquivalent = dailyWage * 30;
            
            document.getElementById('calculatedAmount').textContent = 
                `Monthly equivalent: PKR ${monthlyEquivalent.toLocaleString()}`;

        } catch (error) {
            console.error('❌ Failed to calculate monthly from daily:', error);
        }
    }

    /**
     * Calculate daily from monthly salary
     */
    calculateDailyFromMonthly() {
        try {
            const monthlySalary = parseFloat(document.getElementById('monthlySalary')?.value || 0);
            const dailyEquivalent = monthlySalary / 30;
            
            document.getElementById('calculatedAmount').textContent = 
                `Daily equivalent: PKR ${dailyEquivalent.toFixed(2)}`;

        } catch (error) {
            console.error('❌ Failed to calculate daily from monthly:', error);
        }
    }

    /**
     * Save new staff member
     */
    saveNewStaff() {
        try {
            const form = document.getElementById('addStaffForm');
            const formData = new FormData(form);
            
            const name = document.getElementById('staffName')?.value?.trim();
            const position = document.getElementById('staffPosition')?.value?.trim();
            const phone = document.getElementById('staffPhone')?.value?.trim();
            const address = document.getElementById('staffAddress')?.value?.trim();
            const paymentType = document.querySelector('input[name="paymentType"]:checked')?.value;
            
            if (!name || !position) {
                alert('Please fill in all required fields');
                return;
            }

            let dailyWage = 0;
            let monthlySalary = 0;

            if (paymentType === 'daily') {
                dailyWage = parseFloat(document.getElementById('dailyWage')?.value || 0);
                monthlySalary = dailyWage * 30;
            } else {
                monthlySalary = parseFloat(document.getElementById('monthlySalary')?.value || 0);
                dailyWage = monthlySalary / 30;
            }

            if (dailyWage <= 0) {
                alert('Please enter a valid wage amount');
                return;
            }

            const newStaff = {
                id: 'staff_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                name: name,
                position: position,
                phone: phone,
                address: address,
                isActive: true,
                paymentType: paymentType,
                dailyWage: dailyWage,
                monthlySalary: monthlySalary,
                accountBalance: 0,
                totalEarned: 0,
                totalAdvances: 0,
                attendanceHistory: [],
                lastAttendanceDate: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Add to staff array
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            staff.push(newStaff);
            localStorage.setItem('staff', JSON.stringify(staff));

            // Close modal and refresh page
            document.querySelector('.modal-overlay')?.remove();
            this.showNotification(`Staff member ${name} added successfully`, 'success');
            
            // Refresh staff page
            const staffPage = document.getElementById('staffPage');
            if (staffPage && staffPage.style.display !== 'none') {
                this.renderEnhancedStaffPage(staffPage);
            }

        } catch (error) {
            console.error('❌ Failed to save new staff:', error);
            this.showNotification('Failed to add staff member', 'error');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        try {
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(message, type);
            } else {
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        } catch (error) {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Mark staff attendance
     */
    markAttendance(staffId) {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const staffMember = staff.find(s => s.id === staffId);

            if (!staffMember) {
                this.showNotification('Staff member not found', 'error');
                return;
            }

            const today = new Date().toISOString().split('T')[0];

            // Check if already marked present today
            if (staffMember.lastAttendanceDate === today) {
                this.showNotification(`${staffMember.name} is already marked present for today`, 'warning');
                return;
            }

            // Mark attendance
            const attendanceRecord = {
                date: today,
                timestamp: new Date().toISOString(),
                status: 'present',
                dailyWage: staffMember.dailyWage
            };

            staffMember.attendanceHistory.push(attendanceRecord);
            staffMember.lastAttendanceDate = today;

            // Add daily wage to account balance
            staffMember.accountBalance = (staffMember.accountBalance || 0) + staffMember.dailyWage;
            staffMember.totalEarned = (staffMember.totalEarned || 0) + staffMember.dailyWage;
            staffMember.updatedAt = new Date().toISOString();

            // Save updated staff data
            localStorage.setItem('staff', JSON.stringify(staff));

            // Add to expenses automatically
            this.addAttendanceExpense(staffMember, today);

            this.showNotification(`${staffMember.name} marked present. PKR ${staffMember.dailyWage} added to account`, 'success');

            // Refresh staff page
            const staffPage = document.getElementById('staffPage');
            if (staffPage && staffPage.style.display !== 'none') {
                this.renderEnhancedStaffPage(staffPage);
            }

        } catch (error) {
            console.error('❌ Failed to mark attendance:', error);
            this.showNotification('Failed to mark attendance', 'error');
        }
    }

    /**
     * Add attendance expense
     */
    addAttendanceExpense(staffMember, date) {
        try {
            const expenseEntry = {
                id: 'attendance_' + staffMember.id + '_' + Date.now(),
                description: `${staffMember.name} - Daily Wage (Attendance)`,
                amount: staffMember.dailyWage,
                category: 'Staff Salaries',
                date: date,
                created_at: new Date().toISOString(),
                type: 'staff_attendance',
                staffId: staffMember.id
            };

            // Add to expenses
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            expenses.push(expenseEntry);
            localStorage.setItem('expenses', JSON.stringify(expenses));

            // Deduct from cash in hand
            if (window.cashManager) {
                window.cashManager.updateCashInHand(-staffMember.dailyWage);
            }

        } catch (error) {
            console.error('❌ Failed to add attendance expense:', error);
        }
    }

    /**
     * Show advance payment modal
     */
    showAdvanceModal(staffId) {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const staffMember = staff.find(s => s.id === staffId);

            if (!staffMember) {
                this.showNotification('Staff member not found', 'error');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-hand-holding-usd"></i> Give Advance Payment</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>

                    <div class="modal-body">
                        <div class="staff-info">
                            <h3>${staffMember.name}</h3>
                            <p>Position: ${staffMember.position}</p>
                            <p>Current Balance: PKR ${(staffMember.accountBalance || 0).toLocaleString()}</p>
                        </div>

                        <div class="advance-form">
                            <div class="form-group">
                                <label>Advance Amount (PKR) *</label>
                                <input type="number" id="advanceAmount" placeholder="Enter advance amount" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label>Reason/Description</label>
                                <textarea id="advanceReason" placeholder="Enter reason for advance (optional)" rows="3"></textarea>
                            </div>
                            <div class="advance-warning">
                                <i class="fas fa-info-circle"></i>
                                This amount will be deducted from the staff member's account balance.
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button onclick="window.staffManager.processAdvancePayment('${staffId}')" class="btn btn-warning">
                            <i class="fas fa-money-bill-wave"></i> Give Advance
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

        } catch (error) {
            console.error('❌ Failed to show advance modal:', error);
        }
    }

    /**
     * Process advance payment
     */
    processAdvancePayment(staffId) {
        try {
            const amount = parseFloat(document.getElementById('advanceAmount')?.value || 0);
            const reason = document.getElementById('advanceReason')?.value?.trim() || 'Advance payment';

            if (amount <= 0) {
                alert('Please enter a valid advance amount');
                return;
            }

            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const staffMember = staff.find(s => s.id === staffId);

            if (!staffMember) {
                this.showNotification('Staff member not found', 'error');
                return;
            }

            // Create advance record
            const advanceRecord = {
                id: 'advance_' + Date.now(),
                amount: amount,
                reason: reason,
                date: new Date().toISOString().split('T')[0],
                timestamp: new Date().toISOString()
            };

            // Update staff account
            if (!staffMember.advanceHistory) {
                staffMember.advanceHistory = [];
            }
            staffMember.advanceHistory.push(advanceRecord);
            staffMember.accountBalance = (staffMember.accountBalance || 0) - amount;
            staffMember.totalAdvances = (staffMember.totalAdvances || 0) + amount;
            staffMember.updatedAt = new Date().toISOString();

            // Save updated staff data
            localStorage.setItem('staff', JSON.stringify(staff));

            // Add expense entry
            const expenseEntry = {
                id: 'advance_exp_' + Date.now(),
                description: `Advance to ${staffMember.name} - ${reason}`,
                amount: amount,
                category: 'Staff Advances',
                date: advanceRecord.date,
                created_at: advanceRecord.timestamp,
                type: 'staff_advance',
                staffId: staffId
            };

            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            expenses.push(expenseEntry);
            localStorage.setItem('expenses', JSON.stringify(expenses));

            // Deduct from cash in hand
            if (window.cashManager) {
                window.cashManager.updateCashInHand(-amount);
            }

            // Close modal and refresh
            document.querySelector('.modal-overlay')?.remove();
            this.showNotification(`Advance of PKR ${amount.toLocaleString()} given to ${staffMember.name}`, 'success');

            // Refresh staff page
            const staffPage = document.getElementById('staffPage');
            if (staffPage && staffPage.style.display !== 'none') {
                this.renderEnhancedStaffPage(staffPage);
            }

        } catch (error) {
            console.error('❌ Failed to process advance payment:', error);
            this.showNotification('Failed to process advance payment', 'error');
        }
    }

    /**
     * Show attendance modal for multiple staff
     */
    showAttendanceModal() {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const activeStaff = staff.filter(s => s.isActive);
            const today = new Date().toISOString().split('T')[0];

            if (activeStaff.length === 0) {
                this.showNotification('No active staff members found', 'warning');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content attendance-modal">
                    <div class="modal-header">
                        <h2><i class="fas fa-calendar-check"></i> Mark Attendance - ${today}</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>

                    <div class="modal-body">
                        <div class="attendance-list">
                            ${activeStaff.map(member => `
                                <div class="attendance-item">
                                    <div class="staff-info">
                                        <div class="staff-name">${member.name}</div>
                                        <div class="staff-position">${member.position}</div>
                                        <div class="staff-wage">Daily Wage: PKR ${member.dailyWage.toLocaleString()}</div>
                                    </div>
                                    <div class="attendance-status">
                                        ${member.lastAttendanceDate === today ?
                                            '<span class="status-present">Already Present</span>' :
                                            `<button onclick="window.staffManager.markAttendance('${member.id}')" class="btn btn-success">
                                                <i class="fas fa-check"></i> Mark Present
                                            </button>`
                                        }
                                    </div>
                                </div>
                            `).join('')}
                        </div>

                        <div class="attendance-summary">
                            <h3>Today's Summary</h3>
                            <p>Present: ${activeStaff.filter(s => s.lastAttendanceDate === today).length} / ${activeStaff.length}</p>
                            <p>Total Daily Wages: PKR ${activeStaff.filter(s => s.lastAttendanceDate === today).reduce((sum, s) => sum + s.dailyWage, 0).toLocaleString()}</p>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Close
                        </button>
                        <button onclick="window.staffManager.markAllPresent()" class="btn btn-primary">
                            <i class="fas fa-users-check"></i> Mark All Present
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

        } catch (error) {
            console.error('❌ Failed to show attendance modal:', error);
        }
    }

    /**
     * Mark all staff present
     */
    markAllPresent() {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const activeStaff = staff.filter(s => s.isActive);
            const today = new Date().toISOString().split('T')[0];

            let markedCount = 0;

            activeStaff.forEach(member => {
                if (member.lastAttendanceDate !== today) {
                    this.markAttendance(member.id);
                    markedCount++;
                }
            });

            if (markedCount > 0) {
                this.showNotification(`${markedCount} staff members marked present`, 'success');
            } else {
                this.showNotification('All staff already marked present for today', 'info');
            }

            // Close modal
            document.querySelector('.modal-overlay')?.remove();

        } catch (error) {
            console.error('❌ Failed to mark all present:', error);
        }
    }

    /**
     * Toggle staff status (active/inactive)
     */
    toggleStaffStatus(staffId) {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const staffMember = staff.find(s => s.id === staffId);

            if (!staffMember) {
                this.showNotification('Staff member not found', 'error');
                return;
            }

            const action = staffMember.isActive ? 'deactivate' : 'activate';
            const confirmed = confirm(`Are you sure you want to ${action} ${staffMember.name}?`);

            if (!confirmed) return;

            staffMember.isActive = !staffMember.isActive;
            staffMember.updatedAt = new Date().toISOString();

            localStorage.setItem('staff', JSON.stringify(staff));

            this.showNotification(`${staffMember.name} ${staffMember.isActive ? 'activated' : 'deactivated'} successfully`, 'success');

            // Refresh staff page
            const staffPage = document.getElementById('staffPage');
            if (staffPage && staffPage.style.display !== 'none') {
                this.renderEnhancedStaffPage(staffPage);
            }

        } catch (error) {
            console.error('❌ Failed to toggle staff status:', error);
        }
    }

    /**
     * Initialize attendance tracking
     */
    initializeAttendanceTracking() {
        try {
            // Set up daily attendance checking
            setInterval(() => {
                this.checkDailyAttendance();
            }, 60 * 60 * 1000); // Check every hour

            console.log('✅ Attendance tracking initialized');

        } catch (error) {
            console.error('❌ Failed to initialize attendance tracking:', error);
        }
    }

    /**
     * Check daily attendance
     */
    checkDailyAttendance() {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const activeStaff = staff.filter(s => s.isActive);
            const today = new Date().toISOString().split('T')[0];

            const presentCount = activeStaff.filter(s => s.lastAttendanceDate === today).length;
            const totalCount = activeStaff.length;

            if (presentCount < totalCount) {
                console.log(`📊 Attendance Status: ${presentCount}/${totalCount} staff present today`);
            }

        } catch (error) {
            console.error('❌ Failed to check daily attendance:', error);
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize the staff management system
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.staffManager = new ZaiqaStaffManagement();
    }, 2500);
});

// Export for global use
window.ZaiqaStaffManagement = ZaiqaStaffManagement;
