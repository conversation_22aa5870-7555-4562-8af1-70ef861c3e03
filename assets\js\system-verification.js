/**
 * Zaiqa System Verification - Test All Critical Fixes
 * Verifies that all three critical issues have been resolved
 */

class ZaiqaSystemVerification {
    constructor() {
        this.version = '1.0.0';
        this.testResults = {};
        
        console.log('🔍 Starting Zaiqa System Verification v' + this.version);
    }

    /**
     * Run comprehensive system verification
     */
    async runFullVerification() {
        try {
            console.log('🔍 Running comprehensive system verification...');
            
            // Test Issue 1: Reports Page Integration
            await this.testReportsPageIntegration();
            
            // Test Issue 2: Data Accuracy
            await this.testDataAccuracy();
            
            // Test Issue 3: Manual Day Transition
            await this.testManualDayTransition();
            
            // Generate verification report
            this.generateVerificationReport();
            
        } catch (error) {
            console.error('❌ System verification failed:', error);
        }
    }

    /**
     * Test Issue 1: Reports Page Integration
     */
    async testReportsPageIntegration() {
        console.log('📊 Testing Reports Page Integration...');
        
        try {
            // Check if new reports system is loaded
            const newReportsLoaded = typeof window.ZaiqaReportsPage !== 'undefined';
            this.testResults.newReportsClassLoaded = newReportsLoaded;
            
            // Check if reports instance is created
            const reportsInstanceCreated = typeof window.zaiqaReports !== 'undefined';
            this.testResults.reportsInstanceCreated = reportsInstanceCreated;
            
            // Check if app integration is working
            const appIntegrationWorking = window.app && 
                typeof window.app.loadReportsPage === 'function' &&
                window.app.originalLoadReportsPage !== undefined;
            this.testResults.appIntegrationWorking = appIntegrationWorking;
            
            // Test reports page rendering
            if (reportsInstanceCreated) {
                const testElement = document.createElement('div');
                try {
                    window.zaiqaReports.render(testElement);
                    this.testResults.reportsRenderingWorking = testElement.innerHTML.includes('reports-container');
                } catch (error) {
                    this.testResults.reportsRenderingWorking = false;
                    this.testResults.reportsRenderingError = error.message;
                }
            }
            
            console.log('✅ Reports Page Integration tests completed');
            
        } catch (error) {
            console.error('❌ Reports Page Integration test failed:', error);
            this.testResults.reportsPageIntegrationError = error.message;
        }
    }

    /**
     * Test Issue 2: Data Accuracy
     */
    async testDataAccuracy() {
        console.log('📊 Testing Data Accuracy...');
        
        try {
            // Test direct storage calculations
            if (window.zaiqaReports) {
                // Test today's metrics calculation
                const todayMetrics = window.zaiqaReports.calculateTodayMetricsFromStorage();
                this.testResults.todayMetricsCalculation = {
                    working: todayMetrics && typeof todayMetrics.revenue === 'object',
                    hasRevenue: todayMetrics.revenue && typeof todayMetrics.revenue.totalRevenue === 'number',
                    hasExpenses: todayMetrics.expenses && typeof todayMetrics.expenses.totalExpenses === 'number',
                    hasProfitLoss: todayMetrics.profitLoss && typeof todayMetrics.profitLoss.grossProfit === 'number'
                };
                
                // Test Udhar summary calculation
                const udharSummary = window.zaiqaReports.calculateUdharSummaryFromStorage();
                this.testResults.udharSummaryCalculation = {
                    working: udharSummary && typeof udharSummary.totalRemainingAmount === 'number',
                    hasData: udharSummary.totalCustomers >= 0
                };
                
                // Test inventory calculation
                const inventoryValue = window.zaiqaReports.calculateInventoryValueFromStorage();
                this.testResults.inventoryCalculation = {
                    working: inventoryValue && typeof inventoryValue.totalValue === 'number',
                    hasData: inventoryValue.totalItems >= 0
                };
            }
            
            // Test localStorage data availability
            const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            const udhars = JSON.parse(localStorage.getItem('udhars') || '[]');
            
            this.testResults.dataAvailability = {
                ordersCount: orders.length,
                expensesCount: expenses.length,
                udharsCount: udhars.length,
                hasData: orders.length > 0 || expenses.length > 0 || udhars.length > 0
            };
            
            console.log('✅ Data Accuracy tests completed');
            
        } catch (error) {
            console.error('❌ Data Accuracy test failed:', error);
            this.testResults.dataAccuracyError = error.message;
        }
    }

    /**
     * Test Issue 3: Manual Day Transition
     */
    async testManualDayTransition() {
        console.log('📅 Testing Manual Day Transition...');
        
        try {
            // Check if business day manager is loaded
            const businessDayManagerLoaded = typeof window.ZaiqaBusinessDayManager !== 'undefined';
            this.testResults.businessDayManagerLoaded = businessDayManagerLoaded;
            
            // Check if business day manager instance is created
            const businessDayManagerInstance = typeof window.businessDayManager !== 'undefined';
            this.testResults.businessDayManagerInstance = businessDayManagerInstance;
            
            // Check if automatic transitions are disabled
            if (window.app) {
                const autoTransitionsDisabled = window.app.originalCheckBusinessDayTransition !== undefined ||
                    window.app.originalHandleMidnightReset !== undefined;
                this.testResults.autoTransitionsDisabled = autoTransitionsDisabled;
            }
            
            // Check if manual controls are available
            const dayTransitionButton = document.querySelector('.day-transition-btn');
            this.testResults.manualControlsAvailable = dayTransitionButton !== null;
            
            // Test business date management
            if (window.businessDayManager) {
                const currentBusinessDate = window.businessDayManager.getCurrentBusinessDate();
                this.testResults.businessDateManagement = {
                    working: typeof currentBusinessDate === 'string',
                    currentDate: currentBusinessDate
                };
            }
            
            console.log('✅ Manual Day Transition tests completed');
            
        } catch (error) {
            console.error('❌ Manual Day Transition test failed:', error);
            this.testResults.manualDayTransitionError = error.message;
        }
    }

    /**
     * Generate comprehensive verification report
     */
    generateVerificationReport() {
        console.log('📋 Generating Verification Report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            version: this.version,
            overallStatus: this.calculateOverallStatus(),
            testResults: this.testResults,
            recommendations: this.generateRecommendations()
        };
        
        // Display report in console
        console.log('📋 ZAIQA SYSTEM VERIFICATION REPORT');
        console.log('=====================================');
        console.log(`Overall Status: ${report.overallStatus}`);
        console.log('Test Results:', this.testResults);
        console.log('Recommendations:', report.recommendations);
        
        // Store report for later access
        window.zaiqaVerificationReport = report;
        
        // Show user-friendly summary
        this.showVerificationSummary(report);
        
        return report;
    }

    /**
     * Calculate overall system status
     */
    calculateOverallStatus() {
        const criticalTests = [
            this.testResults.newReportsClassLoaded,
            this.testResults.reportsInstanceCreated,
            this.testResults.reportsRenderingWorking,
            this.testResults.businessDayManagerLoaded,
            this.testResults.businessDayManagerInstance
        ];
        
        const passedTests = criticalTests.filter(test => test === true).length;
        const totalTests = criticalTests.length;
        
        if (passedTests === totalTests) {
            return 'EXCELLENT - All critical systems working';
        } else if (passedTests >= totalTests * 0.8) {
            return 'GOOD - Most systems working, minor issues';
        } else if (passedTests >= totalTests * 0.6) {
            return 'FAIR - Some systems working, needs attention';
        } else {
            return 'POOR - Critical issues need immediate attention';
        }
    }

    /**
     * Generate recommendations based on test results
     */
    generateRecommendations() {
        const recommendations = [];
        
        if (!this.testResults.newReportsClassLoaded) {
            recommendations.push('Load the ZaiqaReportsPage class - check reports-page.js file');
        }
        
        if (!this.testResults.reportsInstanceCreated) {
            recommendations.push('Initialize the reports instance - check app-integration.js');
        }
        
        if (!this.testResults.reportsRenderingWorking) {
            recommendations.push('Fix reports rendering issues - check console for errors');
        }
        
        if (!this.testResults.businessDayManagerLoaded) {
            recommendations.push('Load the Business Day Manager - check business-day-manager.js file');
        }
        
        if (!this.testResults.manualControlsAvailable) {
            recommendations.push('Add manual day transition controls to the interface');
        }
        
        if (!this.testResults.dataAvailability?.hasData) {
            recommendations.push('Add sample data for testing - no orders/expenses found');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('All systems are working correctly! 🎉');
        }
        
        return recommendations;
    }

    /**
     * Show user-friendly verification summary - COMPLETELY DISABLED
     */
    showVerificationSummary(report) {
        // COMPLETELY DISABLED - No modal will ever show
        console.log('📋 System Verification Summary (Modal Disabled):', report.overallStatus);
        return;
            summaryModal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-check-circle"></i> System Verification Report</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="verification-status ${this.getStatusClass(report.overallStatus)}">
                            <h3>${report.overallStatus}</h3>
                        </div>
                        
                        <div class="test-results">
                            <h4>Test Results Summary:</h4>
                            <ul>
                                <li class="${this.testResults.newReportsClassLoaded ? 'pass' : 'fail'}">
                                    Reports Page Class: ${this.testResults.newReportsClassLoaded ? 'LOADED' : 'FAILED'}
                                </li>
                                <li class="${this.testResults.reportsInstanceCreated ? 'pass' : 'fail'}">
                                    Reports Instance: ${this.testResults.reportsInstanceCreated ? 'CREATED' : 'FAILED'}
                                </li>
                                <li class="${this.testResults.reportsRenderingWorking ? 'pass' : 'fail'}">
                                    Reports Rendering: ${this.testResults.reportsRenderingWorking ? 'WORKING' : 'FAILED'}
                                </li>
                                <li class="${this.testResults.businessDayManagerLoaded ? 'pass' : 'fail'}">
                                    Business Day Manager: ${this.testResults.businessDayManagerLoaded ? 'LOADED' : 'FAILED'}
                                </li>
                                <li class="${this.testResults.manualControlsAvailable ? 'pass' : 'fail'}">
                                    Manual Controls: ${this.testResults.manualControlsAvailable ? 'AVAILABLE' : 'MISSING'}
                                </li>
                            </ul>
                        </div>
                        
                        <div class="recommendations">
                            <h4>Recommendations:</h4>
                            <ul>
                                ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-primary">
                            Close Report
                        </button>
                        <button onclick="window.zaiqaVerification.runFullVerification()" class="btn btn-secondary">
                            Re-run Tests
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(summaryModal);
            
        } catch (error) {
            console.error('❌ Failed to show verification summary:', error);
        }
    }

    /**
     * Get CSS class for status
     */
    getStatusClass(status) {
        if (status.includes('EXCELLENT')) return 'status-excellent';
        if (status.includes('GOOD')) return 'status-good';
        if (status.includes('FAIR')) return 'status-fair';
        return 'status-poor';
    }
}

// COMPLETELY DISABLED - No auto-run, no initialization
// document.addEventListener('DOMContentLoaded', function() {
//     setTimeout(() => {
//         window.zaiqaVerification = new ZaiqaSystemVerification();
//         console.log('✅ System verification loaded (auto-run disabled)');
//     }, 2000);
// });

console.log('🚫 System verification COMPLETELY DISABLED');

// Export for global use
window.ZaiqaSystemVerification = ZaiqaSystemVerification;
