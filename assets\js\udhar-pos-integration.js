/**
 * Udhar POS Integration - Add Udhar payment method to POS system
 * Integrates Udhar payments with existing POS and customer management
 */

class ZaiqaUdharPOSIntegration {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize Udhar POS integration
     */
    init() {
        try {
            console.log('💳 Initializing Udhar POS Integration v' + this.version);
            
            // Set up POS payment method integration
            this.setupPOSIntegration();
            
            this.initialized = true;
            console.log('✅ Udhar POS Integration initialized successfully');
            
        } catch (error) {
            console.error('❌ Udhar POS Integration initialization failed:', error);
        }
    }

    /**
     * Set up POS payment method integration
     */
    setupPOSIntegration() {
        try {
            // Override POS payment processing
            if (window.app && typeof window.app.processPayment === 'function') {
                window.app.originalProcessPayment = window.app.processPayment;
                
                window.app.processPayment = (paymentMethod, orderData) => {
                    if (paymentMethod === 'udhar') {
                        return this.processUdharPayment(orderData);
                    } else {
                        return window.app.originalProcessPayment(paymentMethod, orderData);
                    }
                };
            }

            // Add Udhar payment tile to POS
            this.addUdharPaymentTile();

            console.log('✅ POS integration setup completed');

        } catch (error) {
            console.error('❌ Failed to setup POS integration:', error);
        }
    }

    /**
     * Add Udhar payment tile to POS
     */
    addUdharPaymentTile() {
        try {
            // Wait for POS to be loaded and add Udhar tile
            const checkPOSInterval = setInterval(() => {
                const paymentMethodsContainer = document.querySelector('.payment-methods') || 
                                               document.querySelector('.payment-tiles') ||
                                               document.querySelector('.payment-options');
                
                if (paymentMethodsContainer && !document.querySelector('.udhar-payment-tile')) {
                    const udharTile = document.createElement('div');
                    udharTile.className = 'payment-tile udhar-payment-tile';
                    udharTile.innerHTML = `
                        <div class="payment-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="payment-label">Udhar</div>
                        <div class="payment-subtitle">Credit</div>
                    `;
                    
                    udharTile.onclick = () => {
                        this.handleUdharPaymentSelection();
                    };
                    
                    paymentMethodsContainer.appendChild(udharTile);
                    console.log('✅ Udhar payment tile added to POS');
                    clearInterval(checkPOSInterval);
                }
            }, 1000);

            // Clear interval after 30 seconds if POS not found
            setTimeout(() => {
                clearInterval(checkPOSInterval);
            }, 30000);

        } catch (error) {
            console.error('❌ Failed to add Udhar payment tile:', error);
        }
    }

    /**
     * Handle Udhar payment selection
     */
    handleUdharPaymentSelection() {
        try {
            // Get current cart/order data
            const cartData = this.getCurrentCartData();
            
            if (!cartData || !cartData.total || cartData.total <= 0) {
                alert('Please add items to cart before selecting payment method');
                return;
            }

            this.showUdharCustomerModal(cartData);

        } catch (error) {
            console.error('❌ Failed to handle Udhar payment selection:', error);
            alert('Failed to process Udhar payment. Please try again.');
        }
    }

    /**
     * Get current cart data from POS
     */
    getCurrentCartData() {
        try {
            // Try to get cart data from various possible sources
            if (window.app && window.app.currentCart) {
                return window.app.currentCart;
            }
            
            if (window.cart) {
                return window.cart;
            }
            
            // Try to extract from DOM
            const totalElement = document.querySelector('.cart-total') || 
                                document.querySelector('.order-total') ||
                                document.querySelector('#totalAmount');
            
            if (totalElement) {
                const totalText = totalElement.textContent || totalElement.value || '';
                const total = parseFloat(totalText.replace(/[^\d.]/g, '')) || 0;
                
                return {
                    total: total,
                    items: this.extractCartItemsFromDOM(),
                    customerCount: this.extractCustomerCount(),
                    serviceType: this.extractServiceType()
                };
            }

            return null;

        } catch (error) {
            console.error('❌ Failed to get current cart data:', error);
            return null;
        }
    }

    /**
     * Extract cart items from DOM
     */
    extractCartItemsFromDOM() {
        try {
            const cartItems = [];
            const itemElements = document.querySelectorAll('.cart-item') || 
                               document.querySelectorAll('.order-item');
            
            itemElements.forEach(element => {
                const name = element.querySelector('.item-name')?.textContent || 'Unknown Item';
                const price = parseFloat(element.querySelector('.item-price')?.textContent?.replace(/[^\d.]/g, '') || '0');
                const quantity = parseInt(element.querySelector('.item-quantity')?.textContent || '1');
                
                cartItems.push({
                    name: name,
                    price: price,
                    quantity: quantity
                });
            });

            return cartItems;

        } catch (error) {
            console.error('❌ Failed to extract cart items:', error);
            return [];
        }
    }

    /**
     * Extract customer count
     */
    extractCustomerCount() {
        try {
            const customerCountElement = document.querySelector('#customerCount') ||
                                       document.querySelector('.customer-count') ||
                                       document.querySelector('[name="customerCount"]');
            
            return parseInt(customerCountElement?.value || customerCountElement?.textContent || '1');

        } catch (error) {
            return 1;
        }
    }

    /**
     * Extract service type
     */
    extractServiceType() {
        try {
            const serviceTypeElement = document.querySelector('input[name="serviceType"]:checked') ||
                                     document.querySelector('.service-type.active');
            
            return serviceTypeElement?.value || serviceTypeElement?.dataset?.type || 'dine_in';

        } catch (error) {
            return 'dine_in';
        }
    }

    /**
     * Show Udhar customer selection modal
     */
    showUdharCustomerModal(cartData) {
        try {
            const existingCustomers = JSON.parse(localStorage.getItem('udhars') || '[]');
            
            const modal = document.createElement('div');
            modal.className = 'modal-overlay udhar-customer-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-handshake"></i> Udhar Payment - Select Customer</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="order-summary">
                            <h3>Order Summary</h3>
                            <div class="summary-details">
                                <div class="summary-item">
                                    <span>Total Amount:</span>
                                    <span class="amount">PKR ${cartData.total.toLocaleString()}</span>
                                </div>
                                <div class="summary-item">
                                    <span>Items:</span>
                                    <span>${cartData.items?.length || 0} items</span>
                                </div>
                                <div class="summary-item">
                                    <span>Service:</span>
                                    <span>${cartData.serviceType === 'dine_in' ? 'Dine In' : 'Take Away'}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="customer-selection">
                            <h3>Select Customer</h3>
                            
                            <div class="customer-options">
                                <div class="customer-option">
                                    <input type="radio" id="existingCustomer" name="customerOption" value="existing" 
                                           ${existingCustomers.length > 0 ? 'checked' : 'disabled'}>
                                    <label for="existingCustomer">Select Existing Customer</label>
                                </div>
                                <div class="customer-option">
                                    <input type="radio" id="newCustomer" name="customerOption" value="new" 
                                           ${existingCustomers.length === 0 ? 'checked' : ''}>
                                    <label for="newCustomer">Add New Customer</label>
                                </div>
                            </div>
                            
                            <div class="customer-details">
                                <!-- Existing Customer Selection -->
                                <div id="existingCustomerSection" class="customer-section" 
                                     style="display: ${existingCustomers.length > 0 ? 'block' : 'none'}">
                                    <h4>Choose Existing Customer</h4>
                                    <select id="existingCustomerSelect" class="form-control">
                                        <option value="">Select a customer...</option>
                                        ${existingCustomers.map(customer => `
                                            <option value="${customer.id}">
                                                ${customer.customerName} - Balance: PKR ${(customer.remainingAmount || 0).toLocaleString()}
                                            </option>
                                        `).join('')}
                                    </select>
                                    <div id="selectedCustomerInfo" class="customer-info"></div>
                                </div>
                                
                                <!-- New Customer Form -->
                                <div id="newCustomerSection" class="customer-section" 
                                     style="display: ${existingCustomers.length === 0 ? 'block' : 'none'}">
                                    <h4>Add New Customer</h4>
                                    <div class="form-group">
                                        <label>Customer Name *</label>
                                        <input type="text" id="newCustomerName" placeholder="Enter customer name" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Phone Number</label>
                                        <input type="tel" id="newCustomerPhone" placeholder="Enter phone number (optional)">
                                    </div>
                                    <div class="form-group">
                                        <label>Address</label>
                                        <input type="text" id="newCustomerAddress" placeholder="Enter address (optional)">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button onclick="window.udharPOSIntegration.processUdharOrder()" class="btn btn-warning">
                            <i class="fas fa-handshake"></i>
                            Process Udhar Order
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Set up event listeners
            this.setupCustomerModalEventListeners();

        } catch (error) {
            console.error('❌ Failed to show Udhar customer modal:', error);
        }
    }

    /**
     * Set up customer modal event listeners
     */
    setupCustomerModalEventListeners() {
        try {
            // Customer option change
            const customerOptions = document.querySelectorAll('input[name="customerOption"]');
            customerOptions.forEach(option => {
                option.addEventListener('change', (e) => {
                    const existingSection = document.getElementById('existingCustomerSection');
                    const newSection = document.getElementById('newCustomerSection');
                    
                    if (e.target.value === 'existing') {
                        existingSection.style.display = 'block';
                        newSection.style.display = 'none';
                    } else {
                        existingSection.style.display = 'none';
                        newSection.style.display = 'block';
                    }
                });
            });

            // Existing customer selection
            const customerSelect = document.getElementById('existingCustomerSelect');
            if (customerSelect) {
                customerSelect.addEventListener('change', (e) => {
                    this.showSelectedCustomerInfo(e.target.value);
                });
            }

        } catch (error) {
            console.error('❌ Failed to setup customer modal event listeners:', error);
        }
    }

    /**
     * Show selected customer info
     */
    showSelectedCustomerInfo(customerId) {
        try {
            const infoElement = document.getElementById('selectedCustomerInfo');
            if (!infoElement || !customerId) {
                if (infoElement) infoElement.innerHTML = '';
                return;
            }

            const customers = JSON.parse(localStorage.getItem('udhars') || '[]');
            const customer = customers.find(c => c.id === customerId);
            
            if (!customer) {
                infoElement.innerHTML = '';
                return;
            }

            infoElement.innerHTML = `
                <div class="customer-info-card">
                    <h5>${customer.customerName}</h5>
                    <div class="info-item">
                        <span>Phone:</span>
                        <span>${customer.phoneNumber || 'Not provided'}</span>
                    </div>
                    <div class="info-item">
                        <span>Address:</span>
                        <span>${customer.address || 'Not provided'}</span>
                    </div>
                    <div class="info-item">
                        <span>Current Balance:</span>
                        <span class="balance ${(customer.remainingAmount || 0) > 0 ? 'positive' : 'zero'}">
                            PKR ${(customer.remainingAmount || 0).toLocaleString()}
                        </span>
                    </div>
                    <div class="info-item">
                        <span>Total Udhar:</span>
                        <span>PKR ${(customer.totalAmount || 0).toLocaleString()}</span>
                    </div>
                </div>
            `;

        } catch (error) {
            console.error('❌ Failed to show selected customer info:', error);
        }
    }

    /**
     * Process Udhar order
     */
    processUdharOrder() {
        try {
            const customerOption = document.querySelector('input[name="customerOption"]:checked')?.value;
            const cartData = this.getCurrentCartData();
            
            if (!cartData || cartData.total <= 0) {
                alert('Invalid order data');
                return;
            }

            let customerId = null;
            let customerName = '';

            if (customerOption === 'existing') {
                customerId = document.getElementById('existingCustomerSelect')?.value;
                if (!customerId) {
                    alert('Please select a customer');
                    return;
                }
                
                const customers = JSON.parse(localStorage.getItem('udhars') || '[]');
                const customer = customers.find(c => c.id === customerId);
                customerName = customer?.customerName || 'Unknown Customer';
                
            } else {
                customerName = document.getElementById('newCustomerName')?.value?.trim();
                if (!customerName) {
                    alert('Please enter customer name');
                    return;
                }
                
                const phone = document.getElementById('newCustomerPhone')?.value?.trim() || '';
                const address = document.getElementById('newCustomerAddress')?.value?.trim() || '';
                
                // Create new customer
                customerId = this.createNewUdharCustomer(customerName, phone, address);
            }

            // Process the Udhar order
            const success = this.addUdharOrder(customerId, customerName, cartData);
            
            if (success) {
                // Close modal
                document.querySelector('.udhar-customer-modal')?.remove();
                
                // Show success message
                if (window.app && typeof window.app.showNotification === 'function') {
                    window.app.showNotification(
                        `Udhar order of PKR ${cartData.total.toLocaleString()} added for ${customerName}`, 
                        'success'
                    );
                }
                
                // Clear cart and refresh POS
                this.clearCartAndRefreshPOS();
            }

        } catch (error) {
            console.error('❌ Failed to process Udhar order:', error);
            alert('Failed to process Udhar order. Please try again.');
        }
    }

    /**
     * Create new Udhar customer
     */
    createNewUdharCustomer(name, phone, address) {
        try {
            const customerId = 'udhar_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const timestamp = new Date().toISOString();

            const newCustomer = {
                id: customerId,
                customerName: name,
                phoneNumber: phone,
                address: address,
                totalAmount: 0,
                paidAmount: 0,
                remainingAmount: 0,
                transactions: [],
                createdAt: timestamp,
                updatedAt: timestamp
            };

            const customers = JSON.parse(localStorage.getItem('udhars') || '[]');
            customers.push(newCustomer);
            localStorage.setItem('udhars', JSON.stringify(customers));

            console.log(`✅ New Udhar customer created: ${name}`);
            return customerId;

        } catch (error) {
            console.error('❌ Failed to create new Udhar customer:', error);
            return null;
        }
    }

    /**
     * Add Udhar order
     */
    addUdharOrder(customerId, customerName, cartData) {
        try {
            const timestamp = new Date().toISOString();
            const today = new Date().toISOString().split('T')[0];

            // Create order entry
            const orderEntry = {
                id: 'udhar_order_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                order_number: 'UDHAR-' + Date.now(),
                customer_name: customerName,
                customer_count: cartData.customerCount || 1,
                service_type: cartData.serviceType || 'dine_in',
                items: cartData.items || [],
                total_amount: cartData.total,
                payment_method: 'udhar',
                status: 'completed',
                created_at: timestamp,
                createdAt: timestamp,
                udhar_customer_id: customerId
            };

            // Add to orders
            const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
            orders.push(orderEntry);
            localStorage.setItem('restaurantOrders', JSON.stringify(orders));

            // Update Udhar customer record
            const customers = JSON.parse(localStorage.getItem('udhars') || '[]');
            const customer = customers.find(c => c.id === customerId);

            if (customer) {
                // Add transaction to customer
                const transaction = {
                    id: 'trans_' + Date.now(),
                    type: 'order',
                    amount: cartData.total,
                    description: `Order #${orderEntry.order_number}`,
                    date: today,
                    timestamp: timestamp,
                    orderId: orderEntry.id
                };

                customer.transactions.push(transaction);
                customer.totalAmount = (customer.totalAmount || 0) + cartData.total;
                customer.remainingAmount = (customer.remainingAmount || 0) + cartData.total;
                customer.updatedAt = timestamp;

                localStorage.setItem('udhars', JSON.stringify(customers));
            }

            console.log(`✅ Udhar order added: PKR ${cartData.total} for ${customerName}`);
            return true;

        } catch (error) {
            console.error('❌ Failed to add Udhar order:', error);
            return false;
        }
    }

    /**
     * Clear cart and refresh POS
     */
    clearCartAndRefreshPOS() {
        try {
            // Try various methods to clear the cart
            if (window.app && typeof window.app.clearCart === 'function') {
                window.app.clearCart();
            }

            if (window.cart && typeof window.cart.clear === 'function') {
                window.cart.clear();
            }

            // Clear cart from DOM
            const cartContainer = document.querySelector('.cart-items') ||
                                document.querySelector('.order-items');
            if (cartContainer) {
                cartContainer.innerHTML = '';
            }

            // Reset total
            const totalElements = document.querySelectorAll('.cart-total, .order-total, #totalAmount');
            totalElements.forEach(element => {
                if (element.tagName === 'INPUT') {
                    element.value = '0';
                } else {
                    element.textContent = 'PKR 0';
                }
            });

            // Reset customer count
            const customerCountElement = document.querySelector('#customerCount');
            if (customerCountElement) {
                customerCountElement.value = '1';
            }

            // Refresh dashboard if needed
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                setTimeout(() => {
                    window.app.updateDashboardStats();
                }, 500);
            }

            console.log('✅ Cart cleared and POS refreshed');

        } catch (error) {
            console.error('❌ Failed to clear cart and refresh POS:', error);
        }
    }

    /**
     * Process Udhar payment (called from POS payment processing)
     */
    processUdharPayment(orderData) {
        try {
            // This method is called when Udhar is selected as payment method
            // It should trigger the customer selection modal
            this.showUdharCustomerModal(orderData);

            // Return false to prevent default payment processing
            // The actual processing will happen in processUdharOrder()
            return false;

        } catch (error) {
            console.error('❌ Failed to process Udhar payment:', error);
            return false;
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize Udhar POS integration
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.udharPOSIntegration = new ZaiqaUdharPOSIntegration();
    }, 2000);
});

// Export for global use
window.ZaiqaUdharPOSIntegration = ZaiqaUdharPOSIntegration;
