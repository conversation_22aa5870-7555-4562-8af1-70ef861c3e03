/**
 * Unified Day-End Process - Consolidates all business day transition tasks
 * Merges "End Business Day" and "Finalize Day" into a single comprehensive process
 */

class ZaiqaUnifiedDayEnd {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize the unified day-end system
     */
    init() {
        try {
            console.log('🌅 Initializing Unified Day-End Process v' + this.version);
            
            // Override existing day-end functions
            this.overrideDayEndFunctions();
            
            this.initialized = true;
            console.log('✅ Unified Day-End Process initialized successfully');
            
        } catch (error) {
            console.error('❌ Unified Day-End initialization failed:', error);
        }
    }

    /**
     * Override existing day-end functions to use unified process
     */
    overrideDayEndFunctions() {
        try {
            // Override business day manager transition
            if (window.businessDayManager && typeof window.businessDayManager.executeTransition === 'function') {
                window.businessDayManager.originalExecuteTransition = window.businessDayManager.executeTransition;
                window.businessDayManager.executeTransition = () => {
                    this.showUnifiedDayEndModal();
                };
            }

            // Override any finalize day functions
            if (window.app && typeof window.app.finalizeDay === 'function') {
                window.app.originalFinalizeDay = window.app.finalizeDay;
                window.app.finalizeDay = () => {
                    this.showUnifiedDayEndModal();
                };
            }

            // Override cash manager day-end if it exists
            if (window.cashManager && typeof window.cashManager.showDayEndExpensePrompts === 'function') {
                window.cashManager.originalShowDayEndExpensePrompts = window.cashManager.showDayEndExpensePrompts;
                window.cashManager.showDayEndExpensePrompts = () => {
                    this.showUnifiedDayEndModal();
                };
            }

        } catch (error) {
            console.error('❌ Failed to override day-end functions:', error);
        }
    }

    /**
     * Show unified day-end modal
     */
    showUnifiedDayEndModal() {
        try {
            const currentDate = new Date().toISOString().split('T')[0];
            const lastBusinessDate = localStorage.getItem('lastBusinessDate') || currentDate;
            
            // Get auto expense configuration
            const autoExpenseConfig = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const enabledAutoExpenses = autoExpenseConfig.autoExpenses ? 
                autoExpenseConfig.autoExpenses.filter(exp => exp.enabled && exp.amount > 0) : [];

            const modal = document.createElement('div');
            modal.className = 'modal-overlay unified-day-end-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-calendar-check"></i> End Business Day - Complete Process</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="day-transition-info">
                            <div class="info-section">
                                <h3>Business Day Transition</h3>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <label>Current Business Date:</label>
                                        <span class="current-date">${lastBusinessDate}</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Transition to Date:</label>
                                        <input type="date" id="transitionDate" value="${currentDate}" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-end-sections">
                            <!-- Auto Expenses Section -->
                            <div class="day-end-section">
                                <h3><i class="fas fa-cog"></i> Auto Expenses Deduction</h3>
                                <div class="section-content">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="processAutoExpenses" ${enabledAutoExpenses.length > 0 ? 'checked' : ''}>
                                        <span>Process configured auto expenses (${enabledAutoExpenses.length} items)</span>
                                    </label>
                                    
                                    ${enabledAutoExpenses.length > 0 ? `
                                        <div class="auto-expenses-preview">
                                            <h4>Auto Expenses to be deducted:</h4>
                                            <div class="expenses-list">
                                                ${enabledAutoExpenses.map(exp => `
                                                    <div class="expense-preview-item">
                                                        <span class="expense-name">${exp.name} (${exp.nameUrdu})</span>
                                                        <span class="expense-amount">PKR ${exp.amount.toLocaleString()}</span>
                                                    </div>
                                                `).join('')}
                                            </div>
                                            <div class="total-auto-expenses">
                                                <strong>Total Auto Expenses: PKR ${enabledAutoExpenses.reduce((sum, exp) => sum + exp.amount, 0).toLocaleString()}</strong>
                                            </div>
                                        </div>
                                    ` : '<p class="no-auto-expenses">No auto expenses configured</p>'}
                                </div>
                            </div>
                            
                            <!-- Staff Wages Section -->
                            <div class="day-end-section">
                                <h3><i class="fas fa-users"></i> Staff Wages Processing</h3>
                                <div class="section-content">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="processStaffWages" checked>
                                        <span>Process pending staff wages for present staff</span>
                                    </label>
                                    <div id="staffWagesPreview" class="staff-wages-preview">
                                        ${this.renderStaffWagesPreview()}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Data Management Section -->
                            <div class="day-end-section">
                                <h3><i class="fas fa-database"></i> Data Management</h3>
                                <div class="section-content">
                                    <div class="data-options">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="createBackup" checked>
                                            <span>Create data backup before transition</span>
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="generateReport" checked>
                                            <span>Generate end-of-day report</span>
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="resetCashRegister">
                                            <span>Reset cash register for new day</span>
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="archiveOrders">
                                            <span>Archive completed orders</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-end-summary">
                            <h3>Process Summary</h3>
                            <div id="processSummary" class="summary-content">
                                <p>Select options above to see summary</p>
                            </div>
                        </div>
                        
                        <div class="warning-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Important:</strong> This action will complete the business day transition. 
                            Make sure all today's transactions are complete before proceeding.
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button onclick="window.unifiedDayEnd.executeUnifiedDayEnd()" class="btn btn-warning">
                            <i class="fas fa-calendar-check"></i>
                            Complete Business Day
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Set up event listeners for dynamic updates
            this.setupDayEndEventListeners();

        } catch (error) {
            console.error('❌ Failed to show unified day-end modal:', error);
        }
    }

    /**
     * Render staff wages preview
     */
    renderStaffWagesPreview() {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const activeStaff = staff.filter(s => s.isActive);
            const today = new Date().toISOString().split('T')[0];
            
            const presentStaff = activeStaff.filter(s => s.lastAttendanceDate === today);
            const totalWages = presentStaff.reduce((sum, s) => sum + (s.dailyWage || 0), 0);

            if (presentStaff.length === 0) {
                return '<p class="no-staff-wages">No staff marked present today</p>';
            }

            return `
                <div class="staff-wages-list">
                    ${presentStaff.map(staff => `
                        <div class="staff-wage-item">
                            <span class="staff-name">${staff.name}</span>
                            <span class="staff-wage">PKR ${(staff.dailyWage || 0).toLocaleString()}</span>
                        </div>
                    `).join('')}
                </div>
                <div class="total-staff-wages">
                    <strong>Total Staff Wages: PKR ${totalWages.toLocaleString()}</strong>
                </div>
            `;

        } catch (error) {
            console.error('❌ Failed to render staff wages preview:', error);
            return '<p class="error">Error loading staff wages</p>';
        }
    }

    /**
     * Set up event listeners for dynamic updates
     */
    setupDayEndEventListeners() {
        try {
            // Update summary when checkboxes change
            const checkboxes = document.querySelectorAll('.unified-day-end-modal input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    this.updateProcessSummary();
                });
            });

            // Initial summary update
            setTimeout(() => {
                this.updateProcessSummary();
            }, 100);

        } catch (error) {
            console.error('❌ Failed to setup day-end event listeners:', error);
        }
    }

    /**
     * Update process summary
     */
    updateProcessSummary() {
        try {
            const summaryElement = document.getElementById('processSummary');
            if (!summaryElement) return;

            const processAutoExpenses = document.getElementById('processAutoExpenses')?.checked;
            const processStaffWages = document.getElementById('processStaffWages')?.checked;
            const createBackup = document.getElementById('createBackup')?.checked;
            const generateReport = document.getElementById('generateReport')?.checked;

            let totalDeductions = 0;
            const actions = [];

            if (processAutoExpenses) {
                const autoExpenseConfig = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
                const enabledAutoExpenses = autoExpenseConfig.autoExpenses ? 
                    autoExpenseConfig.autoExpenses.filter(exp => exp.enabled && exp.amount > 0) : [];
                const autoTotal = enabledAutoExpenses.reduce((sum, exp) => sum + exp.amount, 0);
                totalDeductions += autoTotal;
                actions.push(`Process ${enabledAutoExpenses.length} auto expenses (PKR ${autoTotal.toLocaleString()})`);
            }

            if (processStaffWages) {
                const staff = JSON.parse(localStorage.getItem('staff') || '[]');
                const today = new Date().toISOString().split('T')[0];
                const presentStaff = staff.filter(s => s.isActive && s.lastAttendanceDate === today);
                const wagesTotal = presentStaff.reduce((sum, s) => sum + (s.dailyWage || 0), 0);
                totalDeductions += wagesTotal;
                actions.push(`Process ${presentStaff.length} staff wages (PKR ${wagesTotal.toLocaleString()})`);
            }

            if (createBackup) {
                actions.push('Create data backup');
            }

            if (generateReport) {
                actions.push('Generate end-of-day report');
            }

            summaryElement.innerHTML = `
                <div class="summary-actions">
                    ${actions.length > 0 ? `
                        <h4>Actions to be performed:</h4>
                        <ul>
                            ${actions.map(action => `<li>${action}</li>`).join('')}
                        </ul>
                    ` : '<p>No actions selected</p>'}
                </div>
                ${totalDeductions > 0 ? `
                    <div class="summary-totals">
                        <div class="total-deductions">
                            <strong>Total Cash Deductions: PKR ${totalDeductions.toLocaleString()}</strong>
                        </div>
                    </div>
                ` : ''}
            `;

        } catch (error) {
            console.error('❌ Failed to update process summary:', error);
        }
    }

    /**
     * Execute unified day-end process
     */
    executeUnifiedDayEnd() {
        try {
            const transitionDate = document.getElementById('transitionDate')?.value;
            const processAutoExpenses = document.getElementById('processAutoExpenses')?.checked;
            const processStaffWages = document.getElementById('processStaffWages')?.checked;
            const createBackup = document.getElementById('createBackup')?.checked;
            const generateReport = document.getElementById('generateReport')?.checked;
            const resetCashRegister = document.getElementById('resetCashRegister')?.checked;
            const archiveOrders = document.getElementById('archiveOrders')?.checked;

            if (!transitionDate) {
                alert('Please select a transition date.');
                return;
            }

            // Show confirmation
            const confirmed = confirm(`Are you sure you want to complete the business day and transition to ${transitionDate}?`);
            if (!confirmed) {
                return;
            }

            console.log('🌅 Executing unified day-end process...');

            // Execute selected processes
            if (createBackup) {
                this.createDayEndBackup();
            }

            if (processAutoExpenses) {
                this.processAutoExpenses();
            }

            if (processStaffWages) {
                this.processStaffWages();
            }

            if (generateReport) {
                this.generateEndOfDayReport();
            }

            if (resetCashRegister) {
                this.resetCashRegister(transitionDate);
            }

            if (archiveOrders) {
                this.archiveCompletedOrders();
            }

            // Update business date
            localStorage.setItem('lastBusinessDate', transitionDate);

            // Close modal
            document.querySelector('.unified-day-end-modal')?.remove();

            // Show success notification
            this.showSuccessNotification(transitionDate);

            // Refresh the system
            this.refreshSystemAfterDayEnd();

            console.log('✅ Unified day-end process completed successfully');

        } catch (error) {
            console.error('❌ Failed to execute unified day-end:', error);
            alert('Failed to complete day-end process. Please try again.');
        }
    }

    /**
     * Create day-end backup
     */
    createDayEndBackup() {
        try {
            const backupData = {
                date: new Date().toISOString(),
                businessDate: localStorage.getItem('lastBusinessDate'),
                orders: JSON.parse(localStorage.getItem('restaurantOrders') || '[]'),
                expenses: JSON.parse(localStorage.getItem('expenses') || '[]'),
                cashRegister: JSON.parse(localStorage.getItem('cashRegister') || '[]'),
                udhars: JSON.parse(localStorage.getItem('udhars') || '[]'),
                inventory: JSON.parse(localStorage.getItem('inventory') || '[]'),
                staff: JSON.parse(localStorage.getItem('staff') || '[]'),
                cashTransactions: JSON.parse(localStorage.getItem('cashTransactions') || '[]')
            };

            const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-unified-backup-${backupData.businessDate}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log('✅ Unified day-end backup created');

        } catch (error) {
            console.error('❌ Failed to create day-end backup:', error);
        }
    }

    /**
     * Process auto expenses
     */
    processAutoExpenses() {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const enabledExpenses = config.autoExpenses ?
                config.autoExpenses.filter(exp => exp.enabled && exp.amount > 0) : [];

            if (enabledExpenses.length === 0) return;

            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();
            let totalDeducted = 0;

            enabledExpenses.forEach(expense => {
                const expenseEntry = {
                    id: 'dayend_auto_' + expense.id + '_' + Date.now(),
                    description: `${expense.name} (Day-End Auto Deduction)`,
                    amount: expense.amount,
                    category: 'Auto Expenses',
                    date: today,
                    created_at: timestamp,
                    type: 'day_end_auto_expense',
                    autoExpenseId: expense.id
                };

                // Add to expenses
                const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                expenses.push(expenseEntry);
                localStorage.setItem('expenses', JSON.stringify(expenses));

                totalDeducted += expense.amount;
            });

            // Deduct from cash in hand
            if (totalDeducted > 0) {
                const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
                const newCash = currentCash - totalDeducted;
                localStorage.setItem('currentCashInHand', newCash.toString());
            }

            console.log(`✅ Auto expenses processed: PKR ${totalDeducted}`);

        } catch (error) {
            console.error('❌ Failed to process auto expenses:', error);
        }
    }

    /**
     * Process staff wages
     */
    processStaffWages() {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            const presentStaff = staff.filter(s => s.isActive && s.lastAttendanceDate === today);
            let totalWages = 0;

            presentStaff.forEach(staffMember => {
                const wageAmount = staffMember.dailyWage || 0;
                if (wageAmount > 0) {
                    const expenseEntry = {
                        id: 'dayend_wage_' + staffMember.id + '_' + Date.now(),
                        description: `${staffMember.name} - Daily Wage (Day-End Processing)`,
                        amount: wageAmount,
                        category: 'Staff Salaries',
                        date: today,
                        created_at: timestamp,
                        type: 'day_end_staff_wage',
                        staffId: staffMember.id
                    };

                    // Add to expenses
                    const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                    expenses.push(expenseEntry);
                    localStorage.setItem('expenses', JSON.stringify(expenses));

                    totalWages += wageAmount;
                }
            });

            // Deduct from cash in hand
            if (totalWages > 0) {
                const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
                const newCash = currentCash - totalWages;
                localStorage.setItem('currentCashInHand', newCash.toString());
            }

            console.log(`✅ Staff wages processed: PKR ${totalWages}`);

        } catch (error) {
            console.error('❌ Failed to process staff wages:', error);
        }
    }

    /**
     * Generate end-of-day report
     */
    generateEndOfDayReport() {
        try {
            if (window.zaiqaReports && typeof window.zaiqaReports.generateDailyReport === 'function') {
                window.zaiqaReports.generateDailyReport();
            }
            console.log('✅ End-of-day report generated');

        } catch (error) {
            console.error('❌ Failed to generate end-of-day report:', error);
        }
    }

    /**
     * Reset cash register for new day
     */
    resetCashRegister(newDate) {
        try {
            const cashRegister = JSON.parse(localStorage.getItem('cashRegister') || '[]');

            const newEntry = {
                date: newDate,
                morningBalance: 0,
                eveningBalance: 0,
                totalSales: 0,
                totalExpenses: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            cashRegister.push(newEntry);
            localStorage.setItem('cashRegister', JSON.stringify(cashRegister));

            console.log('✅ Cash register reset for new day');

        } catch (error) {
            console.error('❌ Failed to reset cash register:', error);
        }
    }

    /**
     * Archive completed orders
     */
    archiveCompletedOrders() {
        try {
            const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
            const archivedOrders = JSON.parse(localStorage.getItem('archivedOrders') || '[]');

            const completedOrders = orders.filter(order => order.status === 'completed');
            const activeOrders = orders.filter(order => order.status !== 'completed');

            archivedOrders.push(...completedOrders);

            localStorage.setItem('restaurantOrders', JSON.stringify(activeOrders));
            localStorage.setItem('archivedOrders', JSON.stringify(archivedOrders));

            console.log(`✅ Archived ${completedOrders.length} completed orders`);

        } catch (error) {
            console.error('❌ Failed to archive orders:', error);
        }
    }

    /**
     * Show success notification
     */
    showSuccessNotification(newDate) {
        try {
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(
                    `Business day successfully completed and transitioned to ${newDate}!`,
                    'success'
                );
            } else {
                alert(`Business day successfully completed and transitioned to ${newDate}!`);
            }

        } catch (error) {
            console.error('❌ Failed to show success notification:', error);
        }
    }

    /**
     * Refresh system after day-end
     */
    refreshSystemAfterDayEnd() {
        try {
            // Refresh dashboard
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                setTimeout(() => {
                    window.app.updateDashboardStats();
                }, 500);
            }

            // Refresh reports if visible
            const reportsPage = document.getElementById('reportsPage');
            if (reportsPage && reportsPage.style.display !== 'none') {
                if (window.zaiqaReports && typeof window.zaiqaReports.render === 'function') {
                    setTimeout(() => {
                        window.zaiqaReports.render(reportsPage);
                    }, 1000);
                }
            }

        } catch (error) {
            console.error('❌ Failed to refresh system after day-end:', error);
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize the unified day-end system
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.unifiedDayEnd = new ZaiqaUnifiedDayEnd();
    }, 3000);
});

// Export for global use
window.ZaiqaUnifiedDayEnd = ZaiqaUnifiedDayEnd;
