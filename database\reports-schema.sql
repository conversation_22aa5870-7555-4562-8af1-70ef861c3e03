-- =====================================================
-- COMPREHENSIVE RESTAURANT REPORTS DATABASE SCHEMA
-- Production-Ready Schema for Advanced Analytics
-- =====================================================

-- Business Days Management
CREATE TABLE business_days (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    business_date DATE UNIQUE NOT NULL,
    opening_balance DECIMAL(12,2) DEFAULT 0.00,
    closing_balance DECIMAL(12,2) DEFAULT 0.00,
    expected_closing DECIMAL(12,2) DEFAULT 0.00,
    cash_difference DECIMAL(12,2) DEFAULT 0.00,
    total_sales DECIMAL(12,2) DEFAULT 0.00,
    total_expenses DECIMAL(12,2) DEFAULT 0.00,
    net_profit DECIMAL(12,2) DEFAULT 0.00,
    order_count INT DEFAULT 0,
    is_finalized BOOLEAN DEFAULT FALSE,
    finalized_at TIMESTAMP NULL,
    finalized_by BIGINT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_business_date (business_date),
    INDEX idx_finalized (is_finalized),
    INDEX idx_created_at (created_at)
);

-- Owner Withdrawals Tracking
CREATE TABLE owner_withdrawals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    business_day_id BIGINT,
    amount DECIMAL(12,2) NOT NULL,
    description TEXT,
    withdrawal_type ENUM('cash', 'bank_transfer', 'other') DEFAULT 'cash',
    reference_number VARCHAR(100),
    withdrawn_by BIGINT,
    approved_by BIGINT,
    withdrawal_date DATE NOT NULL,
    withdrawal_time TIME NOT NULL,
    status ENUM('pending', 'approved', 'completed', 'cancelled') DEFAULT 'completed',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_withdrawal_date (withdrawal_date),
    INDEX idx_status (status),
    INDEX idx_amount (amount)
);

-- Enhanced Orders for Analytics
CREATE TABLE orders_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    business_day_id BIGINT,
    order_number VARCHAR(50) NOT NULL,
    customer_name VARCHAR(255),
    customer_phone VARCHAR(20),
    order_type ENUM('dine_in', 'takeaway', 'delivery') NOT NULL,
    table_number VARCHAR(10),
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'digital', 'credit') NOT NULL,
    order_status ENUM('pending', 'preparing', 'ready', 'completed', 'cancelled') NOT NULL,
    served_by BIGINT,
    order_date DATE NOT NULL,
    order_time TIME NOT NULL,
    completion_time TIME,
    preparation_duration INT, -- minutes
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_order_date (order_date),
    INDEX idx_order_status (order_status),
    INDEX idx_order_type (order_type),
    INDEX idx_total_amount (total_amount),
    INDEX idx_payment_method (payment_method)
);

-- Order Items for Detailed Analytics
CREATE TABLE order_items_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_analytics_id BIGINT NOT NULL,
    menu_item_id BIGINT NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(8,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    cost_price DECIMAL(8,2) DEFAULT 0.00,
    profit_margin DECIMAL(10,2) GENERATED ALWAYS AS (total_price - (cost_price * quantity)) STORED,
    profit_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN (cost_price * quantity) > 0 
            THEN ((total_price - (cost_price * quantity)) / (cost_price * quantity)) * 100 
            ELSE 0 
        END
    ) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_analytics_id) REFERENCES orders_analytics(id) ON DELETE CASCADE,
    INDEX idx_menu_item (menu_item_id),
    INDEX idx_category (category),
    INDEX idx_profit_margin (profit_margin),
    INDEX idx_created_at (created_at)
);

-- Comprehensive Expenses Tracking
CREATE TABLE expenses_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    business_day_id BIGINT,
    expense_category ENUM('ingredients', 'staff_wages', 'utilities', 'rent', 'maintenance', 'marketing', 'supplies', 'other') NOT NULL,
    subcategory VARCHAR(100),
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'card', 'cheque') NOT NULL,
    vendor_name VARCHAR(255),
    invoice_number VARCHAR(100),
    expense_date DATE NOT NULL,
    expense_time TIME NOT NULL,
    recorded_by BIGINT,
    approved_by BIGINT,
    status ENUM('pending', 'approved', 'paid', 'cancelled') DEFAULT 'paid',
    receipt_url VARCHAR(500),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_expense_date (expense_date),
    INDEX idx_expense_category (expense_category),
    INDEX idx_amount (amount),
    INDEX idx_status (status)
);

-- Staff Performance & Wages
CREATE TABLE staff_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    staff_id BIGINT NOT NULL,
    business_day_id BIGINT,
    staff_name VARCHAR(255) NOT NULL,
    role VARCHAR(100) NOT NULL,
    shift_start TIME NOT NULL,
    shift_end TIME,
    hours_worked DECIMAL(4,2),
    hourly_rate DECIMAL(8,2) NOT NULL,
    total_wages DECIMAL(10,2) GENERATED ALWAYS AS (hours_worked * hourly_rate) STORED,
    orders_served INT DEFAULT 0,
    total_sales DECIMAL(10,2) DEFAULT 0.00,
    attendance_date DATE NOT NULL,
    status ENUM('present', 'absent', 'late', 'half_day') NOT NULL,
    overtime_hours DECIMAL(4,2) DEFAULT 0.00,
    overtime_rate DECIMAL(8,2) DEFAULT 0.00,
    bonus_amount DECIMAL(8,2) DEFAULT 0.00,
    deductions DECIMAL(8,2) DEFAULT 0.00,
    net_pay DECIMAL(10,2) GENERATED ALWAYS AS (
        (hours_worked * hourly_rate) + 
        (overtime_hours * overtime_rate) + 
        bonus_amount - deductions
    ) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_attendance_date (attendance_date),
    INDEX idx_staff_id (staff_id),
    INDEX idx_total_sales (total_sales)
);

-- Inventory Usage & Waste Tracking
CREATE TABLE inventory_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    inventory_item_id BIGINT NOT NULL,
    business_day_id BIGINT,
    item_name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    opening_stock DECIMAL(10,3) NOT NULL,
    stock_added DECIMAL(10,3) DEFAULT 0.000,
    stock_used DECIMAL(10,3) DEFAULT 0.000,
    stock_wasted DECIMAL(10,3) DEFAULT 0.000,
    closing_stock DECIMAL(10,3) NOT NULL,
    unit VARCHAR(50) NOT NULL,
    cost_per_unit DECIMAL(8,2) NOT NULL,
    total_cost_used DECIMAL(10,2) GENERATED ALWAYS AS (stock_used * cost_per_unit) STORED,
    waste_cost DECIMAL(10,2) GENERATED ALWAYS AS (stock_wasted * cost_per_unit) STORED,
    usage_date DATE NOT NULL,
    supplier VARCHAR(255),
    expiry_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_usage_date (usage_date),
    INDEX idx_category (category),
    INDEX idx_stock_used (stock_used),
    INDEX idx_waste_cost (waste_cost)
);

-- Cash Flow Tracking
CREATE TABLE cash_flow_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    business_day_id BIGINT,
    transaction_type ENUM('inflow', 'outflow') NOT NULL,
    category ENUM('sales', 'expense', 'withdrawal', 'deposit', 'refund', 'other') NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    description TEXT NOT NULL,
    reference_id BIGINT, -- Links to orders, expenses, withdrawals
    reference_type ENUM('order', 'expense', 'withdrawal', 'other'),
    payment_method ENUM('cash', 'card', 'digital', 'bank_transfer') NOT NULL,
    transaction_date DATE NOT NULL,
    transaction_time TIME NOT NULL,
    recorded_by BIGINT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_transaction_date (transaction_date),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_category (category),
    INDEX idx_amount (amount)
);

-- Peak Hours Analytics
CREATE TABLE peak_hours_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    business_day_id BIGINT,
    hour_slot TIME NOT NULL,
    order_count INT DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0.00,
    avg_order_value DECIMAL(8,2) DEFAULT 0.00,
    staff_count INT DEFAULT 0,
    table_turnover DECIMAL(4,2) DEFAULT 0.00,
    analysis_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (business_day_id) REFERENCES business_days(id) ON DELETE SET NULL,
    INDEX idx_analysis_date (analysis_date),
    INDEX idx_hour_slot (hour_slot),
    INDEX idx_order_count (order_count),
    INDEX idx_total_revenue (total_revenue)
);

-- User Roles & Permissions
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role ENUM('owner', 'manager', 'cashier', 'staff', 'viewer') NOT NULL,
    permissions JSON,
    can_view_reports BOOLEAN DEFAULT FALSE,
    can_export_data BOOLEAN DEFAULT FALSE,
    can_manage_business_day BOOLEAN DEFAULT FALSE,
    can_approve_withdrawals BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_role (role)
);

-- System Settings for Reports
CREATE TABLE reports_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    data_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_active (is_active)
);

-- Insert Default Settings
INSERT INTO reports_settings (setting_key, setting_value, data_type, description) VALUES
('currency_symbol', 'PKR', 'string', 'Currency symbol for reports'),
('business_hours_start', '09:00', 'string', 'Business opening time'),
('business_hours_end', '23:00', 'string', 'Business closing time'),
('tax_rate', '16.0', 'number', 'Tax rate percentage'),
('auto_refresh_interval', '30', 'number', 'Auto refresh interval in seconds'),
('export_formats', '["pdf", "excel", "csv"]', 'json', 'Available export formats'),
('default_date_range', '7', 'number', 'Default date range for reports in days'),
('low_stock_threshold', '10', 'number', 'Low stock alert threshold'),
('peak_hours_threshold', '5', 'number', 'Minimum orders to consider peak hour');
