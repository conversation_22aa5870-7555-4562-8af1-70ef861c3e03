<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal JavaScript Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔍 Minimal JavaScript Test</h1>
    <div id="results"></div>
    
    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Test 1: Basic JavaScript
        try {
            addResult('✅ Basic JavaScript working', 'success');
        } catch (error) {
            addResult('❌ Basic JavaScript failed: ' + error.message, 'error');
        }

        // Test 2: Try to load the app.js file
        addResult('🔄 Loading app.js file...', 'info');
        
        const script = document.createElement('script');
        script.src = 'assets/js/app.js?v=13.0&test=' + Date.now();
        
        script.onload = function() {
            addResult('✅ app.js loaded successfully', 'success');
            
            // Test 3: Check if RestaurantApp is defined
            if (typeof RestaurantApp !== 'undefined') {
                addResult('✅ RestaurantApp class is defined', 'success');
                
                // Test 4: Try to instantiate
                try {
                    const app = new RestaurantApp();
                    addResult('✅ RestaurantApp instantiated successfully', 'success');
                    
                    // Test 5: Check specific methods
                    const methods = ['applyDateFilter', 'resetDateFilter', 'addNewWithdrawal'];
                    let allMethodsExist = true;
                    
                    methods.forEach(method => {
                        if (typeof app[method] === 'function') {
                            addResult(`✅ Method ${method} exists`, 'success');
                        } else {
                            addResult(`❌ Method ${method} missing`, 'error');
                            allMethodsExist = false;
                        }
                    });
                    
                    if (allMethodsExist) {
                        addResult('🎉 All required methods are available!', 'success');
                        
                        // Test 6: Try calling a method
                        try {
                            // Create mock elements
                            const mockStart = document.createElement('input');
                            mockStart.id = 'reportStartDate';
                            mockStart.value = '2024-01-01';
                            mockStart.style.display = 'none';
                            document.body.appendChild(mockStart);
                            
                            const mockEnd = document.createElement('input');
                            mockEnd.id = 'reportEndDate';
                            mockEnd.value = '2024-01-31';
                            mockEnd.style.display = 'none';
                            document.body.appendChild(mockEnd);
                            
                            const mockContent = document.createElement('div');
                            mockContent.id = 'analyticsContent';
                            mockContent.style.display = 'none';
                            document.body.appendChild(mockContent);
                            
                            const mockPeriod = document.createElement('div');
                            mockPeriod.id = 'currentPeriodDisplay';
                            mockPeriod.style.display = 'none';
                            document.body.appendChild(mockPeriod);
                            
                            // Make app globally available
                            window.app = app;
                            
                            // Try calling the method
                            app.applyDateFilter();
                            addResult('✅ applyDateFilter method executed successfully', 'success');
                            
                        } catch (methodError) {
                            addResult('❌ Method execution failed: ' + methodError.message, 'error');
                        }
                    }
                    
                } catch (instError) {
                    addResult('❌ Failed to instantiate RestaurantApp: ' + instError.message, 'error');
                }
                
            } else {
                addResult('❌ RestaurantApp class not defined', 'error');
            }
        };
        
        script.onerror = function() {
            addResult('❌ Failed to load app.js - syntax error detected', 'error');
            
            // Try to get more specific error information
            fetch('assets/js/app.js?v=13.0')
                .then(response => response.text())
                .then(code => {
                    addResult('📄 File loaded via fetch, checking syntax...', 'info');
                    
                    try {
                        // Try to parse with Function constructor
                        new Function(code);
                        addResult('✅ Syntax check passed with Function constructor', 'success');
                    } catch (syntaxError) {
                        addResult('❌ Syntax error found: ' + syntaxError.message, 'error');
                        
                        // Try to find the line number
                        if (syntaxError.lineNumber) {
                            addResult(`📍 Error at line: ${syntaxError.lineNumber}`, 'error');
                        }
                    }
                })
                .catch(fetchError => {
                    addResult('❌ Failed to fetch file: ' + fetchError.message, 'error');
                });
        };
        
        document.head.appendChild(script);
        
        // Test 7: Check if buttons work after everything loads
        setTimeout(() => {
            addResult('🔄 Testing button functionality...', 'info');
            
            if (typeof window.app !== 'undefined') {
                // Test button onclick handlers
                try {
                    const testButton = document.createElement('button');
                    testButton.setAttribute('onclick', 'app.applyDateFilter()');
                    testButton.style.display = 'none';
                    document.body.appendChild(testButton);
                    
                    testButton.click();
                    addResult('✅ Button onclick handler works', 'success');
                    
                    testButton.remove();
                } catch (buttonError) {
                    addResult('❌ Button onclick failed: ' + buttonError.message, 'error');
                }
            } else {
                addResult('❌ App not available for button testing', 'error');
            }
        }, 2000);
    </script>
</body>
</html>
