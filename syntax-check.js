// Simple syntax checker for app.js
console.log('🔍 Starting syntax check...');

try {
    // Try to load and parse the JavaScript file
    fetch('assets/js/app.js?v=13.0')
        .then(response => response.text())
        .then(code => {
            console.log('📄 JavaScript file loaded successfully');
            console.log(`📊 File size: ${code.length} characters`);
            
            // Try to parse the code
            try {
                // Use Function constructor to check syntax
                new Function(code);
                console.log('✅ JavaScript syntax is valid');
                
                // Check for common issues
                const issues = [];
                
                // Check for unmatched brackets
                const openBrackets = (code.match(/\{/g) || []).length;
                const closeBrackets = (code.match(/\}/g) || []).length;
                if (openBrackets !== closeBrackets) {
                    issues.push(`Unmatched brackets: ${openBrackets} open, ${closeBrackets} close`);
                }
                
                // Check for unmatched parentheses
                const openParens = (code.match(/\(/g) || []).length;
                const closeParens = (code.match(/\)/g) || []).length;
                if (openParens !== closeParens) {
                    issues.push(`Unmatched parentheses: ${openParens} open, ${closeParens} close`);
                }
                
                // Check for template literal issues
                const backticks = (code.match(/`/g) || []).length;
                if (backticks % 2 !== 0) {
                    issues.push(`Unmatched template literals: ${backticks} backticks (should be even)`);
                }
                
                if (issues.length > 0) {
                    console.log('⚠️ Potential issues found:');
                    issues.forEach(issue => console.log(`  - ${issue}`));
                } else {
                    console.log('✅ No syntax issues detected');
                }
                
                // Try to create the RestaurantApp class
                try {
                    eval(code);
                    if (typeof RestaurantApp !== 'undefined') {
                        console.log('✅ RestaurantApp class defined successfully');
                        
                        // Try to instantiate
                        try {
                            const testApp = new RestaurantApp();
                            console.log('✅ RestaurantApp can be instantiated');
                            
                            // Check if key methods exist
                            const keyMethods = [
                                'loadReportsPage',
                                'calculateComprehensiveAnalytics',
                                'applyDateFilter',
                                'resetDateFilter',
                                'addNewWithdrawal',
                                'showWithdrawalDetails'
                            ];
                            
                            const missingMethods = keyMethods.filter(method => 
                                typeof testApp[method] !== 'function'
                            );
                            
                            if (missingMethods.length === 0) {
                                console.log('✅ All key methods are present');
                            } else {
                                console.log('❌ Missing methods:', missingMethods);
                            }
                            
                        } catch (instError) {
                            console.log('❌ Error instantiating RestaurantApp:', instError.message);
                        }
                        
                    } else {
                        console.log('❌ RestaurantApp class not defined after eval');
                    }
                } catch (evalError) {
                    console.log('❌ Error evaluating code:', evalError.message);
                    console.log('Error line:', evalError.lineNumber || 'Unknown');
                }
                
            } catch (syntaxError) {
                console.log('❌ JavaScript syntax error found:');
                console.log('Error:', syntaxError.message);
                console.log('Line:', syntaxError.lineNumber || 'Unknown');
                
                // Try to find the problematic line
                const lines = code.split('\n');
                const errorLine = syntaxError.lineNumber;
                if (errorLine && lines[errorLine - 1]) {
                    console.log('Problematic line:', lines[errorLine - 1]);
                    if (lines[errorLine - 2]) console.log('Previous line:', lines[errorLine - 2]);
                    if (lines[errorLine]) console.log('Next line:', lines[errorLine]);
                }
            }
            
        })
        .catch(error => {
            console.log('❌ Error loading JavaScript file:', error);
        });
        
} catch (error) {
    console.log('❌ Error in syntax checker:', error);
}

// Also check if the current app object exists
setTimeout(() => {
    console.log('\n🔍 Checking current app state...');
    console.log('window.app exists:', typeof window.app !== 'undefined');
    console.log('RestaurantApp exists:', typeof RestaurantApp !== 'undefined');
    
    if (typeof window.app !== 'undefined') {
        console.log('App methods available:');
        const methods = ['loadReportsPage', 'applyDateFilter', 'resetDateFilter', 'addNewWithdrawal'];
        methods.forEach(method => {
            console.log(`  ${method}:`, typeof window.app[method] === 'function' ? '✅' : '❌');
        });
    }
}, 1000);
