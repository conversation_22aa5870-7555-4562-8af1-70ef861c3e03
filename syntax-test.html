<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Syntax Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 JavaScript Syntax & Functionality Test</h1>
        <p>This page will test the JavaScript syntax and functionality of app.js</p>
        
        <div id="testResults"></div>
        
        <div style="margin-top: 20px;">
            <button class="btn" onclick="runSyntaxTest()">🔍 Run Syntax Test</button>
            <button class="btn" onclick="testAppMethods()">🧪 Test App Methods</button>
            <button class="btn" onclick="testButtonHandlers()">🔘 Test Button Handlers</button>
            <button class="btn" onclick="clearResults()">🗑️ Clear Results</button>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message.replace(/<[^>]*>/g, '')}`);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        function runSyntaxTest() {
            addResult('🔍 Starting syntax test...', 'info');
            
            // Test 1: Check if script loads
            const script = document.createElement('script');
            script.src = 'assets/js/app.js?v=13.0&test=' + Date.now();
            script.onload = function() {
                addResult('✅ JavaScript file loaded successfully', 'success');
                
                // Test 2: Check if RestaurantApp is defined
                if (typeof RestaurantApp !== 'undefined') {
                    addResult('✅ RestaurantApp class is defined', 'success');
                    
                    // Test 3: Try to instantiate
                    try {
                        const testApp = new RestaurantApp();
                        addResult('✅ RestaurantApp can be instantiated', 'success');
                        
                        // Make it globally available
                        window.app = testApp;
                        addResult('✅ App object set globally', 'success');
                        
                        // Test key methods
                        const methods = [
                            'loadReportsPage',
                            'calculateComprehensiveAnalytics', 
                            'applyDateFilter',
                            'resetDateFilter',
                            'addNewWithdrawal',
                            'showWithdrawalDetails',
                            'generateAnalyticsContent'
                        ];
                        
                        const missingMethods = methods.filter(method => 
                            typeof testApp[method] !== 'function'
                        );
                        
                        if (missingMethods.length === 0) {
                            addResult('✅ All key methods are present', 'success');
                        } else {
                            addResult(`❌ Missing methods: ${missingMethods.join(', ')}`, 'error');
                        }
                        
                    } catch (error) {
                        addResult(`❌ Error instantiating RestaurantApp: ${error.message}`, 'error');
                    }
                } else {
                    addResult('❌ RestaurantApp class is not defined', 'error');
                }
            };
            
            script.onerror = function() {
                addResult('❌ Error loading JavaScript file - syntax error likely', 'error');
            };
            
            document.head.appendChild(script);
        }

        function testAppMethods() {
            addResult('🧪 Testing app methods...', 'info');
            
            if (typeof window.app === 'undefined') {
                addResult('❌ App object not available. Run syntax test first.', 'error');
                return;
            }
            
            // Test applyDateFilter
            try {
                // Create mock elements
                createMockElement('reportStartDate', 'input', '2024-01-01');
                createMockElement('reportEndDate', 'input', '2024-01-31');
                createMockElement('analyticsContent', 'div');
                createMockElement('currentPeriodDisplay', 'div');
                
                window.app.applyDateFilter();
                addResult('✅ applyDateFilter method executed successfully', 'success');
            } catch (error) {
                addResult(`❌ applyDateFilter failed: ${error.message}`, 'error');
            }
            
            // Test resetDateFilter
            try {
                window.app.resetDateFilter();
                addResult('✅ resetDateFilter method executed successfully', 'success');
            } catch (error) {
                addResult(`❌ resetDateFilter failed: ${error.message}`, 'error');
            }
            
            // Test addNewWithdrawal
            try {
                window.app.addNewWithdrawal();
                addResult('✅ addNewWithdrawal method executed successfully', 'success');
                // Close any modal that opened
                setTimeout(() => {
                    const modal = document.querySelector('.modal-overlay');
                    if (modal) modal.remove();
                }, 100);
            } catch (error) {
                addResult(`❌ addNewWithdrawal failed: ${error.message}`, 'error');
            }
        }

        function testButtonHandlers() {
            addResult('🔘 Testing button handlers...', 'info');
            
            if (typeof window.app === 'undefined') {
                addResult('❌ App object not available. Run syntax test first.', 'error');
                return;
            }
            
            // Create test buttons
            const testButtons = [
                { text: 'Apply Filter', onclick: 'app.applyDateFilter()' },
                { text: 'Reset Filter', onclick: 'app.resetDateFilter()' },
                { text: 'Add Withdrawal', onclick: 'app.addNewWithdrawal()' }
            ];
            
            testButtons.forEach(btn => {
                try {
                    const button = document.createElement('button');
                    button.textContent = btn.text;
                    button.setAttribute('onclick', btn.onclick);
                    button.style.display = 'none';
                    document.body.appendChild(button);
                    
                    // Simulate click
                    button.click();
                    addResult(`✅ Button "${btn.text}" handler works`, 'success');
                    
                    // Clean up
                    button.remove();
                    
                    // Close any modals
                    setTimeout(() => {
                        const modal = document.querySelector('.modal-overlay');
                        if (modal) modal.remove();
                    }, 50);
                    
                } catch (error) {
                    addResult(`❌ Button "${btn.text}" failed: ${error.message}`, 'error');
                }
            });
        }

        function createMockElement(id, type, value = '') {
            let element = document.getElementById(id);
            if (!element) {
                element = document.createElement(type);
                element.id = id;
                if (type === 'input') {
                    element.value = value;
                }
                element.style.display = 'none';
                document.body.appendChild(element);
            }
            return element;
        }

        // Auto-run syntax test on page load
        window.addEventListener('load', () => {
            setTimeout(runSyntaxTest, 500);
        });
    </script>
</body>
</html>
