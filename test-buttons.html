<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-outline { background: white; color: #007bff; border: 1px solid #007bff; }
        .btn-outline:hover { background: #007bff; color: white; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        #testResults { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 Button Functionality Test</h1>
    <p>This page tests if the app object and button handlers are working correctly.</p>
    
    <div class="test-section">
        <h3>🔧 Basic Tests</h3>
        <button class="btn" onclick="testAppObject()">Test App Object</button>
        <button class="btn" onclick="testDateFilter()">Test Date Filter</button>
        <button class="btn" onclick="testWithdrawal()">Test Withdrawal</button>
        <button class="btn" onclick="testAllMethods()">Test All Methods</button>
    </div>
    
    <div class="test-section">
        <h3>📅 Date Filter Test</h3>
        <input type="date" id="testStartDate" value="2024-01-01">
        <input type="date" id="testEndDate" value="2024-01-31">
        <button class="btn" onclick="app.applyDateFilter()">Apply Filter (Direct)</button>
        <button class="btn btn-outline" onclick="app.resetDateFilter()">Reset Filter (Direct)</button>
    </div>
    
    <div class="test-section">
        <h3>💰 Withdrawal Test</h3>
        <button class="btn" onclick="app.addNewWithdrawal()">Add Withdrawal (Direct)</button>
        <button class="btn btn-outline" onclick="app.showWithdrawalDetails()">Show Withdrawals (Direct)</button>
    </div>
    
    <div id="testResults"></div>

    <!-- Include the main app script -->
    <script src="assets/js/app.js?v=13.0"></script>
    
    <script>
        // Initialize the app
        let app;
        
        document.addEventListener('DOMContentLoaded', function() {
            try {
                app = new RestaurantApp();
                window.app = app;
                console.log('✅ App initialized successfully');
                showResult('App initialized successfully', 'success');
            } catch (error) {
                console.error('❌ Error initializing app:', error);
                showResult('Error initializing app: ' + error.message, 'error');
            }
        });
        
        function testAppObject() {
            const results = [];
            
            // Test if app object exists
            if (typeof window.app !== 'undefined') {
                results.push('✅ App object exists globally');
            } else {
                results.push('❌ App object missing globally');
            }
            
            // Test if methods exist
            const methods = ['applyDateFilter', 'resetDateFilter', 'addNewWithdrawal', 'showWithdrawalDetails'];
            methods.forEach(method => {
                if (typeof window.app[method] === 'function') {
                    results.push(`✅ Method ${method} exists`);
                } else {
                    results.push(`❌ Method ${method} missing`);
                }
            });
            
            showResult(results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }
        
        function testDateFilter() {
            try {
                if (typeof window.app.applyDateFilter === 'function') {
                    // Create mock elements
                    if (!document.getElementById('reportStartDate')) {
                        const startInput = document.createElement('input');
                        startInput.type = 'date';
                        startInput.id = 'reportStartDate';
                        startInput.value = '2024-01-01';
                        startInput.style.display = 'none';
                        document.body.appendChild(startInput);
                    }
                    
                    if (!document.getElementById('reportEndDate')) {
                        const endInput = document.createElement('input');
                        endInput.type = 'date';
                        endInput.id = 'reportEndDate';
                        endInput.value = '2024-01-31';
                        endInput.style.display = 'none';
                        document.body.appendChild(endInput);
                    }
                    
                    if (!document.getElementById('analyticsContent')) {
                        const analyticsDiv = document.createElement('div');
                        analyticsDiv.id = 'analyticsContent';
                        analyticsDiv.style.display = 'none';
                        document.body.appendChild(analyticsDiv);
                    }
                    
                    if (!document.getElementById('currentPeriodDisplay')) {
                        const periodDiv = document.createElement('div');
                        periodDiv.id = 'currentPeriodDisplay';
                        periodDiv.style.display = 'none';
                        document.body.appendChild(periodDiv);
                    }
                    
                    window.app.applyDateFilter();
                    showResult('✅ Date filter method executed successfully', 'success');
                } else {
                    showResult('❌ Date filter method not found', 'error');
                }
            } catch (error) {
                showResult('❌ Date filter test failed: ' + error.message, 'error');
            }
        }
        
        function testWithdrawal() {
            try {
                if (typeof window.app.addNewWithdrawal === 'function') {
                    window.app.addNewWithdrawal();
                    showResult('✅ Withdrawal method executed successfully', 'success');
                } else {
                    showResult('❌ Withdrawal method not found', 'error');
                }
            } catch (error) {
                showResult('❌ Withdrawal test failed: ' + error.message, 'error');
            }
        }
        
        function testAllMethods() {
            const methods = [
                'calculateComprehensiveAnalytics',
                'generateAnalyticsContent',
                'showItemAnalysis',
                'showCategoryInsights',
                'showDetailedInventoryAnalytics',
                'showFinancialBreakdown',
                'showCustomerInsights'
            ];
            
            const results = [];
            methods.forEach(method => {
                if (typeof window.app[method] === 'function') {
                    results.push(`✅ ${method} exists`);
                } else {
                    results.push(`❌ ${method} missing`);
                }
            });
            
            showResult(results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<strong>Test Result:</strong><br>${message}`;
            resultsDiv.appendChild(resultDiv);
            
            // Scroll to results
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
