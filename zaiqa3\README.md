# 🍽️ Zaiqa Restaurant Management System

A comprehensive web-based restaurant management system built with HTML, CSS, and JavaScript. Designed specifically for desi restaurants with features like dual-service support (dine-in and takeaway), dynamic pricing, table management, inventory tracking, customer credit management (Udhars), and supplier account management (Khata). **No authentication required** - direct access to all features.

## ✨ Features

### 🏪 **Dual Service Support**
- **Dine-in Service**: Complete table management with interactive layout
- **Takeaway Orders**: Streamlined takeaway order processing
- **Dynamic Pricing**: Cold drinks cost 15 PKR more for dine-in vs takeaway

### 🍽️ **Table Management**
- Interactive visual table layout
- Real-time table status (Available, Occupied, Reserved, Cleaning)
- Table capacity management
- Order assignment to specific tables

### 📝 **Order Management**
- Create new orders for dine-in or takeaway
- Add menu items with automatic pricing
- Order status tracking (Pending → Confirmed → Preparing → Ready → Served)
- Special instructions and notes

### 🍜 **Enhanced Menu Management**
- **Add/Edit/Delete Menu Items**: Full CRUD operations for menu items
- **Category-based Organization**: Appetizers, Main Course, Beverages, Desserts
- **Dynamic Pricing Setup**: Set different prices for dine-in vs takeaway
- **Cold Drink Markup**: Automatic identification and pricing for beverages
- **Availability Management**: Enable/disable items in real-time
- **Preparation Time Tracking**: Set cooking times for kitchen planning

### 💰 **Advanced Pricing**
- **Per-head charges**: Configurable charges based on customer count (default: 100 PKR per person)
- **Cold drink markup**: Automatic 15 PKR markup for dine-in beverages
- **Tax calculation**: 16% GST automatically calculated
- **Discount system**: Flexible discount application

### 🧮 **Built-in Calculator**
- **Floating Calculator Widget**: Accessible from any page
- **Keyboard Support**: Full keyboard input support (numbers, operators, Enter, Escape, Backspace)
- **PKR Currency Formatting**: Automatic Pakistani Rupee formatting
- **Basic Operations**: Addition, subtraction, multiplication, division
- **Quick Access**: Available in billing, inventory, and order management

### 💳 **Complete POS System**
- **Modal POS Interface**: Full point-of-sale system in popup modal (not separate window)
- **Category-based Menu**: Quick access to items by category
- **Shopping Cart**: Add/remove items with quantity controls
- **Real-time Calculations**: Automatic total, tax, and service charge calculation
- **Multiple Payment Methods**: Cash, Card, Mobile Payment, Other
- **Receipt Generation**: Automatic receipt creation and printing
- **Service Type Integration**: Dine-in vs takeaway pricing

### 📦 **Inventory Management**
- Real-time stock tracking
- Low stock alerts and notifications
- Inventory item management with units and suppliers
- Stock status indicators (Good, Low, Critical)

### ✅ **Task Management System**
- **Daily To-Do Lists**: Create and manage restaurant tasks
- **Priority Levels**: High, Medium, Low priority assignments
- **Staff Assignment**: Assign tasks to specific team members
- **Progress Tracking**: Mark tasks as complete/incomplete
- **Due Time Management**: Set deadlines for tasks
- **Role-based Tasks**: Different tasks for different staff roles

### 🛒 **Smart Purchase Management**
- **Auto-generated Shopping Lists**: Based on low stock items
- **Manual Item Addition**: Add custom purchase items
- **Supplier Tracking**: Maintain supplier information
- **Cost Estimation**: Track estimated costs for budgeting
- **Urgency Levels**: High, Medium, Low urgency indicators
- **Status Tracking**: Pending → Ordered → Received workflow
- **Inventory Integration**: Links with existing inventory system

### 🧾 **Billing System**
- Generate detailed bills with itemized breakdown
- Multiple payment methods (Cash, Card, Mobile Payment)
- Tax and service charge calculations
- Bill printing functionality

### � **Customer Credit Management (Udhars)**
- **Customer Account Tracking**: Maintain credit accounts for regular customers
- **Outstanding Balance Management**: Track amounts owed by customers
- **Payment Recording**: Record payments and settle accounts
- **Credit History**: View complete transaction history for each customer
- **Overdue Alerts**: Identify customers with overdue payments
- **Customer Information**: Store contact details and addresses

### 📊 **Supplier Account Management (Khata)**
- **Supplier Account Tracking**: Manage accounts for all suppliers (Mughi Wala, Pepsi Wala, etc.)
- **Transaction Recording**: Track purchases, payments, and outstanding balances
- **Category-based Organization**: Organize suppliers by type (Meat, Beverages, Vegetables, etc.)
- **Balance Management**: Track what you owe suppliers and what they owe you
- **Supplier Information**: Maintain contact details and business information
- **Account Statements**: View detailed transaction history for each supplier

### � **Staff Management System**
- **Employee Records**: Maintain complete staff information with employee IDs
- **Position Management**: Track different roles (Manager, Chef, Waiter, Cashier, etc.)
- **Salary Tracking**: Record monthly salaries for all staff members
- **Dehari System**: Manage staff credit/debit accounts for restaurant purchases
- **Transaction Types**: Handle purchases, payments, advances, and salary deductions
- **Balance Management**: Track what staff owes restaurant and vice versa
- **Staff Purchases**: Record when staff buy food/items from restaurant
- **Payment History**: Complete transaction history for each staff member

### �📊 **Enhanced Dashboard**
- **Real-time Statistics**: Live revenue, orders, and table status (no fake data)
- **Actual Data Display**: All metrics calculated from real system usage
- **Recent Orders Tracking**: Shows actual orders as they are created
- **Low Stock Alerts**: Real inventory-based notifications
- **Daily Task Overview**: See pending and completed tasks
- **Purchase List Summary**: Quick view of items to buy
- **Clean Interface**: No dummy data - starts with clean, empty states

## 🚀 Quick Start

### Prerequisites
- **XAMPP** (or any local web server)
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Installation

1. **Download XAMPP**
   - Go to [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Download and install XAMPP for your operating system

2. **Setup the Application**
   - Copy all files to your XAMPP `htdocs` directory:
     ```
     C:\xampp\htdocs\zaiqa\
     ```

3. **Start XAMPP**
   - Open XAMPP Control Panel
   - Start **Apache** service
   - (MySQL is optional for this version as it uses local storage)

4. **Access the Application**
   - Open your web browser
   - Go to: `http://localhost/zaiqa`
   - **No login required** - you'll have immediate access to all features

## 📁 File Structure

```
zaiqa/
├── index.html              # Main application file
├── assets/
│   ├── css/
│   │   └── style.css       # Complete styling
│   └── js/
│       ├── app.js          # Main application logic
│       └── dashboard.js    # Dashboard functionality
├── supabase/
│   ├── schema.sql          # Database schema (for future use)
│   └── sample-data.sql     # Sample data (for future use)
└── README.md               # This file
```

## 🎯 How to Use

### 1. **Dashboard**
- **Immediate Access**: No login required - start using right away
- **Real-time Statistics**: View live revenue, orders, and table status
- **Clean Interface**: All data reflects actual system usage (no fake data)
- **Quick Actions**: Use action buttons for common tasks

### 2. **Table Management**
- Click on "Tables" in the sidebar
- View all restaurant tables with their current status
- Click on available tables to create new orders
- Monitor occupied tables and their orders

### 4. **Create Orders**
- Click "New Order" or select an available table
- Choose service type (Dine-in or Takeaway)
- Add menu items (notice dynamic pricing for cold drinks)
- Set customer count for per-head charges
- Add special instructions if needed

### 5. **Enhanced Menu Management**
- **Add New Items**: Click "Add Item" in Menu Management
- **Edit Existing Items**: Click "Edit" on any menu item
- **Set Pricing**: Configure base price, dine-in, and takeaway prices
- **Mark Cold Drinks**: Enable cold drink markup for beverages
- **Manage Availability**: Enable/disable items in real-time
- **Category Filtering**: View items by category (All, Appetizers, Main Course, etc.)

### 6. **Use POS System**
- **Open POS**: Click "Open POS" in quick actions or dashboard
- **Select Items**: Browse menu by category and click to add
- **Manage Cart**: Adjust quantities, remove items
- **Choose Service**: Switch between dine-in and takeaway
- **Process Payment**: Select payment method and complete transaction
- **Print Receipt**: Automatic receipt generation and printing

### 7. **Calculator Functions**
- **Access Calculator**: Click "Calculator" in quick actions
- **Basic Operations**: Use for manual calculations
- **PKR Formatting**: Click "PKR" button to format as currency
- **Quick Access**: Available from billing, inventory, and order pages

### 8. **Task Management**
- **View Tasks**: Check daily tasks on dashboard
- **Add Tasks**: Click "Add Task" to create new restaurant tasks
- **Assign Staff**: Assign tasks to specific team members
- **Set Priorities**: Mark tasks as High, Medium, or Low priority
- **Track Progress**: Check off completed tasks
- **Set Deadlines**: Add due times for important tasks

### 9. **Purchase Management**
- **Auto-generated Lists**: System suggests items based on low stock
- **Add Items**: Manually add items to purchase list
- **Track Suppliers**: Maintain supplier information
- **Monitor Costs**: Track estimated costs for budgeting
- **Update Status**: Mark items as Pending → Ordered → Received
- **Urgency Levels**: Prioritize purchases by urgency

### 10. **Inventory Tracking**
- Monitor stock levels for all ingredients
- Receive alerts for low stock items
- Update inventory quantities
- Integration with purchase management

### 11. **Customer Credit Management (Udhars)**
- **Access Udhars**: Click "Udhars" in the sidebar navigation
- **Add Customer**: Click "Add Udhar" to create new customer credit account
- **Record Transactions**: Add credit purchases and payments
- **Track Balances**: Monitor outstanding amounts for each customer
- **Payment History**: View complete transaction history
- **Overdue Management**: Identify customers with overdue payments

### 12. **Supplier Account Management (Khata)**
- **Access Khata**: Click "Khata" in the sidebar navigation
- **Add Suppliers**: Create accounts for suppliers (Mughi Wala, Pepsi Wala, etc.)
- **Record Purchases**: Track what you buy from each supplier
- **Payment Tracking**: Record payments made to suppliers
- **Balance Management**: Monitor what you owe and what suppliers owe you
- **Category Organization**: Organize suppliers by type (Meat, Beverages, etc.)

### 13. **Staff Management**
- **Access Staff**: Click "Staff" in the sidebar navigation
- **Add Staff Members**: Create employee records with IDs and positions
- **Salary Management**: Set monthly salaries for each staff member
- **Dehari Transactions**: Record staff purchases from restaurant
- **Payment Tracking**: Track staff payments and advances
- **Balance Monitoring**: Monitor staff credit/debit balances
- **Transaction Types**: Handle purchases, payments, advances, deductions

### 14. **Billing**
- Generate bills for completed orders
- Apply discounts if needed
- Process payments and print receipts

## 🔧 Customization

### **Restaurant Settings**
- Restaurant name: "Zaiqa Restaurant" (changeable in Settings)
- Currency: PKR (Pakistani Rupee)
- Tax rate: 16% GST
- Cold drink markup: 15 PKR for dine-in
- Per-head charge: 100 PKR (configurable)

### **Menu Items**
The system includes sample desi restaurant items:
- **Appetizers**: Chicken Tikka, Seekh Kebab
- **Main Course**: Chicken Karhai, Mutton Karhai, Biryani
- **Beverages**: Coca Cola, Fresh Lime, Lassi
- **Desserts**: Kheer, Gulab Jamun

### **Adding Your Own Data**
1. Edit the JavaScript files to add your menu items
2. Modify table layout in the `generateTablesGrid()` function
3. Update inventory items in the inventory management section

## 🌟 Key Features Demonstration

### **Dynamic Pricing Example:**
- Coca Cola (Takeaway): PKR 80
- Coca Cola (Dine-in): PKR 95 (15 PKR markup)

### **Per-Head Charges:**
- 4 customers × 100 PKR = 400 PKR additional charge
- Automatically added to dine-in orders

### **Tax Calculation:**
- Subtotal: PKR 1,000
- Per-head charges: PKR 400
- Before tax: PKR 1,400
- Tax (16%): PKR 224
- **Total: PKR 1,624**

## 🔄 Real-time Features

- **Live Dashboard Updates**: Statistics refresh every 30 seconds
- **Order Status Tracking**: Real-time order status changes
- **Table Status**: Instant table availability updates
- **Inventory Alerts**: Immediate low stock notifications

## 📱 Mobile Responsive

The system is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- Touch screen devices

## 🛠️ Technical Details

- **Frontend**: Pure HTML5, CSS3, JavaScript (ES6+)
- **Storage**: Local Storage (no database required)
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Inter)
- **Responsive**: CSS Grid and Flexbox
- **Browser Support**: All modern browsers

## 🔒 Security Features

- Input validation and sanitization
- Local storage data protection
- Secure data handling
- No authentication required (single-admin system)

## 🚀 Future Enhancements

The current version uses local storage. For production use, consider:
- Database integration (MySQL/PostgreSQL)
- Real-time synchronization across devices
- Print integration for receipts
- Payment gateway integration
- Advanced reporting features

## 📞 Support

This is a demo restaurant management system. For customization or production deployment:
1. Modify the JavaScript files for your specific needs
2. Integrate with a proper database system
3. Add server-side functionality as needed

## 📄 License

This project is for educational and demonstration purposes.

---

**🍽️ Zaiqa Restaurant Management System** - Built for the modern desi restaurant industry!
