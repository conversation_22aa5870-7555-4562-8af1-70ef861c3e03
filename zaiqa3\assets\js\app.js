// Main Application JavaScript
class RestaurantApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
        this.initializeTheme();
    }

    init() {
        try {
            console.log('🚀 Starting app initialization...');

            this.setupEventListeners();
            this.updateTime();
            this.loadTodoList();
            this.loadPurchaseList();
            this.updateDashboardStats();

            // Update time every second
            setInterval(() => {
                try {
                    this.updateTime();
                } catch (error) {
                    console.error('Error updating time:', error);
                }
            }, 1000);

            // Update dashboard stats every 30 seconds
            setInterval(() => {
                try {
                    this.updateDashboardStats();
                } catch (error) {
                    console.error('Error updating dashboard stats:', error);
                }
            }, 30000);

            console.log('✅ App initialization completed successfully');
        } catch (error) {
            console.error('❌ Critical error during app initialization:', error);
            console.error('Error details:', error.message);
            console.error('Stack trace:', error.stack);

            // Show user-friendly error
            this.showNotification('System initialization error. Please refresh the page.', 'error');
        }
    }

    setupEventListeners() {
        // Navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Action buttons
        const actionBtns = document.querySelectorAll('.action-btn');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleActionClick(e));
        });

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }
    }



    handleNavigation(e) {
        e.preventDefault();
        
        const page = e.currentTarget.getAttribute('data-page');
        this.navigateToPage(page);
    }

    handleActionClick(e) {
        e.preventDefault();
        
        const page = e.currentTarget.getAttribute('data-page');
        if (page) {
            this.navigateToPage(page);
        }
    }

    navigateToPage(page) {
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-page="${page}"]`);
        if (activeLink && activeLink.classList.contains('nav-link')) {
            activeLink.classList.add('active');
        }

        // Hide all pages
        document.querySelectorAll('.page').forEach(p => {
            p.classList.remove('active');
        });

        // Show selected page
        const targetPage = document.getElementById(`${page}Page`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.loadPageContent(page);
        }

        // Update page title
        this.updatePageTitle(page);
        this.currentPage = page;
    }

    loadPageContent(page) {
        const pageElement = document.getElementById(`${page}Page`);

        if (!pageElement) {
            console.error(`Page element not found: ${page}Page`);
            return;
        }

        switch (page) {
            case 'tables':
                this.loadTablesPage(pageElement);
                break;

            case 'menu':
                this.loadMenuPage(pageElement);
                break;
            case 'inventory':
                this.loadInventoryPage(pageElement);
                break;
            case 'billing':
                this.loadBillingPage(pageElement);
                break;
            case 'reports':
                this.loadReportsPage(pageElement);
                break;
            case 'udhars':
                this.loadUdharsPage(pageElement);
                break;
            case 'khata':
                this.loadKhataPage(pageElement);
                break;
            case 'staff':
                this.loadStaffPage(pageElement);
                break;
            case 'expenses':
                this.loadExpensesPage(pageElement);
                break;
            case 'settings':
                this.loadSettingsPage(pageElement);
                break;
            default:
                console.warn(`Unknown page: ${page}`);
        }
    }

    updatePageTitle(page) {
        const titles = {
            dashboard: { title: 'Dashboard', subtitle: 'Welcome to your restaurant management system' },
            tables: { title: 'Table Management', subtitle: 'Monitor and manage restaurant tables' },
            orders: { title: 'Order Management', subtitle: 'Create and track customer orders' },
            menu: { title: 'Menu Management', subtitle: 'Manage your restaurant menu items' },
            inventory: { title: 'Inventory Management', subtitle: 'Track and manage inventory items' },
            billing: { title: 'Billing & Payments', subtitle: 'Generate bills and process payments' },
            udhars: { title: 'Customer Credit (Udhars)', subtitle: 'Manage customer credit accounts and payments' },
            khata: { title: 'Supplier Accounts (Khata)', subtitle: 'Track supplier transactions and balances' },
            staff: { title: 'Staff Management', subtitle: 'Manage staff payments, deharis, and restaurant purchases' },
            reports: { title: 'Reports & Analytics', subtitle: 'View sales reports and analytics' },
            settings: { title: 'Settings', subtitle: 'Configure system settings' }
        };

        const pageInfo = titles[page] || { title: 'Page', subtitle: '' };
        document.getElementById('pageTitle').textContent = pageInfo.title;
        document.getElementById('pageSubtitle').textContent = pageInfo.subtitle;
    }

    updateDashboardStats() {
        // Calculate real stats from actual data
        const orders = this.getOrders();
        const todayOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            const today = new Date();
            return orderDate.toDateString() === today.toDateString();
        });

        const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const activeOrders = orders.filter(order => ['pending', 'confirmed', 'preparing'].includes(order.status)).length;
        const avgOrderValue = todayOrders.length > 0 ? Math.round(todayRevenue / todayOrders.length) : 0;

        // Calculate cash balance (Total Revenue - Total Expenses)
        const totalRevenue = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const expenses = this.getExpenses();
        const automaticExpenses = this.calculateAutomaticExpenses();
        const manualExpenses = expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
        const totalExpenses = manualExpenses + automaticExpenses.staffDehari + automaticExpenses.khataPayments;
        const cashBalance = totalRevenue - totalExpenses;

        // Update dashboard elements
        const revenueEl = document.getElementById('todayRevenue');
        const ordersEl = document.getElementById('todayOrders');
        const activeEl = document.getElementById('activeOrders');
        const avgEl = document.getElementById('avgOrderValue');
        const cashEl = document.getElementById('cashBalance');

        if (revenueEl) revenueEl.textContent = this.formatCurrency(todayRevenue);
        if (ordersEl) ordersEl.textContent = todayOrders.length.toString();
        if (activeEl) activeEl.textContent = `${activeOrders} active`;
        if (avgEl) avgEl.textContent = this.formatCurrency(avgOrderValue);

        // Update cash balance with health indicator
        if (cashEl) {
            cashEl.textContent = this.formatCurrency(cashBalance);

            // Update cash balance health indicator
            const cashCard = cashEl.closest('.stat-card');
            if (cashCard) {
                // Remove existing health classes
                cashCard.classList.remove('cash-healthy', 'cash-warning', 'cash-critical');

                // Add appropriate health class
                if (cashBalance >= 50000) {
                    cashCard.classList.add('cash-healthy');
                } else if (cashBalance >= 10000) {
                    cashCard.classList.add('cash-warning');
                } else {
                    cashCard.classList.add('cash-critical');
                }
            }
        }

        // Update recent orders
        this.updateRecentOrdersList(todayOrders.slice(-5).reverse());
    }

    updateRecentOrdersList(orders) {
        const recentOrdersList = document.getElementById('recentOrdersList');
        if (!recentOrdersList) return;

        if (orders.length === 0) {
            recentOrdersList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <p>No orders yet today</p>
                    <p style="font-size: 0.875rem; color: var(--gray-500);">Orders will appear here as they are created</p>
                </div>
            `;
            return;
        }

        recentOrdersList.innerHTML = orders.map(order => `
            <div class="order-item">
                <div class="order-info">
                    <span class="order-id">${order.order_number}</span>
                    <span class="order-table">${order.table_number || 'Takeaway'} • ${order.items?.length || 0} items</span>
                </div>
                <div class="order-status">
                    <span class="status-badge ${order.status}">${order.status}</span>
                    <span class="order-amount">${this.formatCurrency(order.total_amount)}</span>
                </div>
            </div>
        `).join('');
    }

    getOrders() {
        const saved = localStorage.getItem('restaurantOrders');
        return saved ? JSON.parse(saved) : [];
    }

    formatCurrency(amount) {
        // Handle null, undefined, or invalid amounts
        if (amount === null || amount === undefined || isNaN(amount)) {
            return 'PKR 0';
        }

        // Ensure amount is a number
        const numAmount = Number(amount);
        if (isNaN(numAmount)) {
            return 'PKR 0';
        }

        return `PKR ${numAmount.toLocaleString()}`;
    }

    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.toggle('collapsed');
    }



    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Page loading methods
    loadTablesPage(pageElement) {
        const tables = this.getTables();
        const availableTables = tables.filter(t => t.status === 'available').length;
        const occupiedTables = tables.filter(t => t.status === 'occupied').length;
        const reservedTables = tables.filter(t => t.status === 'reserved').length;
        const cleaningTables = tables.filter(t => t.status === 'cleaning').length;

        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Restaurant Tables</h3>
                <div class="header-actions">
                    <button class="btn btn-outline" onclick="app.showTableLayoutSettings()">
                        <i class="fas fa-cog"></i>
                        Layout Settings
                    </button>
                    <button class="btn btn-primary" onclick="app.showAddTableModal()">
                        <i class="fas fa-plus"></i>
                        Add Table
                    </button>
                </div>
            </div>

            <div class="table-stats">
                <div class="stat-row">
                    <div class="stat-item">
                        <span class="stat-label">Total Tables:</span>
                        <span class="stat-value">${tables.length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Available:</span>
                        <span class="stat-value text-success">${availableTables}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Occupied:</span>
                        <span class="stat-value text-error">${occupiedTables}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Reserved:</span>
                        <span class="stat-value text-warning">${reservedTables}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Cleaning:</span>
                        <span class="stat-value text-info">${cleaningTables}</span>
                    </div>
                </div>
            </div>

            <div class="table-legend">
                <div class="legend-item">
                    <div class="legend-color available"></div>
                    <span>Available</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color occupied"></div>
                    <span>Occupied</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color reserved"></div>
                    <span>Reserved</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color cleaning"></div>
                    <span>Cleaning</span>
                </div>
            </div>

            <div class="tables-grid" id="tablesGrid">
                ${this.generateTablesGrid()}
            </div>
        `;
    }



    loadMenuPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Menu Management</h3>
                <button class="btn btn-primary" onclick="app.showAddMenuItemModal()">
                    <i class="fas fa-plus"></i>
                    Add Item
                </button>
            </div>

            <div class="menu-categories">
                <div class="category-tabs">
                    <button class="category-tab active" data-category="all" onclick="app.filterMenuItems('all')">All Items</button>
                    <button class="category-tab" data-category="appetizers" onclick="app.filterMenuItems('appetizers')">Appetizers</button>
                    <button class="category-tab" data-category="main" onclick="app.filterMenuItems('main')">Main Course</button>
                    <button class="category-tab" data-category="beverages" onclick="app.filterMenuItems('beverages')">Beverages</button>
                    <button class="category-tab" data-category="desserts" onclick="app.filterMenuItems('desserts')">Desserts</button>
                </div>
            </div>

            <div class="menu-items-grid" id="menuItemsGrid">
                ${this.generateMenuItems()}
            </div>
        `;
    }

    loadInventoryPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Inventory Management</h3>
                <div class="header-actions">
                    <button class="btn btn-outline" onclick="app.showRecordUsageModal()">
                        <i class="fas fa-minus-circle"></i>
                        Record Usage
                    </button>
                    <button class="btn btn-primary" onclick="app.showAddInventoryItemModal()">
                        <i class="fas fa-plus"></i>
                        Add Item
                    </button>
                </div>
            </div>

            <div class="inventory-alerts" id="inventoryAlerts">
                ${this.generateInventoryAlerts()}
            </div>

            <div class="inventory-table">
                ${this.generateInventoryTable()}
            </div>

            <!-- Usage History Section -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-history"></i> Recent Usage History</h4>
                    <button class="btn btn-outline btn-sm" onclick="app.showFullUsageHistory()">
                        <i class="fas fa-list"></i>
                        View All
                    </button>
                </div>
                <div class="usage-history-table">
                    ${this.generateUsageHistoryTable()}
                </div>
            </div>
        `;

        this.updateInventoryAlerts();
    }

    loadBillingPage(pageElement) {
        const billingStats = this.calculateBillingStats();

        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Billing & Payments</h3>
                <button class="btn btn-primary" onclick="app.openPOS()">
                    <i class="fas fa-plus"></i>
                    New Bill (POS)
                </button>
            </div>

            <div class="billing-stats">
                <div class="stat-card">
                    <h4>Today's Sales</h4>
                    <p class="amount">PKR ${billingStats.todaySales.toLocaleString()}</p>
                    <small>${billingStats.todayOrdersCount} orders today</small>
                </div>
                <div class="stat-card">
                    <h4>Yesterday's Sales</h4>
                    <p class="amount">PKR ${billingStats.yesterdaySales.toLocaleString()}</p>
                    <small>${billingStats.yesterdayOrdersCount} orders yesterday</small>
                </div>
                <div class="stat-card">
                    <h4>This Week's Sales</h4>
                    <p class="amount">PKR ${billingStats.weekSales.toLocaleString()}</p>
                    <small>${billingStats.weekOrdersCount} orders this week</small>
                </div>
                <div class="stat-card">
                    <h4>Average Order</h4>
                    <p class="amount">PKR ${billingStats.averageOrder.toLocaleString()}</p>
                    <small>Per order</small>
                </div>
            </div>

            <!-- Time-based Filter Buttons -->
            <div class="billing-filters">
                <div class="filter-buttons">
                    <button class="btn btn-outline filter-btn active" onclick="app.filterBills('all')" data-filter="all">
                        <i class="fas fa-list"></i>
                        All Bills (${billingStats.totalBills})
                    </button>
                    <button class="btn btn-outline filter-btn" onclick="app.filterBills('today')" data-filter="today">
                        <i class="fas fa-calendar-day"></i>
                        Today's Bills (${billingStats.todayOrdersCount})
                    </button>
                    <button class="btn btn-outline filter-btn" onclick="app.filterBills('yesterday')" data-filter="yesterday">
                        <i class="fas fa-calendar-minus"></i>
                        Yesterday's Bills (${billingStats.yesterdayOrdersCount})
                    </button>
                    <button class="btn btn-outline filter-btn" onclick="app.filterBills('week')" data-filter="week">
                        <i class="fas fa-calendar-week"></i>
                        This Week's Bills (${billingStats.weekOrdersCount})
                    </button>
                </div>
                <div class="filter-summary" id="filterSummary">
                    <span class="summary-text">Showing all bills</span>
                    <span class="summary-amount">Total: PKR ${billingStats.totalSales.toLocaleString()}</span>
                </div>
            </div>

            <div class="bills-list" id="billsList">
                ${this.generateBillsList('all')}
            </div>
        `;
    }

    calculateBillingStats() {
        const orders = this.getOrders();
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const yesterdayStart = new Date(todayStart.getTime() - 24 * 60 * 60 * 1000);
        const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        // Today's stats
        const todayOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= todayStart;
        });

        const todaySales = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const todayOrdersCount = todayOrders.length;

        // Yesterday's stats
        const yesterdayOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= yesterdayStart && orderDate < todayStart;
        });

        const yesterdaySales = yesterdayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const yesterdayOrdersCount = yesterdayOrders.length;

        // Week's stats
        const weekOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= weekStart;
        });

        const weekSales = weekOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const weekOrdersCount = weekOrders.length;

        // Total stats
        const totalBills = orders.length;
        const totalSales = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const averageOrder = totalBills > 0 ? totalSales / totalBills : 0;

        return {
            todaySales,
            todayOrdersCount,
            yesterdaySales,
            yesterdayOrdersCount,
            weekSales,
            weekOrdersCount,
            totalBills,
            totalSales,
            averageOrder
        };
    }

    filterBills(filterType) {
        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

        // Update bills list
        const billsList = document.getElementById('billsList');
        const filterSummary = document.getElementById('filterSummary');

        if (billsList) {
            billsList.innerHTML = this.generateBillsList(filterType);
        }

        // Update filter summary
        const orders = this.getFilteredOrders(filterType);
        const totalAmount = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);

        let summaryText = '';
        switch(filterType) {
            case 'today':
                summaryText = "Showing today's bills";
                break;
            case 'yesterday':
                summaryText = "Showing yesterday's bills";
                break;
            case 'week':
                summaryText = "Showing this week's bills";
                break;
            default:
                summaryText = "Showing all bills";
        }

        if (filterSummary) {
            filterSummary.innerHTML = `
                <span class="summary-text">${summaryText}</span>
                <span class="summary-amount">Total: PKR ${totalAmount.toLocaleString()}</span>
            `;
        }
    }

    getFilteredOrders(filterType) {
        const orders = this.getOrders();
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const yesterdayStart = new Date(todayStart.getTime() - 24 * 60 * 60 * 1000);
        const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        switch(filterType) {
            case 'today':
                return orders.filter(order => {
                    const orderDate = new Date(order.created_at);
                    return orderDate >= todayStart;
                });
            case 'yesterday':
                return orders.filter(order => {
                    const orderDate = new Date(order.created_at);
                    return orderDate >= yesterdayStart && orderDate < todayStart;
                });
            case 'week':
                return orders.filter(order => {
                    const orderDate = new Date(order.created_at);
                    return orderDate >= weekStart;
                });
            default:
                return orders;
        }
    }

    loadReportsPage(pageElement) {
        const today = new Date().toISOString().split('T')[0];
        const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        const analytics = this.calculateComprehensiveAnalytics();

        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Reports & Analytics</h3>
                <div class="header-actions">
                    <div class="date-range">
                        <input type="date" id="reportStartDate" class="form-control" value="${lastWeek}">
                        <span>to</span>
                        <input type="date" id="reportEndDate" class="form-control" value="${today}">
                        <button class="btn btn-primary" onclick="app.generateCustomReport()">
                            <i class="fas fa-chart-line"></i>
                            Generate Report
                        </button>
                        <button class="btn btn-outline" onclick="app.exportReports()">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                    <div class="end-of-day-actions">
                        <button class="btn btn-success" onclick="app.showEndOfDayModal()" ${this.isDayFinalized(today) ? 'disabled' : ''}>
                            <i class="fas fa-calendar-check"></i>
                            ${this.isDayFinalized(today) ? 'Day Finalized' : 'Finalize Day'}
                        </button>
                    </div>
                </div>
            </div>

            <!-- End of Day Summary (if day is finalized) -->
            ${this.isDayFinalized(today) ? this.generateEndOfDaySummary(today) : ''}

            <!-- Enhanced Summary Cards -->
            <div class="reports-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="summary-content">
                            <h3>PKR ${analytics.totalRevenue.toLocaleString()}</h3>
                            <p>Total Revenue</p>
                            <small class="growth ${analytics.growthRate >= 0 ? 'positive' : 'negative'}">
                                ${analytics.growthRate >= 0 ? '+' : ''}${analytics.growthRate.toFixed(1)}% vs last week
                            </small>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="summary-content">
                            <h3>PKR ${analytics.estimatedProfit.toLocaleString()}</h3>
                            <p>Estimated Profit</p>
                            <small class="profit-margin">${analytics.profitMargin.toFixed(1)}% margin</small>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="summary-content">
                            <h3>${analytics.totalOrders}</h3>
                            <p>Total Orders</p>
                            <small>PKR ${analytics.averageOrder.toFixed(0)} avg order</small>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="summary-content">
                            <h3>${analytics.totalCustomers}</h3>
                            <p>Total Customers</p>
                            <small>${analytics.avgCustomersPerOrder.toFixed(1)} avg per order</small>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="summary-content">
                            <h3>PKR ${this.getTotalOwnerWithdrawals().toLocaleString()}</h3>
                            <p>Money Taken</p>
                            <small>Owner withdrawals</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top-Selling Items Report -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-trophy"></i> Top-Selling Items</h4>
                </div>
                <div class="top-items-grid">
                    ${analytics.topItems.map((item, index) => `
                        <div class="top-item-card">
                            <div class="item-rank">#${index + 1}</div>
                            <div class="item-details">
                                <h5>${item.name}</h5>
                                <div class="item-stats">
                                    <span class="quantity">${item.quantity} sold</span>
                                    <span class="revenue">PKR ${item.revenue.toLocaleString()}</span>
                                    <span class="percentage">${item.percentage.toFixed(1)}% of total</span>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <!-- Item-Wise Detailed Analysis -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-analytics"></i> Item-Wise Profit Analysis</h4>
                </div>
                <div class="profit-analysis-table">
                    <table class="analysis-table">
                        <thead>
                            <tr>
                                <th>Item Name</th>
                                <th>Sales Amount</th>
                                <th>Ingredient Cost</th>
                                <th>Profit/Loss</th>
                                <th>Margin %</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.generateItemProfitAnalysis()}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Comprehensive Expense Tracking -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-receipt"></i> Expense Analysis</h4>
                </div>
                <div class="expense-grid">
                    <div class="expense-card">
                        <h5>Ingredient Costs</h5>
                        <div class="expense-amount">PKR ${analytics.totalIngredientCosts.toLocaleString()}</div>
                        <div class="expense-details">
                            ${analytics.inventoryCosts.slice(0, 3).map(item => `
                                <div class="expense-item">
                                    <span>${item.name}</span>
                                    <span>PKR ${item.cost.toFixed(0)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="expense-card">
                        <h5>Staff Costs</h5>
                        <div class="expense-amount">PKR ${this.calculateStaffCosts().toLocaleString()}</div>
                        <div class="expense-details">
                            <div class="expense-item">
                                <span>Salaries</span>
                                <span>PKR ${this.calculateStaffCosts().toFixed(0)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="expense-card">
                        <h5>Operational Costs</h5>
                        <div class="expense-amount">PKR ${(analytics.estimatedCosts - analytics.totalIngredientCosts).toLocaleString()}</div>
                        <div class="expense-details">
                            <div class="expense-item">
                                <span>Utilities & Others</span>
                                <span>PKR ${(analytics.estimatedCosts - analytics.totalIngredientCosts).toFixed(0)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Trends and Customer Analytics -->
            <div class="analytics-grid">
                <div class="analytics-card">
                    <div class="card-header">
                        <h4><i class="fas fa-chart-area"></i> Sales Trends (7 Days)</h4>
                    </div>
                    <div class="trend-chart">
                        ${analytics.dailyTrend.map(day => `
                            <div class="trend-day">
                                <div class="trend-bar" style="height: ${day.revenue > 0 ? (day.revenue / Math.max(...analytics.dailyTrend.map(d => d.revenue))) * 100 : 0}%"></div>
                                <div class="trend-label">${day.date}</div>
                                <div class="trend-value">PKR ${day.revenue.toLocaleString()}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h4><i class="fas fa-clock"></i> Peak Hours Analysis</h4>
                    </div>
                    <div class="peak-hours">
                        ${analytics.peakHours.map(hour => `
                            <div class="hour-stat">
                                <span class="hour-time">${hour.hour}:00</span>
                                <div class="hour-bar">
                                    <div class="hour-fill" style="width: ${hour.percentage}%"></div>
                                </div>
                                <span class="hour-orders">${hour.orders} orders</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h4><i class="fas fa-pie-chart"></i> Category Breakdown</h4>
                    </div>
                    <div class="category-breakdown">
                        ${analytics.categoryBreakdown.map(cat => `
                            <div class="category-item">
                                <div class="category-info">
                                    <span class="category-name">${cat.category}</span>
                                    <span class="category-percentage">${cat.percentage.toFixed(1)}%</span>
                                </div>
                                <div class="category-bar">
                                    <div class="category-fill" style="width: ${cat.percentage}%"></div>
                                </div>
                                <div class="category-stats">
                                    <span>PKR ${cat.revenue.toLocaleString()}</span>
                                    <span>${cat.orders} orders</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-tachometer-alt"></i> Key Performance Indicators</h4>
                </div>
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-value">${analytics.averageOrder.toFixed(0)}</div>
                        <div class="kpi-label">Average Order Value</div>
                        <div class="kpi-unit">PKR</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">${analytics.avgCustomersPerOrder.toFixed(1)}</div>
                        <div class="kpi-label">Customers per Order</div>
                        <div class="kpi-unit">People</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">${analytics.profitMargin.toFixed(1)}</div>
                        <div class="kpi-label">Profit Margin</div>
                        <div class="kpi-unit">%</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">${analytics.growthRate.toFixed(1)}</div>
                        <div class="kpi-label">Growth Rate</div>
                        <div class="kpi-unit">%</div>
                    </div>
                </div>
            </div>

            <!-- Inventory Analytics -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-boxes"></i> Inventory Analytics</h4>
                    <button class="btn btn-outline btn-sm" onclick="app.showDetailedInventoryAnalytics()">
                        <i class="fas fa-chart-bar"></i> Detailed View
                    </button>
                </div>
                <div class="inventory-analytics-grid">
                    <div class="inventory-stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="stat-content">
                            <h3>${analytics.inventoryStats.totalItems}</h3>
                            <p>Total Items</p>
                            <small>PKR ${analytics.inventoryStats.totalValue.toLocaleString()} total value</small>
                        </div>
                    </div>
                    <div class="inventory-stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>${analytics.inventoryStats.lowStockItems}</h3>
                            <p>Low Stock Items</p>
                            <small>${analytics.inventoryStats.outOfStockItems} out of stock</small>
                        </div>
                    </div>
                    <div class="inventory-stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3>${analytics.inventoryStats.usageRecords}</h3>
                            <p>Usage Records</p>
                            <small>Last 30 days</small>
                        </div>
                    </div>
                    <div class="inventory-stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3>PKR ${analytics.inventoryStats.avgItemValue.toFixed(0)}</h3>
                            <p>Avg Item Value</p>
                            <small>Per unit cost</small>
                        </div>
                    </div>
                </div>
                <div class="top-usage-items">
                    <h5>Most Used Items (Last 30 Days)</h5>
                    <div class="usage-items-list">
                        ${analytics.inventoryStats.topUsedItems.map((item, index) => `
                            <div class="usage-item">
                                <span class="usage-rank">#${index + 1}</span>
                                <span class="usage-name">${item.name}</span>
                                <span class="usage-quantity">${item.totalUsed} ${item.unit}</span>
                                <span class="usage-reason">${item.primaryReason}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <!-- Financial Health Dashboard -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-chart-pie"></i> Financial Health Dashboard</h4>
                    <button class="btn btn-outline btn-sm" onclick="app.showFinancialBreakdown()">
                        <i class="fas fa-calculator"></i> Detailed Breakdown
                    </button>
                </div>
                <div class="financial-health-grid">
                    <div class="financial-card revenue">
                        <div class="financial-header">
                            <h5><i class="fas fa-arrow-up"></i> Revenue Streams</h5>
                        </div>
                        <div class="financial-content">
                            <div class="financial-item">
                                <span>Dine-in Sales</span>
                                <span class="amount positive">PKR ${analytics.financialHealth.dineInRevenue.toLocaleString()}</span>
                            </div>
                            <div class="financial-item">
                                <span>Takeaway Sales</span>
                                <span class="amount positive">PKR ${analytics.financialHealth.takeawayRevenue.toLocaleString()}</span>
                            </div>
                            <div class="financial-item total">
                                <span><strong>Total Revenue</strong></span>
                                <span class="amount positive"><strong>PKR ${analytics.totalRevenue.toLocaleString()}</strong></span>
                            </div>
                        </div>
                    </div>
                    <div class="financial-card expenses">
                        <div class="financial-header">
                            <h5><i class="fas fa-arrow-down"></i> Expense Breakdown</h5>
                        </div>
                        <div class="financial-content">
                            <div class="financial-item">
                                <span>Ingredient Costs</span>
                                <span class="amount negative">PKR ${analytics.financialHealth.ingredientCosts.toLocaleString()}</span>
                            </div>
                            <div class="financial-item">
                                <span>Staff Salaries</span>
                                <span class="amount negative">PKR ${analytics.financialHealth.staffCosts.toLocaleString()}</span>
                            </div>
                            <div class="financial-item">
                                <span>Supplier Payments</span>
                                <span class="amount negative">PKR ${analytics.financialHealth.supplierPayments.toLocaleString()}</span>
                            </div>
                            <div class="financial-item">
                                <span>Other Expenses</span>
                                <span class="amount negative">PKR ${analytics.financialHealth.otherExpenses.toLocaleString()}</span>
                            </div>
                            <div class="financial-item total">
                                <span><strong>Total Expenses</strong></span>
                                <span class="amount negative"><strong>PKR ${analytics.financialHealth.totalExpenses.toLocaleString()}</strong></span>
                            </div>
                        </div>
                    </div>
                    <div class="financial-card profit">
                        <div class="financial-header">
                            <h5><i class="fas fa-chart-line"></i> Profit Analysis</h5>
                        </div>
                        <div class="financial-content">
                            <div class="financial-item">
                                <span>Gross Profit</span>
                                <span class="amount ${analytics.financialHealth.grossProfit >= 0 ? 'positive' : 'negative'}">
                                    PKR ${Math.abs(analytics.financialHealth.grossProfit).toLocaleString()}
                                </span>
                            </div>
                            <div class="financial-item">
                                <span>Profit Margin</span>
                                <span class="amount ${analytics.profitMargin >= 0 ? 'positive' : 'negative'}">
                                    ${analytics.profitMargin.toFixed(1)}%
                                </span>
                            </div>
                            <div class="financial-item">
                                <span>Break-even Point</span>
                                <span class="amount info">PKR ${analytics.financialHealth.breakEvenPoint.toLocaleString()}</span>
                            </div>
                            <div class="financial-item total">
                                <span><strong>Net Profit</strong></span>
                                <span class="amount ${analytics.financialHealth.netProfit >= 0 ? 'positive' : 'negative'}">
                                    <strong>PKR ${Math.abs(analytics.financialHealth.netProfit).toLocaleString()}</strong>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Analytics -->
            <div class="analytics-section">
                <div class="section-header">
                    <h4><i class="fas fa-users"></i> Customer Analytics</h4>
                    <button class="btn btn-outline btn-sm" onclick="app.showCustomerInsights()">
                        <i class="fas fa-user-chart"></i> Customer Insights
                    </button>
                </div>
                <div class="customer-analytics-grid">
                    <div class="customer-metric">
                        <div class="metric-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="metric-content">
                            <h4>${analytics.customerAnalytics.totalUniqueCustomers}</h4>
                            <p>Unique Customers</p>
                            <small>${analytics.customerAnalytics.newCustomersThisWeek} new this week</small>
                        </div>
                    </div>
                    <div class="customer-metric">
                        <div class="metric-icon">
                            <i class="fas fa-redo"></i>
                        </div>
                        <div class="metric-content">
                            <h4>${analytics.customerAnalytics.repeatCustomers}</h4>
                            <p>Repeat Customers</p>
                            <small>${analytics.customerAnalytics.repeatRate.toFixed(1)}% repeat rate</small>
                        </div>
                    </div>
                    <div class="customer-metric">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-content">
                            <h4>${analytics.customerAnalytics.avgOrderFrequency.toFixed(1)}</h4>
                            <p>Avg Orders/Customer</p>
                            <small>Per month</small>
                        </div>
                    </div>
                    <div class="customer-metric">
                        <div class="metric-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="metric-content">
                            <h4>PKR ${analytics.customerAnalytics.customerLifetimeValue.toFixed(0)}</h4>
                            <p>Avg Customer Value</p>
                            <small>Lifetime value</small>
                        </div>
                    </div>
                </div>
                <div class="customer-segments">
                    <h5>Customer Segments</h5>
                    <div class="segments-list">
                        ${analytics.customerAnalytics.segments.map(segment => `
                            <div class="segment-item">
                                <div class="segment-info">
                                    <span class="segment-name">${segment.name}</span>
                                    <span class="segment-count">${segment.count} customers</span>
                                </div>
                                <div class="segment-bar">
                                    <div class="segment-fill" style="width: ${segment.percentage}%"></div>
                                </div>
                                <span class="segment-percentage">${segment.percentage.toFixed(1)}%</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        // Load initial report data
        this.loadReportData();
    }

    calculateComprehensiveAnalytics() {
        const orders = this.getOrders();
        const menuItems = this.getMenuItems();
        const inventoryItems = this.getInventoryItems();

        // Basic calculations
        const totalRevenue = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const totalOrders = orders.length;
        const averageOrder = totalOrders > 0 ? totalRevenue / totalOrders : 0;

        // Customer analytics
        const totalCustomers = orders.reduce((sum, order) => sum + (order.customer_count || 1), 0);
        const avgCustomersPerOrder = totalOrders > 0 ? totalCustomers / totalOrders : 0;

        // Top selling items
        const itemSales = {};
        orders.forEach(order => {
            if (order.items) {
                order.items.forEach(item => {
                    if (!itemSales[item.name]) {
                        itemSales[item.name] = { quantity: 0, revenue: 0 };
                    }
                    itemSales[item.name].quantity += item.quantity;
                    itemSales[item.name].revenue += item.price * item.quantity;
                });
            }
        });

        const topItems = Object.entries(itemSales)
            .map(([name, data]) => ({
                name,
                quantity: data.quantity,
                revenue: data.revenue,
                percentage: totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0
            }))
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 5);

        // Category breakdown
        const categoryBreakdown = {};
        orders.forEach(order => {
            if (order.items) {
                order.items.forEach(item => {
                    const menuItem = menuItems.find(m => m.name === item.name);
                    const category = menuItem ? menuItem.category : 'Other';

                    if (!categoryBreakdown[category]) {
                        categoryBreakdown[category] = { revenue: 0, orders: 0 };
                    }
                    categoryBreakdown[category].revenue += item.price * item.quantity;
                    categoryBreakdown[category].orders += 1;
                });
            }
        });

        const categoryData = Object.entries(categoryBreakdown)
            .map(([category, data]) => ({
                category: category.charAt(0).toUpperCase() + category.slice(1),
                revenue: data.revenue,
                orders: data.orders,
                percentage: totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0
            }))
            .sort((a, b) => b.revenue - a.revenue);

        // Peak hours analysis
        const hourlyData = {};
        orders.forEach(order => {
            const hour = new Date(order.created_at).getHours();
            if (!hourlyData[hour]) {
                hourlyData[hour] = 0;
            }
            hourlyData[hour]++;
        });

        const maxOrders = Math.max(...Object.values(hourlyData));
        const peakHours = Object.entries(hourlyData)
            .map(([hour, orders]) => ({
                hour: parseInt(hour),
                orders,
                percentage: maxOrders > 0 ? (orders / maxOrders) * 100 : 0
            }))
            .sort((a, b) => b.orders - a.orders)
            .slice(0, 6);

        // Daily trend (last 7 days)
        const dailyTrend = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            const dayOrders = orders.filter(order => {
                const orderDate = new Date(order.created_at).toISOString().split('T')[0];
                return orderDate === dateStr;
            });

            const dayRevenue = dayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);

            dailyTrend.push({
                date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
                revenue: dayRevenue,
                orders: dayOrders.length
            });
        }

        // Profit analysis (estimated)
        const estimatedCosts = totalRevenue * 0.35; // Assume 35% cost ratio
        const estimatedProfit = totalRevenue - estimatedCosts;
        const profitMargin = totalRevenue > 0 ? (estimatedProfit / totalRevenue) * 100 : 0;

        // Inventory costs
        const inventoryCosts = inventoryItems.map(item => {
            const used = item.restockHistory ?
                item.restockHistory.reduce((sum, restock) => sum + restock.quantity, 0) - item.currentStock : 0;

            return {
                name: item.name,
                used: Math.max(0, used),
                unit: item.unit,
                cost: Math.max(0, used) * item.unitPrice
            };
        }).filter(item => item.used > 0);

        const totalIngredientCosts = inventoryCosts.reduce((sum, item) => sum + item.cost, 0);

        // Growth rate (compare with previous period)
        const currentPeriodStart = new Date();
        currentPeriodStart.setDate(currentPeriodStart.getDate() - 7);

        const previousPeriodStart = new Date();
        previousPeriodStart.setDate(previousPeriodStart.getDate() - 14);

        const currentPeriodOrders = orders.filter(order =>
            new Date(order.created_at) >= currentPeriodStart
        );
        const previousPeriodOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= previousPeriodStart && orderDate < currentPeriodStart;
        });

        const currentRevenue = currentPeriodOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const previousRevenue = previousPeriodOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);

        const growthRate = previousRevenue > 0 ?
            ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;

        return {
            totalRevenue,
            totalOrders,
            averageOrder,
            totalCustomers,
            avgCustomersPerOrder,
            topItems,
            categoryBreakdown: categoryData,
            peakHours,
            dailyTrend,
            estimatedCosts,
            estimatedProfit,
            profitMargin,
            inventoryCosts,
            totalIngredientCosts,
            growthRate
        };
    }

    refreshReports() {
        const reportsPage = document.getElementById('reportsPage');
        if (reportsPage && reportsPage.innerHTML.trim() !== '') {
            this.loadReportsPage(reportsPage);
        }
    }

    exportReports() {
        const analytics = this.calculateComprehensiveAnalytics();
        const reportData = {
            generatedAt: new Date().toISOString(),
            analytics: analytics,
            orders: this.getOrders(),
            menuItems: this.getMenuItems(),
            inventoryItems: this.getInventoryItems()
        };

        const dataStr = JSON.stringify(reportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `zaiqa-report-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showNotification('Report exported successfully!', 'success');
    }

    // End of Day Functionality
    isDayFinalized(date) {
        const finalizedDays = JSON.parse(localStorage.getItem('finalizedDays') || '[]');
        return finalizedDays.includes(date);
    }

    showEndOfDayModal() {
        const today = new Date().toISOString().split('T')[0];
        const todayOrders = this.getOrders().filter(order => {
            const orderDate = new Date(order.created_at).toISOString().split('T')[0];
            return orderDate === today;
        });

        const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const expenses = this.getExpenses();
        const automaticExpenses = this.calculateAutomaticExpenses();
        const todayManualExpenses = this.calculateManualExpensesForPeriod(expenses, today, today);
        const todayTotalExpenses = todayManualExpenses + automaticExpenses.daily;
        const netProfit = todayRevenue - todayTotalExpenses;

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content end-of-day-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-calendar-check"></i> End of Day Summary</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="end-of-day-summary">
                        <div class="summary-section">
                            <h4><i class="fas fa-chart-line"></i> Today's Performance</h4>
                            <div class="performance-grid">
                                <div class="performance-item">
                                    <span class="label">Total Sales:</span>
                                    <span class="value positive">PKR ${todayRevenue.toLocaleString()}</span>
                                </div>
                                <div class="performance-item">
                                    <span class="label">Total Orders:</span>
                                    <span class="value">${todayOrders.length}</span>
                                </div>
                                <div class="performance-item">
                                    <span class="label">Manual Expenses:</span>
                                    <span class="value negative">PKR ${todayManualExpenses.toLocaleString()}</span>
                                </div>
                                <div class="performance-item">
                                    <span class="label">Auto Expenses:</span>
                                    <span class="value negative">PKR ${automaticExpenses.daily.toLocaleString()}</span>
                                </div>
                                <div class="performance-item total">
                                    <span class="label">Net Profit:</span>
                                    <span class="value ${netProfit >= 0 ? 'positive' : 'negative'}">PKR ${netProfit.toLocaleString()}</span>
                                </div>
                            </div>
                        </div>

                        <div class="summary-section">
                            <h4><i class="fas fa-hand-holding-usd"></i> Owner Withdrawal</h4>
                            <p class="withdrawal-note">How much money are you taking while leaving?</p>
                            <div class="withdrawal-input">
                                <div class="form-group">
                                    <label for="withdrawalAmount">Amount (PKR)</label>
                                    <input type="number" id="withdrawalAmount" class="form-control" placeholder="0" min="0" max="${Math.max(0, netProfit)}">
                                    <small class="form-text">Maximum available: PKR ${Math.max(0, netProfit).toLocaleString()}</small>
                                </div>
                                <div class="form-group">
                                    <label for="withdrawalNotes">Notes (Optional)</label>
                                    <textarea id="withdrawalNotes" class="form-control" rows="2" placeholder="Reason for withdrawal"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="summary-section">
                            <h4><i class="fas fa-exclamation-triangle"></i> Important Notice</h4>
                            <div class="finalization-warning">
                                <p><strong>Finalizing the day will:</strong></p>
                                <ul>
                                    <li>Lock all today's transactions from further edits</li>
                                    <li>Record the owner withdrawal amount</li>
                                    <li>Generate a permanent daily summary report</li>
                                    <li>Update cash balance calculations</li>
                                </ul>
                                <p class="warning-text"><i class="fas fa-warning"></i> This action cannot be undone!</p>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="app.finalizeDay()">
                            <i class="fas fa-check"></i> Finalize Day
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    finalizeDay() {
        const today = new Date().toISOString().split('T')[0];
        const withdrawalAmount = parseFloat(document.getElementById('withdrawalAmount').value) || 0;
        const withdrawalNotes = document.getElementById('withdrawalNotes').value.trim();

        // Validate withdrawal amount
        const todayOrders = this.getOrders().filter(order => {
            const orderDate = new Date(order.created_at).toISOString().split('T')[0];
            return orderDate === today;
        });

        const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const expenses = this.getExpenses();
        const automaticExpenses = this.calculateAutomaticExpenses();
        const todayManualExpenses = this.calculateManualExpensesForPeriod(expenses, today, today);
        const todayTotalExpenses = todayManualExpenses + automaticExpenses.daily;
        const netProfit = todayRevenue - todayTotalExpenses;

        if (withdrawalAmount > Math.max(0, netProfit)) {
            this.showNotification('Withdrawal amount cannot exceed net profit!', 'error');
            return;
        }

        // Create daily summary record
        const dailySummary = {
            date: today,
            revenue: todayRevenue,
            manualExpenses: todayManualExpenses,
            automaticExpenses: automaticExpenses.daily,
            totalExpenses: todayTotalExpenses,
            netProfit: netProfit,
            ownerWithdrawal: withdrawalAmount,
            withdrawalNotes: withdrawalNotes,
            ordersCount: todayOrders.length,
            finalizedAt: new Date().toISOString(),
            finalizedBy: 'Owner'
        };

        // Save daily summary
        const dailySummaries = JSON.parse(localStorage.getItem('dailySummaries') || '[]');
        dailySummaries.push(dailySummary);
        localStorage.setItem('dailySummaries', JSON.stringify(dailySummaries));

        // Mark day as finalized
        const finalizedDays = JSON.parse(localStorage.getItem('finalizedDays') || '[]');
        finalizedDays.push(today);
        localStorage.setItem('finalizedDays', JSON.stringify(finalizedDays));

        // Record owner withdrawal
        if (withdrawalAmount > 0) {
            const withdrawals = JSON.parse(localStorage.getItem('ownerWithdrawals') || '[]');
            withdrawals.push({
                id: Date.now().toString(),
                date: today,
                amount: withdrawalAmount,
                notes: withdrawalNotes,
                timestamp: new Date().toISOString()
            });
            localStorage.setItem('ownerWithdrawals', JSON.stringify(withdrawals));
        }

        // Close modal and refresh reports
        document.querySelector('.modal-overlay').remove();
        this.loadReportsPage(document.getElementById('reportsPage'));
        this.updateDashboardStats();

        this.showNotification(`Day finalized successfully! ${withdrawalAmount > 0 ? `PKR ${withdrawalAmount.toLocaleString()} withdrawal recorded.` : ''}`, 'success');
    }

    generateEndOfDaySummary(date) {
        const dailySummaries = JSON.parse(localStorage.getItem('dailySummaries') || '[]');
        const summary = dailySummaries.find(s => s.date === date);

        if (!summary) return '';

        return `
            <div class="end-of-day-summary-card">
                <div class="summary-header">
                    <h4><i class="fas fa-calendar-check"></i> Day Finalized - ${new Date(date).toLocaleDateString()}</h4>
                    <span class="finalized-badge">Locked</span>
                </div>
                <div class="summary-content">
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-label">Revenue:</span>
                            <span class="stat-value positive">PKR ${summary.revenue.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Expenses:</span>
                            <span class="stat-value negative">PKR ${summary.totalExpenses.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Net Profit:</span>
                            <span class="stat-value ${summary.netProfit >= 0 ? 'positive' : 'negative'}">PKR ${summary.netProfit.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Owner Withdrawal:</span>
                            <span class="stat-value">PKR ${summary.ownerWithdrawal.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Orders:</span>
                            <span class="stat-value">${summary.ordersCount}</span>
                        </div>
                    </div>
                    ${summary.withdrawalNotes ? `<div class="withdrawal-notes"><strong>Notes:</strong> ${summary.withdrawalNotes}</div>` : ''}
                </div>
            </div>
        `;
    }

    getTotalOwnerWithdrawals() {
        const withdrawals = JSON.parse(localStorage.getItem('ownerWithdrawals') || '[]');
        return withdrawals.reduce((total, withdrawal) => total + (withdrawal.amount || 0), 0);
    }

    // Helper methods for enhanced reports
    generateItemProfitAnalysis() {
        const orders = this.getOrders();
        const menuItems = this.getMenuItems();

        // Calculate sales for each menu item
        const itemSales = {};
        orders.forEach(order => {
            if (order.items) {
                order.items.forEach(item => {
                    if (!itemSales[item.name]) {
                        itemSales[item.name] = { quantity: 0, revenue: 0 };
                    }
                    itemSales[item.name].quantity += item.quantity;
                    itemSales[item.name].revenue += item.price * item.quantity;
                });
            }
        });

        return menuItems.map(menuItem => {
            const sales = itemSales[menuItem.name] || { quantity: 0, revenue: 0 };
            const ingredientCost = this.calculateItemIngredientCost(menuItem);
            const totalCost = ingredientCost * sales.quantity;
            const profit = sales.revenue - totalCost;
            const margin = sales.revenue > 0 ? (profit / sales.revenue) * 100 : 0;

            return `
                <tr>
                    <td>${menuItem.name}</td>
                    <td>PKR ${sales.revenue.toLocaleString()}</td>
                    <td>PKR ${totalCost.toLocaleString()}</td>
                    <td class="${profit >= 0 ? 'profit' : 'loss'}">PKR ${profit.toLocaleString()}</td>
                    <td>${margin.toFixed(1)}%</td>
                    <td>
                        <span class="status-badge ${profit >= 0 ? 'profitable' : 'loss'}">
                            ${profit >= 0 ? 'Profitable' : 'Loss'}
                        </span>
                    </td>
                </tr>
            `;
        }).join('');
    }

    calculateItemIngredientCost(menuItem) {
        if (!menuItem || !menuItem.ingredients) return 0;

        const inventoryItems = this.getInventoryItems();
        let totalCost = 0;

        menuItem.ingredients.forEach(ingredient => {
            // Add null checks for ingredient and ingredient.name
            if (!ingredient || !ingredient.name) {
                console.warn('Invalid ingredient found:', ingredient);
                return;
            }

            const inventoryItem = inventoryItems.find(item => {
                // Add null checks for item and item.name
                if (!item || !item.name || !ingredient.name) {
                    return false;
                }
                return item.name.toLowerCase() === ingredient.name.toLowerCase();
            });

            if (inventoryItem) {
                totalCost += (ingredient.quantity || 1) * (inventoryItem.unitPrice || 0);
            }
        });

        return totalCost;
    }

    calculateStaffCosts() {
        const staff = this.getStaff();
        return staff.reduce((total, member) => {
            return total + (member.salary || 0) + (member.dehari_balance || 0);
        }, 0);
    }

    calculateComprehensiveAnalytics() {
        try {
            const orders = this.getOrders();
            const menuItems = this.getMenuItems();
            const inventoryItems = this.getInventoryItems();

            // Basic calculations with null safety
            const totalRevenue = orders.reduce((sum, order) => {
                const amount = order.total_amount;
                return sum + (amount && !isNaN(amount) ? Number(amount) : 0);
            }, 0);

            const totalOrders = orders.length;
            const totalCustomers = orders.reduce((sum, order) => {
                const count = order.customer_count;
                return sum + (count && !isNaN(count) ? Number(count) : 1);
            }, 0);

            const averageOrder = totalOrders > 0 ? totalRevenue / totalOrders : 0;
            const avgCustomersPerOrder = totalOrders > 0 ? totalCustomers / totalOrders : 0;

        // Calculate estimated costs and profit
        const totalIngredientCosts = this.calculateTotalIngredientCosts(orders);
        const staffCosts = this.calculateStaffCosts();
        const estimatedCosts = totalIngredientCosts + staffCosts + (totalRevenue * 0.15); // 15% operational costs
        const estimatedProfit = totalRevenue - estimatedCosts;
        const profitMargin = totalRevenue > 0 ? (estimatedProfit / totalRevenue) * 100 : 0;

        // Growth rate calculation (mock data for demo)
        const growthRate = Math.random() * 20 - 5; // Random between -5% and 15%

        // Top selling items
        const itemSales = {};
        orders.forEach(order => {
            if (order.items) {
                order.items.forEach(item => {
                    if (!itemSales[item.name]) {
                        itemSales[item.name] = { quantity: 0, revenue: 0 };
                    }
                    itemSales[item.name].quantity += item.quantity;
                    itemSales[item.name].revenue += item.price * item.quantity;
                });
            }
        });

        const topItems = Object.entries(itemSales)
            .map(([name, data]) => ({
                name,
                quantity: data.quantity,
                revenue: data.revenue,
                percentage: totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0
            }))
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 5);

        // Daily trend (last 7 days)
        const dailyTrend = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

            const dayOrders = orders.filter(order => {
                const orderDate = new Date(order.created_at);
                return orderDate.toDateString() === date.toDateString();
            });

            const dayRevenue = dayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
            dailyTrend.push({ date: dateStr, revenue: dayRevenue, orders: dayOrders.length });
        }

        // Peak hours analysis
        const hourlyData = {};
        orders.forEach(order => {
            const hour = new Date(order.created_at).getHours();
            if (!hourlyData[hour]) {
                hourlyData[hour] = 0;
            }
            hourlyData[hour]++;
        });

        const maxOrders = Math.max(...Object.values(hourlyData));
        const peakHours = Object.entries(hourlyData)
            .map(([hour, orders]) => ({
                hour: parseInt(hour),
                orders,
                percentage: maxOrders > 0 ? (orders / maxOrders) * 100 : 0
            }))
            .sort((a, b) => b.orders - a.orders)
            .slice(0, 6);

        // Category breakdown
        const categoryData = {};
        orders.forEach(order => {
            if (order.items) {
                order.items.forEach(item => {
                    const menuItem = menuItems.find(m => m.name === item.name);
                    const category = menuItem?.category || 'Other';

                    if (!categoryData[category]) {
                        categoryData[category] = { revenue: 0, orders: 0 };
                    }
                    categoryData[category].revenue += item.price * item.quantity;
                    categoryData[category].orders++;
                });
            }
        });

        const categoryBreakdown = Object.entries(categoryData)
            .map(([category, data]) => ({
                category,
                revenue: data.revenue,
                orders: data.orders,
                percentage: totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0
            }))
            .sort((a, b) => b.revenue - a.revenue);

        // Inventory costs
        const inventoryCosts = inventoryItems.map(item => ({
            name: item.name,
            cost: (item.quantity || 0) * (item.unitPrice || 0)
        })).sort((a, b) => b.cost - a.cost);

        // Enhanced Analytics - Inventory Stats
        const inventoryStats = this.calculateInventoryStats(inventoryItems);

        // Enhanced Analytics - Financial Health
        const financialHealth = this.calculateFinancialHealth(orders, totalRevenue, totalIngredientCosts, staffCosts);

        // Enhanced Analytics - Customer Analytics
        const customerAnalytics = this.calculateCustomerAnalytics(orders);

            return {
                totalRevenue: totalRevenue || 0,
                totalOrders: totalOrders || 0,
                totalCustomers: totalCustomers || 0,
                averageOrder: averageOrder || 0,
                avgCustomersPerOrder: avgCustomersPerOrder || 0,
                estimatedProfit: estimatedProfit || 0,
                profitMargin: profitMargin || 0,
                growthRate: growthRate || 0,
                topItems: topItems || [],
                dailyTrend: dailyTrend || [],
                peakHours: peakHours || [],
                categoryBreakdown: categoryBreakdown || [],
                totalIngredientCosts: totalIngredientCosts || 0,
                estimatedCosts: estimatedCosts || 0,
                inventoryCosts: inventoryCosts || [],
                inventoryStats: inventoryStats || {},
                financialHealth: financialHealth || {},
                customerAnalytics: customerAnalytics || {}
            };
        } catch (error) {
            console.error('Error calculating analytics:', error);

            // Return safe default values
            return {
                totalRevenue: 0,
                totalOrders: 0,
                totalCustomers: 0,
                averageOrder: 0,
                avgCustomersPerOrder: 0,
                estimatedProfit: 0,
                profitMargin: 0,
                growthRate: 0,
                topItems: [],
                dailyTrend: [],
                peakHours: [],
                categoryBreakdown: [],
                totalIngredientCosts: 0,
                estimatedCosts: 0,
                inventoryCosts: []
            };
        }
    }

    calculateTotalIngredientCosts(orders) {
        try {
            const menuItems = this.getMenuItems();
            const inventoryItems = this.getInventoryItems();
            let totalCost = 0;

            if (!orders || !Array.isArray(orders)) {
                console.warn('Invalid orders data provided to calculateTotalIngredientCosts');
                return 0;
            }

            orders.forEach(order => {
                if (order && order.items && Array.isArray(order.items)) {
                    order.items.forEach(orderItem => {
                        if (!orderItem || !orderItem.name) {
                            console.warn('Invalid order item found:', orderItem);
                            return;
                        }

                        const menuItem = menuItems.find(m => m && m.name === orderItem.name);
                        if (menuItem && menuItem.ingredients && Array.isArray(menuItem.ingredients)) {
                            menuItem.ingredients.forEach(ingredient => {
                                // Add comprehensive null checks for ingredient properties
                                if (!ingredient || !ingredient.name) {
                                    console.warn('Invalid ingredient found in menu item:', ingredient);
                                    return;
                                }

                                const inventoryItem = inventoryItems.find(inv => {
                                    // Add null checks for inventory item and its name
                                    if (!inv || !inv.name || !ingredient.name) {
                                        return false;
                                    }
                                    return inv.name.toLowerCase() === ingredient.name.toLowerCase();
                                });

                                if (inventoryItem) {
                                    const ingredientQuantity = ingredient.quantity || 1;
                                    const unitPrice = inventoryItem.unitPrice || 0;
                                    const orderQuantity = orderItem.quantity || 1;
                                    totalCost += ingredientQuantity * unitPrice * orderQuantity;
                                }
                            });
                        }
                    });
                }
            });

            return totalCost;
        } catch (error) {
            console.error('Error calculating total ingredient costs:', error);
            return 0;
        }
    }

    // Enhanced Analytics Methods
    calculateInventoryStats(inventoryItems) {
        try {
            const usageHistory = this.getInventoryUsageHistory();
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            // Recent usage records
            const recentUsage = usageHistory.filter(record =>
                new Date(record.timestamp) >= thirtyDaysAgo
            );

            // Calculate stats
            const totalItems = inventoryItems.length;
            const totalValue = inventoryItems.reduce((sum, item) =>
                sum + ((item.quantity || 0) * (item.unitPrice || 0)), 0
            );
            const avgItemValue = totalItems > 0 ? totalValue / totalItems : 0;

            // Low stock and out of stock items
            const lowStockItems = inventoryItems.filter(item =>
                (item.quantity || 0) <= (item.minStockLevel || 0) && (item.quantity || 0) > 0
            ).length;
            const outOfStockItems = inventoryItems.filter(item =>
                (item.quantity || 0) === 0
            ).length;

            // Top used items
            const usageByItem = {};
            recentUsage.forEach(record => {
                if (!usageByItem[record.itemName]) {
                    usageByItem[record.itemName] = {
                        name: record.itemName,
                        totalUsed: 0,
                        unit: record.unit,
                        reasons: {}
                    };
                }
                usageByItem[record.itemName].totalUsed += record.quantityUsed;
                usageByItem[record.itemName].reasons[record.reason] =
                    (usageByItem[record.itemName].reasons[record.reason] || 0) + 1;
            });

            const topUsedItems = Object.values(usageByItem)
                .map(item => ({
                    ...item,
                    primaryReason: Object.keys(item.reasons).reduce((a, b) =>
                        item.reasons[a] > item.reasons[b] ? a : b, 'cooking'
                    )
                }))
                .sort((a, b) => b.totalUsed - a.totalUsed)
                .slice(0, 5);

            return {
                totalItems,
                totalValue,
                avgItemValue,
                lowStockItems,
                outOfStockItems,
                usageRecords: recentUsage.length,
                topUsedItems
            };
        } catch (error) {
            console.error('Error calculating inventory stats:', error);
            return {
                totalItems: 0,
                totalValue: 0,
                avgItemValue: 0,
                lowStockItems: 0,
                outOfStockItems: 0,
                usageRecords: 0,
                topUsedItems: []
            };
        }
    }

    calculateFinancialHealth(orders, totalRevenue, ingredientCosts, staffCosts) {
        try {
            const expenses = this.getExpenses();
            const suppliers = this.getSuppliers();

            // Revenue breakdown by service type
            const dineInRevenue = orders
                .filter(order => order.service_type === 'dine-in')
                .reduce((sum, order) => sum + (order.total_amount || 0), 0);
            const takeawayRevenue = orders
                .filter(order => order.service_type === 'takeaway')
                .reduce((sum, order) => sum + (order.total_amount || 0), 0);

            // Expense breakdown
            const supplierPayments = expenses
                .filter(expense => expense.category === 'supplier_payments')
                .reduce((sum, expense) => sum + (expense.amount || 0), 0);

            const otherExpenses = expenses
                .filter(expense => !['supplier_payments'].includes(expense.category))
                .reduce((sum, expense) => sum + (expense.amount || 0), 0);

            const totalExpenses = ingredientCosts + staffCosts + supplierPayments + otherExpenses;
            const grossProfit = totalRevenue - ingredientCosts;
            const netProfit = totalRevenue - totalExpenses;
            const breakEvenPoint = totalExpenses; // Simplified break-even calculation

            return {
                dineInRevenue,
                takeawayRevenue,
                ingredientCosts,
                staffCosts,
                supplierPayments,
                otherExpenses,
                totalExpenses,
                grossProfit,
                netProfit,
                breakEvenPoint
            };
        } catch (error) {
            console.error('Error calculating financial health:', error);
            return {
                dineInRevenue: 0,
                takeawayRevenue: 0,
                ingredientCosts: 0,
                staffCosts: 0,
                supplierPayments: 0,
                otherExpenses: 0,
                totalExpenses: 0,
                grossProfit: 0,
                netProfit: 0,
                breakEvenPoint: 0
            };
        }
    }

    calculateCustomerAnalytics(orders) {
        try {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            const oneMonthAgo = new Date();
            oneMonthAgo.setDate(oneMonthAgo.getDate() - 30);

            // Customer tracking (simplified - using phone numbers or customer names)
            const customerData = {};
            orders.forEach(order => {
                const customerId = order.customer_phone || order.customer_name || `guest_${order.id}`;
                if (!customerData[customerId]) {
                    customerData[customerId] = {
                        orders: [],
                        totalSpent: 0,
                        firstOrder: order.created_at
                    };
                }
                customerData[customerId].orders.push(order);
                customerData[customerId].totalSpent += order.total_amount || 0;
            });

            const totalUniqueCustomers = Object.keys(customerData).length;
            const newCustomersThisWeek = Object.values(customerData).filter(customer =>
                new Date(customer.firstOrder) >= oneWeekAgo
            ).length;

            // Repeat customers (customers with more than 1 order)
            const repeatCustomers = Object.values(customerData).filter(customer =>
                customer.orders.length > 1
            ).length;
            const repeatRate = totalUniqueCustomers > 0 ? (repeatCustomers / totalUniqueCustomers) * 100 : 0;

            // Average order frequency and customer lifetime value
            const avgOrderFrequency = totalUniqueCustomers > 0 ? orders.length / totalUniqueCustomers : 0;
            const customerLifetimeValue = totalUniqueCustomers > 0 ?
                Object.values(customerData).reduce((sum, customer) => sum + customer.totalSpent, 0) / totalUniqueCustomers : 0;

            // Customer segments
            const segments = [
                {
                    name: 'New Customers',
                    count: Object.values(customerData).filter(customer =>
                        new Date(customer.firstOrder) >= oneMonthAgo && customer.orders.length === 1
                    ).length
                },
                {
                    name: 'Regular Customers',
                    count: Object.values(customerData).filter(customer =>
                        customer.orders.length >= 2 && customer.orders.length <= 5
                    ).length
                },
                {
                    name: 'VIP Customers',
                    count: Object.values(customerData).filter(customer =>
                        customer.orders.length > 5 || customer.totalSpent > 5000
                    ).length
                },
                {
                    name: 'Inactive Customers',
                    count: Object.values(customerData).filter(customer => {
                        const lastOrder = Math.max(...customer.orders.map(order => new Date(order.created_at)));
                        return new Date(lastOrder) < oneMonthAgo;
                    }).length
                }
            ];

            // Calculate percentages
            segments.forEach(segment => {
                segment.percentage = totalUniqueCustomers > 0 ? (segment.count / totalUniqueCustomers) * 100 : 0;
            });

            return {
                totalUniqueCustomers,
                newCustomersThisWeek,
                repeatCustomers,
                repeatRate,
                avgOrderFrequency,
                customerLifetimeValue,
                segments
            };
        } catch (error) {
            console.error('Error calculating customer analytics:', error);
            return {
                totalUniqueCustomers: 0,
                newCustomersThisWeek: 0,
                repeatCustomers: 0,
                repeatRate: 0,
                avgOrderFrequency: 0,
                customerLifetimeValue: 0,
                segments: []
            };
        }
    }

    // Enhanced Analytics Modal Methods
    showDetailedInventoryAnalytics() {
        const inventoryItems = this.getInventoryItems();
        const usageHistory = this.getInventoryUsageHistory();
        const analytics = this.calculateInventoryStats(inventoryItems);

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-chart-bar"></i> Detailed Inventory Analytics</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="analytics-tabs">
                        <button class="tab-btn active" onclick="app.switchAnalyticsTab('overview')">Overview</button>
                        <button class="tab-btn" onclick="app.switchAnalyticsTab('usage')">Usage Trends</button>
                        <button class="tab-btn" onclick="app.switchAnalyticsTab('valuation')">Valuation</button>
                        <button class="tab-btn" onclick="app.switchAnalyticsTab('alerts')">Alerts</button>
                    </div>

                    <div class="tab-content" id="analyticsTabContent">
                        ${this.generateInventoryOverviewTab(analytics, inventoryItems)}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    generateInventoryOverviewTab(analytics, inventoryItems) {
        return `
            <div class="inventory-overview">
                <div class="overview-stats">
                    <div class="stat-card">
                        <h4>Total Inventory Value</h4>
                        <div class="stat-value">PKR ${analytics.totalValue.toLocaleString()}</div>
                        <small>${analytics.totalItems} items</small>
                    </div>
                    <div class="stat-card">
                        <h4>Stock Status</h4>
                        <div class="stock-breakdown">
                            <div class="stock-item good">
                                <span>In Stock:</span>
                                <span>${inventoryItems.filter(item => (item.quantity || 0) > (item.minStockLevel || 0)).length}</span>
                            </div>
                            <div class="stock-item warning">
                                <span>Low Stock:</span>
                                <span>${analytics.lowStockItems}</span>
                            </div>
                            <div class="stock-item danger">
                                <span>Out of Stock:</span>
                                <span>${analytics.outOfStockItems}</span>
                            </div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <h4>Usage Activity</h4>
                        <div class="stat-value">${analytics.usageRecords}</div>
                        <small>Records in last 30 days</small>
                    </div>
                </div>

                <div class="inventory-charts">
                    <div class="chart-section">
                        <h5>Top Value Items</h5>
                        <div class="value-items-list">
                            ${inventoryItems
                                .map(item => ({
                                    ...item,
                                    value: (item.quantity || 0) * (item.unitPrice || 0)
                                }))
                                .sort((a, b) => b.value - a.value)
                                .slice(0, 10)
                                .map(item => `
                                    <div class="value-item">
                                        <span class="item-name">${item.name}</span>
                                        <span class="item-quantity">${item.quantity} ${item.unit}</span>
                                        <span class="item-value">PKR ${item.value.toLocaleString()}</span>
                                    </div>
                                `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    showFinancialBreakdown() {
        const analytics = this.calculateComprehensiveAnalytics();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-calculator"></i> Detailed Financial Breakdown</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="financial-breakdown">
                        <div class="breakdown-section">
                            <h4>Revenue Analysis</h4>
                            <div class="breakdown-chart">
                                <div class="chart-item">
                                    <div class="chart-bar revenue">
                                        <div class="bar-fill" style="width: ${analytics.financialHealth.dineInRevenue > 0 ? (analytics.financialHealth.dineInRevenue / analytics.totalRevenue) * 100 : 0}%"></div>
                                    </div>
                                    <div class="chart-label">
                                        <span>Dine-in: PKR ${analytics.financialHealth.dineInRevenue.toLocaleString()}</span>
                                        <span>${analytics.totalRevenue > 0 ? ((analytics.financialHealth.dineInRevenue / analytics.totalRevenue) * 100).toFixed(1) : 0}%</span>
                                    </div>
                                </div>
                                <div class="chart-item">
                                    <div class="chart-bar revenue">
                                        <div class="bar-fill" style="width: ${analytics.financialHealth.takeawayRevenue > 0 ? (analytics.financialHealth.takeawayRevenue / analytics.totalRevenue) * 100 : 0}%"></div>
                                    </div>
                                    <div class="chart-label">
                                        <span>Takeaway: PKR ${analytics.financialHealth.takeawayRevenue.toLocaleString()}</span>
                                        <span>${analytics.totalRevenue > 0 ? ((analytics.financialHealth.takeawayRevenue / analytics.totalRevenue) * 100).toFixed(1) : 0}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="breakdown-section">
                            <h4>Expense Analysis</h4>
                            <div class="expense-breakdown-chart">
                                ${this.generateExpenseBreakdownChart(analytics.financialHealth)}
                            </div>
                        </div>

                        <div class="breakdown-section">
                            <h4>Profitability Metrics</h4>
                            <div class="profitability-metrics">
                                <div class="metric-item">
                                    <span>Gross Profit Margin:</span>
                                    <span class="${analytics.financialHealth.grossProfit >= 0 ? 'positive' : 'negative'}">
                                        ${analytics.totalRevenue > 0 ? ((analytics.financialHealth.grossProfit / analytics.totalRevenue) * 100).toFixed(1) : 0}%
                                    </span>
                                </div>
                                <div class="metric-item">
                                    <span>Net Profit Margin:</span>
                                    <span class="${analytics.financialHealth.netProfit >= 0 ? 'positive' : 'negative'}">
                                        ${analytics.totalRevenue > 0 ? ((analytics.financialHealth.netProfit / analytics.totalRevenue) * 100).toFixed(1) : 0}%
                                    </span>
                                </div>
                                <div class="metric-item">
                                    <span>Break-even Revenue:</span>
                                    <span>PKR ${analytics.financialHealth.breakEvenPoint.toLocaleString()}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    generateExpenseBreakdownChart(financialHealth) {
        const totalExpenses = financialHealth.totalExpenses;
        const expenses = [
            { name: 'Ingredient Costs', amount: financialHealth.ingredientCosts, color: '#ef4444' },
            { name: 'Staff Costs', amount: financialHealth.staffCosts, color: '#f97316' },
            { name: 'Supplier Payments', amount: financialHealth.supplierPayments, color: '#eab308' },
            { name: 'Other Expenses', amount: financialHealth.otherExpenses, color: '#6b7280' }
        ];

        return expenses.map(expense => `
            <div class="expense-chart-item">
                <div class="expense-bar">
                    <div class="expense-fill" style="width: ${totalExpenses > 0 ? (expense.amount / totalExpenses) * 100 : 0}%; background-color: ${expense.color}"></div>
                </div>
                <div class="expense-label">
                    <span>${expense.name}: PKR ${expense.amount.toLocaleString()}</span>
                    <span>${totalExpenses > 0 ? ((expense.amount / totalExpenses) * 100).toFixed(1) : 0}%</span>
                </div>
            </div>
        `).join('');
    }

    showCustomerInsights() {
        const analytics = this.calculateComprehensiveAnalytics();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-user-chart"></i> Customer Insights & Analytics</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="customer-insights">
                        <div class="insights-section">
                            <h4>Customer Segmentation</h4>
                            <div class="segments-chart">
                                ${analytics.customerAnalytics.segments.map(segment => `
                                    <div class="segment-chart-item">
                                        <div class="segment-bar">
                                            <div class="segment-fill" style="width: ${segment.percentage}%"></div>
                                        </div>
                                        <div class="segment-details">
                                            <span class="segment-name">${segment.name}</span>
                                            <span class="segment-stats">${segment.count} customers (${segment.percentage.toFixed(1)}%)</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="insights-section">
                            <h4>Customer Behavior Insights</h4>
                            <div class="behavior-insights">
                                <div class="insight-card">
                                    <h5>Customer Retention</h5>
                                    <div class="insight-metric">
                                        <span class="metric-value">${analytics.customerAnalytics.repeatRate.toFixed(1)}%</span>
                                        <span class="metric-label">Repeat Rate</span>
                                    </div>
                                    <p>Out of ${analytics.customerAnalytics.totalUniqueCustomers} customers, ${analytics.customerAnalytics.repeatCustomers} have made multiple orders.</p>
                                </div>

                                <div class="insight-card">
                                    <h5>Customer Value</h5>
                                    <div class="insight-metric">
                                        <span class="metric-value">PKR ${analytics.customerAnalytics.customerLifetimeValue.toFixed(0)}</span>
                                        <span class="metric-label">Avg Lifetime Value</span>
                                    </div>
                                    <p>Average customer spends PKR ${analytics.customerAnalytics.customerLifetimeValue.toFixed(0)} across ${analytics.customerAnalytics.avgOrderFrequency.toFixed(1)} orders.</p>
                                </div>

                                <div class="insight-card">
                                    <h5>Growth Metrics</h5>
                                    <div class="insight-metric">
                                        <span class="metric-value">${analytics.customerAnalytics.newCustomersThisWeek}</span>
                                        <span class="metric-label">New This Week</span>
                                    </div>
                                    <p>Customer acquisition rate: ${analytics.customerAnalytics.totalUniqueCustomers > 0 ? ((analytics.customerAnalytics.newCustomersThisWeek / analytics.customerAnalytics.totalUniqueCustomers) * 100).toFixed(1) : 0}% growth this week.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    loadSettingsPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h3>System Settings</h3>
            </div>
            
            <div class="settings-sections">
                <div class="settings-card">
                    <h4>Restaurant Information</h4>
                    <div class="form-group">
                        <label>Restaurant Name</label>
                        <input type="text" value="Zaiqa Al-Hayat" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Currency</label>
                        <select class="form-control">
                            <option value="PKR">PKR - Pakistani Rupee</option>
                            <option value="USD">USD - US Dollar</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-card">
                    <h4>Pricing Settings</h4>
                    <div class="form-group">
                        <label>Tax Rate (%)</label>
                        <input type="number" value="16" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Cold Drink Dine-in Markup (PKR)</label>
                        <input type="number" value="15" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Default Per Head Charge (PKR)</label>
                        <input type="number" value="100" class="form-control">
                    </div>
                </div>
            </div>
            
            <div class="settings-actions">
                <button class="btn btn-primary">Save Settings</button>
                <button class="btn btn-outline">Reset to Default</button>
            </div>
        `;
    }

    loadUdharsPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Customer Credit (Udhars)</h3>
                <button class="btn btn-primary" onclick="app.showAddUdharModal()">
                    <i class="fas fa-plus"></i>
                    Add Udhar
                </button>
            </div>

            <div class="udhars-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalCustomers">0</h3>
                            <p>Total Customers</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalUdharAmount">PKR 0</h3>
                            <p>Total Outstanding</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="overdueUdhars">0</h3>
                            <p>Overdue Accounts</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="udhars-list">
                <div class="list-header">
                    <h4>Customer Accounts</h4>
                    <div class="search-filter">
                        <input type="text" placeholder="Search customers..." class="form-control" style="width: 250px;">
                    </div>
                </div>
                <div id="udharsTable" class="udhars-table">
                    ${this.generateUdharsTable()}
                </div>
            </div>
        `;

        this.updateUdharsSummary();
    }

    loadKhataPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Supplier Accounts (Khata)</h3>
                <button class="btn btn-primary" onclick="app.showAddSupplierModal()">
                    <i class="fas fa-plus"></i>
                    Add Supplier
                </button>
            </div>

            <div class="khata-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalSuppliers">0</h3>
                            <p>Total Suppliers</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalPayable">PKR 0</h3>
                            <p>Total Payable</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalReceivable">PKR 0</h3>
                            <p>Total Receivable</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="khata-list">
                <div class="list-header">
                    <h4>Supplier Accounts</h4>
                    <div class="search-filter">
                        <input type="text" placeholder="Search suppliers..." class="form-control" style="width: 250px;">
                    </div>
                </div>
                <div id="khataTable" class="khata-table">
                    ${this.generateKhataTable()}
                </div>
            </div>
        `;

        this.updateKhataSummary();
    }

    loadStaffPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h3>Staff Management</h3>
                <button class="btn btn-primary" onclick="app.showAddStaffModal()">
                    <i class="fas fa-plus"></i>
                    Add Staff Member
                </button>
            </div>

            <div class="staff-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalStaff">0</h3>
                            <p>Total Staff</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalSalaries">PKR 0</h3>
                            <p>Monthly Salaries</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="summary-content">
                            <h3 id="totalStaffPurchases">PKR 0</h3>
                            <p>Staff Purchases</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="staff-list">
                <div class="list-header">
                    <h4>Staff Members</h4>
                    <div class="search-filter">
                        <input type="text" placeholder="Search staff..." class="form-control" style="width: 250px;">
                    </div>
                </div>
                <div id="staffTable" class="staff-table">
                    ${this.generateStaffTable()}
                </div>
            </div>
        `;

        this.updateStaffSummary();
    }

    // Menu Management Methods
    showAddMenuItemModal() {
        const inventoryItems = this.getInventoryItems();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Add New Menu Item</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addMenuItemForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="itemName">Item Name *</label>
                                <input type="text" id="itemName" name="itemName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="itemCategory">Category *</label>
                                <select id="itemCategory" name="itemCategory" class="form-control" required>
                                    <option value="">Select Category</option>
                                    <option value="appetizers">Appetizers</option>
                                    <option value="main">Main Course</option>
                                    <option value="beverages">Beverages</option>
                                    <option value="desserts">Desserts</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="itemDescription">Description</label>
                            <textarea id="itemDescription" name="itemDescription" class="form-control" rows="3"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="dineInPrice">Dine-in Price (PKR) *</label>
                                <input type="number" id="dineInPrice" name="dineInPrice" class="form-control" min="0" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="takeawayPrice">Takeaway Price (PKR) *</label>
                                <input type="number" id="takeawayPrice" name="takeawayPrice" class="form-control" min="0" step="0.01" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="prepTime">Preparation Time (minutes) *</label>
                                <input type="number" id="prepTime" name="prepTime" class="form-control" min="1" required>
                            </div>
                            <div class="form-group">
                                <label for="isAvailable">Availability</label>
                                <select id="isAvailable" name="isAvailable" class="form-control">
                                    <option value="true">Available</option>
                                    <option value="false">Out of Stock</option>
                                </select>
                            </div>
                        </div>

                        <div class="ingredients-section">
                            <h4><i class="fas fa-list"></i> Ingredients</h4>
                            <div id="ingredientsList" class="ingredients-list">
                                <!-- Ingredients will be added here -->
                            </div>
                            <div class="add-ingredient-row">
                                <select id="ingredientSelect" class="form-control">
                                    <option value="">Select Ingredient</option>
                                </select>
                                <input type="number" id="ingredientQuantity" class="form-control" placeholder="Quantity" min="0.01" step="0.01">
                                <button type="button" class="btn btn-primary btn-sm" onclick="app.addIngredientToMenuItem()">
                                    <i class="fas fa-plus"></i> Add
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveMenuItem()">Add Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Populate ingredients dropdown
        const ingredientSelect = document.getElementById('ingredientSelect');
        inventoryItems.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.name} (${item.unit})`;
            option.dataset.unit = item.unit;
            ingredientSelect.appendChild(option);
        });
    }

    addIngredientToMenuItem() {
        const ingredientSelect = document.getElementById('ingredientSelect');
        const quantityInput = document.getElementById('ingredientQuantity');

        const selectedIngredientId = ingredientSelect.value;
        const quantity = parseFloat(quantityInput.value);

        if (!selectedIngredientId || !quantity || quantity <= 0) {
            this.showNotification('Please select an ingredient and enter a valid quantity', 'error');
            return;
        }

        const selectedOption = ingredientSelect.options[ingredientSelect.selectedIndex];
        const ingredientName = selectedOption.textContent;
        const unit = selectedOption.dataset.unit;

        // Check if ingredient already added
        const existingIngredient = document.querySelector(`[data-ingredient-id="${selectedIngredientId}"]`);
        if (existingIngredient) {
            this.showNotification('This ingredient is already added', 'error');
            return;
        }

        // Add ingredient to the list
        const ingredientsList = document.getElementById('ingredientsList');
        const ingredientItem = document.createElement('div');
        ingredientItem.className = 'ingredient-item';
        ingredientItem.dataset.ingredientId = selectedIngredientId;
        ingredientItem.innerHTML = `
            <div class="ingredient-info">
                <span class="ingredient-name">${ingredientName}</span>
                <span class="ingredient-quantity">${quantity} ${unit}</span>
            </div>
            <button type="button" class="btn btn-error btn-sm" onclick="this.closest('.ingredient-item').remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        ingredientsList.appendChild(ingredientItem);

        // Clear inputs
        ingredientSelect.value = '';
        quantityInput.value = '';

        this.showNotification('Ingredient added successfully', 'success');
    }

    saveMenuItem() {
        const form = document.getElementById('addMenuItemForm');
        const formData = new FormData(form);

        // Validate required fields
        if (!formData.get('itemName') || !formData.get('itemCategory') || !formData.get('dineInPrice') || !formData.get('takeawayPrice') || !formData.get('prepTime')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Collect ingredients
        const ingredients = [];
        const ingredientItems = document.querySelectorAll('.ingredient-item');
        ingredientItems.forEach(item => {
            const ingredientId = item.dataset.ingredientId;
            const quantityText = item.querySelector('.ingredient-quantity').textContent;
            const quantity = parseFloat(quantityText.split(' ')[0]);
            const unit = quantityText.split(' ')[1];

            ingredients.push({
                ingredientId: ingredientId,
                quantity: quantity,
                unit: unit
            });
        });

        // Get existing menu items from localStorage
        let menuItems = JSON.parse(localStorage.getItem('menuItems')) || [];

        // Create new menu item
        const newItem = {
            id: Date.now().toString(),
            name: formData.get('itemName'),
            category: formData.get('itemCategory'),
            description: formData.get('itemDescription') || '',
            dineInPrice: parseFloat(formData.get('dineInPrice')),
            takeawayPrice: parseFloat(formData.get('takeawayPrice')),
            preparationTime: parseInt(formData.get('prepTime')),
            ingredients: ingredients,
            isAvailable: formData.get('isAvailable') === 'true',
            createdAt: new Date().toISOString()
        };

        // Add to menu items
        menuItems.push(newItem);

        // Save to localStorage
        localStorage.setItem('menuItems', JSON.stringify(menuItems));

        // Close modal
        document.querySelector('.modal-overlay').remove();

        // Refresh menu display
        this.refreshMenuItems();

        this.showNotification('Menu item added successfully!', 'success');

        // Update menu availability based on ingredients
        this.updateMenuAvailability();
    }

    // Dynamic Menu Availability Methods
    updateMenuAvailability() {
        const menuItems = this.getMenuItems();
        const inventoryItems = this.getInventoryItems();
        let updated = false;

        menuItems.forEach(menuItem => {
            const wasAvailable = menuItem.isAvailable;
            const isNowAvailable = this.checkMenuItemAvailability(menuItem, inventoryItems);

            if (wasAvailable !== isNowAvailable) {
                menuItem.isAvailable = isNowAvailable;
                menuItem.availabilityReason = isNowAvailable ? 'Available' : this.getUnavailabilityReason(menuItem, inventoryItems);
                updated = true;
            }
        });

        if (updated) {
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            this.refreshMenuItems();
            this.refreshPOSMenu();
        }
    }

    checkMenuItemAvailability(menuItem, inventoryItems) {
        if (!menuItem.ingredients || menuItem.ingredients.length === 0) {
            return true; // Items without ingredients are always available
        }

        return menuItem.ingredients.every(ingredient => {
            const inventoryItem = inventoryItems.find(inv => inv.id === ingredient.ingredientId);
            if (!inventoryItem) return false;

            return inventoryItem.currentStock >= ingredient.quantity;
        });
    }

    getUnavailabilityReason(menuItem, inventoryItems) {
        if (!menuItem.ingredients || menuItem.ingredients.length === 0) {
            return 'Available';
        }

        const unavailableIngredients = [];
        menuItem.ingredients.forEach(ingredient => {
            const inventoryItem = inventoryItems.find(inv => inv.id === ingredient.ingredientId);
            if (!inventoryItem || inventoryItem.currentStock < ingredient.quantity) {
                const itemName = inventoryItem ? inventoryItem.name : 'Unknown ingredient';
                unavailableIngredients.push(itemName);
            }
        });

        if (unavailableIngredients.length > 0) {
            return `Out of stock: ${unavailableIngredients.join(', ')}`;
        }

        return 'Available';
    }

    deductIngredientsFromInventory(menuItem, quantity = 1) {
        if (!menuItem.ingredients || menuItem.ingredients.length === 0) {
            return; // No ingredients to deduct
        }

        const inventoryItems = this.getInventoryItems();
        let updated = false;

        menuItem.ingredients.forEach(ingredient => {
            const inventoryItem = inventoryItems.find(inv => inv.id === ingredient.ingredientId);
            if (inventoryItem) {
                const deductAmount = ingredient.quantity * quantity;
                inventoryItem.currentStock = Math.max(0, inventoryItem.currentStock - deductAmount);
                inventoryItem.lastUpdated = new Date().toISOString();
                updated = true;
            }
        });

        if (updated) {
            localStorage.setItem('inventoryItems', JSON.stringify(inventoryItems));
            this.updateMenuAvailability(); // Check if any menu items became unavailable
            this.updateInventoryAlerts(); // Update inventory alerts
        }
    }

    refreshPOSMenu() {
        const posMenuGrid = document.getElementById('posMenuGrid');
        if (posMenuGrid) {
            posMenuGrid.innerHTML = this.generatePOSMenuItems();
        }
    }

    filterMenuItems(category) {
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Filter and display items
        this.refreshMenuItems(category);
    }

    refreshMenuItems(filterCategory = 'all') {
        const menuGrid = document.getElementById('menuItemsGrid');
        if (menuGrid) {
            menuGrid.innerHTML = this.generateMenuItems(filterCategory);
        }
    }

    // POS System Methods
    showPOSSystem() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay pos-modal';
        modal.innerHTML = this.generatePOSHTML();
        document.body.appendChild(modal);

        // Initialize POS functionality
        this.initializePOS();

        // Add event listeners for discount controls
        setTimeout(() => {
            const discountType = document.getElementById('discountType');
            if (discountType) {
                discountType.addEventListener('change', () => {
                    this.updateDiscountDisplay();
                });
            }
        }, 100);
    }

    generatePOSHTML() {
        return `
            <div class="modal-content pos-modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-cash-register"></i> Zaiqa POS System</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="pos-container">
                    <div class="pos-menu">
                        <div class="menu-categories">
                            <button class="category-btn active" onclick="app.filterPOSItems('all')">All</button>
                            <button class="category-btn" onclick="app.filterPOSItems('appetizers')">Appetizers</button>
                            <button class="category-btn" onclick="app.filterPOSItems('main')">Main Course</button>
                            <button class="category-btn" onclick="app.filterPOSItems('beverages')">Beverages</button>
                            <button class="category-btn" onclick="app.filterPOSItems('desserts')">Desserts</button>
                        </div>

                        <div class="menu-grid" id="posMenuGrid">
                            ${this.generatePOSMenuItems()}
                        </div>
                    </div>

                    <div class="pos-cart">
                        <h3><i class="fas fa-shopping-cart"></i> Current Order</h3>

                        <div class="service-type" style="margin: 1rem 0;">
                            <label>Service Type:</label>
                            <select id="serviceType" class="form-control" onchange="app.updatePOSPricing()">
                                <option value="takeaway" selected>Take Away</option>
                                <option value="dine_in">Dine In</option>
                            </select>
                        </div>

                        <div id="cartItems" class="cart-items"></div>

                        <!-- Additional Charges Section -->
                        <div class="additional-charges-section">
                            <h4><i class="fas fa-plus-circle"></i> Additional Charges</h4>
                            <div class="charge-input-group">
                                <input type="text" id="chargeName" class="form-control" placeholder="Charge description (e.g., Service Charge)">
                                <input type="number" id="chargeAmount" class="form-control" placeholder="Amount (PKR)" min="0">
                                <button class="btn btn-sm btn-primary" onclick="app.addAdditionalCharge()">
                                    <i class="fas fa-plus"></i> Add
                                </button>
                            </div>
                            <div id="additionalChargesList" class="charges-list"></div>
                        </div>

                        <!-- Discount Section -->
                        <div class="discount-section">
                            <h4><i class="fas fa-percentage"></i> Discount</h4>
                            <div class="discount-controls">
                                <select id="discountType" class="form-control">
                                    <option value="none">No Discount</option>
                                    <option value="percentage">Percentage (%)</option>
                                    <option value="fixed">Fixed Amount (PKR)</option>
                                </select>
                                <input type="number" id="discountValue" class="form-control" placeholder="Value" min="0" disabled>
                                <input type="text" id="discountReason" class="form-control" placeholder="Reason (optional)" disabled>
                            </div>
                            <button class="btn btn-sm btn-outline" onclick="app.applyDiscount()" id="applyDiscountBtn" disabled>
                                <i class="fas fa-check"></i> Apply
                            </button>
                            <div id="discountDisplay" class="discount-display"></div>
                        </div>

                        <div class="cart-total">
                            <div id="totalBreakdown"></div>
                        </div>

                        <div class="payment-section">
                            <h4>Payment Method</h4>
                            <div class="payment-methods">
                                <button class="payment-btn active" onclick="app.selectPOSPayment('cash')">
                                    <i class="fas fa-money-bill"></i><br>Cash
                                </button>
                                <button class="payment-btn" onclick="app.selectPOSPayment('card')">
                                    <i class="fas fa-credit-card"></i><br>Card
                                </button>
                                <button class="payment-btn" onclick="app.selectPOSPayment('mobile')">
                                    <i class="fas fa-mobile-alt"></i><br>Mobile
                                </button>
                                <button class="payment-btn" onclick="app.selectPOSPayment('other')">
                                    <i class="fas fa-ellipsis-h"></i><br>Other
                                </button>
                            </div>

                            <button class="btn btn-primary" style="width: 100%; margin-top: 1rem;" onclick="app.processPOSPayment()">
                                <i class="fas fa-check"></i> Process Payment
                            </button>

                            <button class="btn btn-outline" style="width: 100%; margin-top: 0.5rem;" onclick="app.clearPOSCart()">
                                <i class="fas fa-trash"></i> Clear Order
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;
    }

    initializePOS() {
        this.posCart = [];
        this.selectedPayment = 'cash';
        this.additionalCharges = [];
        this.discount = { type: 'none', value: 0, reason: '' };
        this.updatePOSCartDisplay();

        // Ensure payment method is properly initialized
        setTimeout(() => {
            const cashBtn = document.querySelector('.payment-btn[onclick*="cash"]');
            if (cashBtn) {
                cashBtn.classList.add('active');
            }
        }, 100);
    }

    addToPOSCart(itemId, name, price, isColdDrink = false) {
        const serviceType = document.getElementById('serviceType').value;
        let finalPrice = price;

        // Apply cold drink markup for dine-in
        if (isColdDrink && serviceType === 'dine_in') {
            finalPrice += 15;
        }

        const existingItem = this.posCart.find(item => item.id === itemId);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.posCart.push({
                id: itemId,
                name: name,
                basePrice: price,
                price: finalPrice,
                quantity: 1,
                serviceType: serviceType,
                isColdDrink: isColdDrink
            });
        }

        this.updatePOSCartDisplay();
    }

    updatePOSQuantity(itemId, change) {
        const item = this.posCart.find(item => item.id === itemId);
        if (item) {
            item.quantity += change;
            if (item.quantity <= 0) {
                this.removeFromPOSCart(itemId);
            } else {
                this.updatePOSCartDisplay();
            }
        }
    }

    removeFromPOSCart(itemId) {
        this.posCart = this.posCart.filter(item => item.id !== itemId);
        this.updatePOSCartDisplay();
    }

    updatePOSCartDisplay() {
        const cartItems = document.getElementById('cartItems');

        if (this.posCart.length === 0) {
            cartItems.innerHTML = '<p style="text-align: center; color: #64748b; padding: 2rem;">No items in cart</p>';
        } else {
            cartItems.innerHTML = this.posCart.map(item => `
                <div class="cart-item">
                    <div>
                        <div style="font-weight: 500;">${item.name}</div>
                        <div style="font-size: 0.875rem; color: #64748b;">PKR ${item.price} each</div>
                    </div>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="app.updatePOSQuantity('${item.id}', -1)">-</button>
                        <span style="margin: 0 0.5rem;">${item.quantity}</span>
                        <button class="quantity-btn" onclick="app.updatePOSQuantity('${item.id}', 1)">+</button>
                        <button class="quantity-btn" style="background: #dc2626; margin-left: 0.5rem;" onclick="app.removeFromPOSCart('${item.id}')">×</button>
                    </div>
                </div>
            `).join('');
        }

        this.updatePOSTotals();
    }

    updatePOSTotals() {
        const itemsSubtotal = this.posCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // Add per-head charge if this is from a table order
        let perHeadCharge = 0;
        const currentTableOrder = this.getCurrentTableOrder();
        const serviceType = document.getElementById('serviceType')?.value || 'takeaway';

        if (currentTableOrder && currentTableOrder.customer_count && serviceType === 'dine_in') {
            perHeadCharge = currentTableOrder.customer_count * 100; // 100 PKR per head
        }

        // Add additional charges
        const additionalChargesTotal = this.additionalCharges.reduce((sum, charge) => sum + charge.amount, 0);

        // Calculate subtotal before discount
        const subtotalBeforeDiscount = itemsSubtotal + perHeadCharge + additionalChargesTotal;

        // Apply discount
        let discountAmount = 0;
        if (this.discount.type === 'percentage') {
            discountAmount = (subtotalBeforeDiscount * this.discount.value) / 100;
        } else if (this.discount.type === 'fixed') {
            discountAmount = this.discount.value;
        }

        // Ensure discount doesn't exceed subtotal
        discountAmount = Math.min(discountAmount, subtotalBeforeDiscount);

        const subtotalAfterDiscount = subtotalBeforeDiscount - discountAmount;
        const tax = subtotalAfterDiscount * 0.16;
        const total = subtotalAfterDiscount + tax;

        // Update the breakdown display
        this.updateTotalBreakdown(itemsSubtotal, perHeadCharge, additionalChargesTotal, discountAmount, tax, total);
    }

    updateTotalBreakdown(itemsSubtotal, perHeadCharge, additionalChargesTotal, discountAmount, tax, total) {
        const breakdown = document.getElementById('totalBreakdown');

        let html = `
            <div class="total-row"><span>Items Subtotal:</span> <span>PKR ${itemsSubtotal.toFixed(0)}</span></div>
        `;

        // Show per-head charge if applicable
        if (perHeadCharge > 0) {
            const currentTableOrder = this.getCurrentTableOrder();
            html += `
                <div class="total-row"><span>Per Head Charge (${currentTableOrder.customer_count} × PKR 100):</span> <span>PKR ${perHeadCharge.toFixed(0)}</span></div>
            `;
        }

        // Show additional charges
        this.additionalCharges.forEach(charge => {
            html += `
                <div class="total-row"><span>${charge.name}:</span> <span>PKR ${charge.amount.toFixed(0)}</span></div>
            `;
        });

        // Show discount if applicable
        if (discountAmount > 0) {
            html += `
                <div class="total-row discount-row"><span>Discount (${this.discount.reason}):</span> <span>-PKR ${discountAmount.toFixed(0)}</span></div>
            `;
        }

        html += `
            <div class="total-row"><span>Subtotal:</span> <span>PKR ${(itemsSubtotal + perHeadCharge + additionalChargesTotal - discountAmount).toFixed(0)}</span></div>
            <div class="total-row"><span>Tax (16%):</span> <span>PKR ${tax.toFixed(0)}</span></div>
            <div class="total-row total-final"><strong><span>TOTAL:</span> <span>PKR ${total.toFixed(0)}</span></strong></div>
        `;

        breakdown.innerHTML = html;
    }

    updatePOSPricing() {
        const serviceType = document.getElementById('serviceType').value;
        this.posCart.forEach(item => {
            if (item.isColdDrink) {
                if (serviceType === 'dine_in') {
                    item.price = item.basePrice + 15;
                } else {
                    item.price = item.basePrice;
                }
            }
        });
        this.updatePOSCartDisplay();
    }

    selectPOSPayment(method) {
        this.selectedPayment = method;
        document.querySelectorAll('.payment-btn').forEach(btn => btn.classList.remove('active'));
        event.target.closest('.payment-btn').classList.add('active');
    }

    filterPOSItems(category) {
        document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        const items = document.querySelectorAll('.menu-item');
        items.forEach(item => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    processPOSPayment() {
        if (this.posCart.length === 0) {
            this.showNotification('Cart is empty!', 'error');
            return;
        }

        const itemsSubtotal = this.posCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // Add per-head charge if this is from a table order
        let perHeadCharge = 0;
        const currentTableOrder = this.getCurrentTableOrder();
        const serviceType = document.getElementById('serviceType').value;

        if (currentTableOrder && currentTableOrder.customer_count && serviceType === 'dine_in') {
            perHeadCharge = currentTableOrder.customer_count * 100; // 100 PKR per head
        }

        // Add additional charges
        const additionalChargesTotal = this.additionalCharges.reduce((sum, charge) => sum + charge.amount, 0);

        // Calculate subtotal before discount
        const subtotalBeforeDiscount = itemsSubtotal + perHeadCharge + additionalChargesTotal;

        // Apply discount
        let discountAmount = 0;
        if (this.discount.type === 'percentage') {
            discountAmount = (subtotalBeforeDiscount * this.discount.value) / 100;
        } else if (this.discount.type === 'fixed') {
            discountAmount = this.discount.value;
        }

        // Ensure discount doesn't exceed subtotal
        discountAmount = Math.min(discountAmount, subtotalBeforeDiscount);

        const subtotalAfterDiscount = subtotalBeforeDiscount - discountAmount;
        const tax = subtotalAfterDiscount * 0.16;
        const total = subtotalAfterDiscount + tax;
        const orderNumber = 'ZQ' + Date.now().toString().slice(-6);

        // Update inventory for ordered items (deduct ingredients)
        this.updateInventoryFromOrder(this.posCart);

        // Save order to localStorage
        this.savePOSOrder(orderNumber, total, perHeadCharge, additionalChargesTotal, discountAmount);

        // Generate and show receipt
        this.showPOSReceipt(orderNumber, total, perHeadCharge, additionalChargesTotal, discountAmount);

        // Clear table if this was a table order
        if (currentTableOrder) {
            this.clearTableAfterPayment(currentTableOrder.table_id);
        }

        // Clear cart
        this.clearPOSCart();

        this.showNotification(`Payment processed successfully! Order Number: ${orderNumber}`, 'success');

        // Update dashboard stats
        this.updateDashboardStats();
    }

    clearPOSCart() {
        this.posCart = [];
        this.additionalCharges = [];
        this.discount = { type: 'none', value: 0, reason: '' };
        this.currentTableOrder = null; // Clear table order reference
        this.updatePOSCartDisplay();
        this.updateAdditionalChargesDisplay();
        this.updateDiscountDisplay();
    }

    getCurrentTableOrder() {
        return this.currentTableOrder || null;
    }

    setCurrentTableOrder(order) {
        this.currentTableOrder = order;
    }

    // Additional Charges Methods
    addAdditionalCharge() {
        const chargeName = document.getElementById('chargeName').value.trim();
        const chargeAmount = parseFloat(document.getElementById('chargeAmount').value);

        if (!chargeName || !chargeAmount || chargeAmount <= 0) {
            this.showNotification('Please enter valid charge name and amount', 'error');
            return;
        }

        const newCharge = {
            id: Date.now().toString(),
            name: chargeName,
            amount: chargeAmount
        };

        this.additionalCharges.push(newCharge);

        // Clear inputs
        document.getElementById('chargeName').value = '';
        document.getElementById('chargeAmount').value = '';

        this.updateAdditionalChargesDisplay();
        this.updatePOSTotals();
        this.showNotification(`Added ${chargeName}: PKR ${chargeAmount}`, 'success');
    }

    removeAdditionalCharge(chargeId) {
        this.additionalCharges = this.additionalCharges.filter(charge => charge.id !== chargeId);
        this.updateAdditionalChargesDisplay();
        this.updatePOSTotals();
    }

    updateAdditionalChargesDisplay() {
        const chargesList = document.getElementById('additionalChargesList');

        if (this.additionalCharges.length === 0) {
            chargesList.innerHTML = '';
        } else {
            chargesList.innerHTML = this.additionalCharges.map(charge => `
                <div class="charge-item">
                    <span>${charge.name}</span>
                    <span>PKR ${charge.amount.toFixed(0)}</span>
                    <button class="btn-icon delete" onclick="app.removeAdditionalCharge('${charge.id}')" title="Remove">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }
    }

    // Discount Methods
    applyDiscount() {
        const discountType = document.getElementById('discountType').value;
        const discountValue = parseFloat(document.getElementById('discountValue').value);
        const discountReason = document.getElementById('discountReason').value.trim();

        if (discountType === 'none') {
            this.discount = { type: 'none', value: 0, reason: '' };
        } else if (!discountValue || discountValue <= 0) {
            this.showNotification('Please enter a valid discount value', 'error');
            return;
        } else {
            this.discount = {
                type: discountType,
                value: discountValue,
                reason: discountReason || 'Discount applied'
            };
        }

        this.updateDiscountDisplay();
        this.updatePOSTotals();

        if (discountType !== 'none') {
            this.showNotification(`Discount applied: ${discountType === 'percentage' ? discountValue + '%' : 'PKR ' + discountValue}`, 'success');
        }
    }

    updateDiscountDisplay() {
        const discountDisplay = document.getElementById('discountDisplay');
        const discountType = document.getElementById('discountType');
        const discountValue = document.getElementById('discountValue');
        const discountReason = document.getElementById('discountReason');
        const applyBtn = document.getElementById('applyDiscountBtn');

        // Enable/disable inputs based on discount type
        if (discountType.value === 'none') {
            discountValue.disabled = true;
            discountReason.disabled = true;
            applyBtn.disabled = true;
            discountDisplay.innerHTML = '';
        } else {
            discountValue.disabled = false;
            discountReason.disabled = false;
            applyBtn.disabled = false;
        }

        // Show current discount
        if (this.discount.type !== 'none' && this.discount.value > 0) {
            const discountText = this.discount.type === 'percentage'
                ? `${this.discount.value}% discount`
                : `PKR ${this.discount.value} discount`;

            discountDisplay.innerHTML = `
                <div class="current-discount">
                    <span><i class="fas fa-tag"></i> ${discountText}</span>
                    <span>${this.discount.reason}</span>
                    <button class="btn-icon delete" onclick="app.removeDiscount()" title="Remove">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }
    }

    removeDiscount() {
        this.discount = { type: 'none', value: 0, reason: '' };
        document.getElementById('discountType').value = 'none';
        document.getElementById('discountValue').value = '';
        document.getElementById('discountReason').value = '';
        this.updateDiscountDisplay();
        this.updatePOSTotals();
    }

    clearTableAfterPayment(tableId) {
        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === tableId);

        if (tableIndex !== -1) {
            tables[tableIndex].status = 'available';
            tables[tableIndex].customers = 0;
            tables[tableIndex].currentOrder = null;

            localStorage.setItem('restaurantTables', JSON.stringify(tables));

            // Refresh tables page if it's currently open
            const tablesPage = document.getElementById('tablesPage');
            if (tablesPage && tablesPage.innerHTML.trim() !== '') {
                this.loadTablesPage(tablesPage);
            }
        }
    }

    updateInventoryFromOrder(cartItems) {
        const menuItems = this.getMenuItems();

        cartItems.forEach(cartItem => {
            const menuItem = menuItems.find(m => m.id === cartItem.id);
            if (menuItem) {
                // Use the new ingredient deduction system
                this.deductIngredientsFromInventory(menuItem, cartItem.quantity);
            }
        });
    }

    savePOSOrder(orderNumber, total, perHeadCharge = 0, additionalChargesTotal = 0, discountAmount = 0) {
        const orders = this.getOrders();
        const currentTableOrder = this.getCurrentTableOrder();

        const newOrder = {
            id: Date.now().toString(),
            order_number: orderNumber,
            items: this.posCart,
            total_amount: total,
            per_head_charge: perHeadCharge,
            additional_charges: [...this.additionalCharges],
            additional_charges_total: additionalChargesTotal,
            discount: { ...this.discount },
            discount_amount: discountAmount,
            customer_count: currentTableOrder ? currentTableOrder.customer_count : 1,
            table_id: currentTableOrder ? currentTableOrder.table_id : null,
            table_number: currentTableOrder ? currentTableOrder.table_number : 'Takeaway',
            payment_method: this.selectedPayment,
            service_type: document.getElementById('serviceType').value,
            status: 'completed',
            created_at: new Date().toISOString()
        };

        orders.push(newOrder);
        localStorage.setItem('restaurantOrders', JSON.stringify(orders));
    }

    showPOSReceipt(orderNumber, total, perHeadCharge = 0, additionalChargesTotal = 0, discountAmount = 0) {
        const currentTableOrder = this.getCurrentTableOrder();
        const itemsSubtotal = this.posCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const subtotalBeforeDiscount = itemsSubtotal + perHeadCharge + additionalChargesTotal;
        const subtotalAfterDiscount = subtotalBeforeDiscount - discountAmount;
        const tax = subtotalAfterDiscount * 0.16;
        const now = new Date();

        const receiptHTML = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt - ${orderNumber}</title>
                <style>
                    body { font-family: monospace; width: 300px; margin: 0 auto; padding: 20px; }
                    .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px; }
                    .item { display: flex; justify-content: space-between; margin: 5px 0; }
                    .total { border-top: 1px solid #000; padding-top: 10px; margin-top: 10px; font-weight: bold; }
                    .discount { color: #dc2626; }
                    .final-total {
                        font-size: 18px;
                        font-weight: bold;
                        border: 2px solid #000;
                        padding: 8px;
                        margin: 10px 0;
                        background: #f0f0f0;
                        text-align: center;
                    }
                    .final-total .amount {
                        font-size: 22px;
                        color: #fb923c;
                        font-weight: 900;
                    }
                    @media print { body { width: auto; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="logo">
                        <svg width="60" height="60" viewBox="0 0 100 100" style="margin: 0 auto 10px auto; display: block;">
                            <circle cx="50" cy="50" r="45" fill="#fb923c" stroke="#ea580c" stroke-width="3"/>
                            <text x="50" y="35" text-anchor="middle" fill="white" font-family="serif" font-size="16" font-weight="bold">زائقہ</text>
                            <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">ZAIQA</text>
                            <text x="50" y="70" text-anchor="middle" fill="white" font-family="Arial" font-size="8">RESTAURANT</text>
                        </svg>
                    </div>
                    <h2>ZAIQA AL-HAYAT</h2>
                    <p style="margin: 5px 0; font-size: 12px;">Authentic Pakistani Cuisine</p>
                    <p>Order #${orderNumber}</p>
                    <p>${now.toLocaleString()}</p>
                    ${currentTableOrder ? `<p>Table: ${currentTableOrder.table_number}</p>` : ''}
                    ${currentTableOrder ? `<p>Customers: ${currentTableOrder.customer_count}</p>` : ''}
                </div>

                <div class="items">
                    ${this.posCart.map(item => `
                        <div class="item">
                            <span>${item.name} x${item.quantity}</span>
                            <span>PKR ${(item.price * item.quantity).toFixed(0)}</span>
                        </div>
                    `).join('')}
                </div>

                <div class="total">
                    <div class="item">
                        <span>Items Subtotal:</span>
                        <span>PKR ${itemsSubtotal.toFixed(0)}</span>
                    </div>
                    ${perHeadCharge > 0 ? `
                        <div class="item">
                            <span>Per Head Charge (${currentTableOrder.customer_count} × PKR 100):</span>
                            <span>PKR ${perHeadCharge.toFixed(0)}</span>
                        </div>
                    ` : ''}
                    ${this.additionalCharges.map(charge => `
                        <div class="item">
                            <span>${charge.name}:</span>
                            <span>PKR ${charge.amount.toFixed(0)}</span>
                        </div>
                    `).join('')}
                    ${discountAmount > 0 ? `
                        <div class="item discount">
                            <span>Discount (${this.discount.reason}):</span>
                            <span>-PKR ${discountAmount.toFixed(0)}</span>
                        </div>
                    ` : ''}
                    <div class="item">
                        <span>Subtotal:</span>
                        <span>PKR ${subtotalAfterDiscount.toFixed(0)}</span>
                    </div>
                    <div class="item">
                        <span>Tax (16%):</span>
                        <span>PKR ${tax.toFixed(0)}</span>
                    </div>
                </div>

                <div class="final-total">
                    <div>TOTAL AMOUNT</div>
                    <div class="amount">PKR ${total.toFixed(0)}</div>
                </div>

                <div class="total">
                    <div class="item">
                        <span>Payment:</span>
                        <span>${this.selectedPayment.toUpperCase()}</span>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px; font-size: 12px;">
                    <p>Thank you for dining with us!</p>
                    <p>Visit us again soon</p>
                </div>
            </body>
            </html>
        `;

        const receiptWindow = window.open('', '_blank', 'width=400,height=600');
        receiptWindow.document.write(receiptHTML);
        receiptWindow.document.close();
        receiptWindow.print();
    }

    generatePOSMenuItems() {
        const menuItems = this.getMenuItems();
        const inventoryItems = this.getInventoryItems();

        // Update availability before displaying
        this.updateMenuAvailability();

        return menuItems.map(item => {
            const isAvailable = this.checkMenuItemAvailability(item, inventoryItems);
            const serviceType = document.getElementById('serviceType')?.value || 'dine_in';
            const price = serviceType === 'dine_in' ? item.dineInPrice : item.takeawayPrice;

            return `
                <div class="menu-item ${!isAvailable ? 'unavailable' : ''}"
                     data-category="${item.category}"
                     ${isAvailable ? `onclick="app.addToPOSCart('${item.id}', '${item.name}', ${price})"` : ''}>
                    <h4>${item.name}</h4>
                    <p style="font-size: 0.875rem; color: #64748b; margin: 0.5rem 0;">${item.description}</p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: 600; color: var(--primary-color);">PKR ${price}</span>
                        ${!isAvailable ? '<span style="font-size: 0.75rem; background: #fee2e2; color: #dc2626; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">Unavailable</span>' : ''}
                    </div>
                    <div style="margin-top: 0.5rem; font-size: 0.75rem; color: #64748b;">
                        <i class="fas fa-clock"></i> ${item.preparationTime} mins
                        ${!isAvailable ? `<br><i class="fas fa-exclamation-triangle"></i> ${this.getUnavailabilityReason(item, inventoryItems)}` : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    getMenuItems() {
        // Get from localStorage or return default items
        const savedItems = localStorage.getItem('menuItems');
        if (savedItems) {
            return JSON.parse(savedItems);
        }

        // Default menu items with ingredients for inventory integration
        const defaultItems = [
            {
                id: '1',
                name: 'Chicken Tikka',
                category: 'appetizers',
                description: 'Grilled chicken pieces with spices',
                basePrice: 450,
                preparationTime: 20,
                isColdDrink: false,
                isAvailable: true,
                ingredients: [
                    { name: 'Chicken (Fresh)', quantity: 0.25 }, // 250g per serving
                    { name: 'Cooking Oil', quantity: 0.03 } // 30ml
                ]
            },
            {
                id: '2',
                name: 'Chicken Karhai (1kg)',
                category: 'main',
                description: 'Traditional chicken karhai for 4-6 people',
                basePrice: 1200,
                preparationTime: 30,
                isColdDrink: false,
                isAvailable: true,
                ingredients: [
                    { name: 'Chicken (Fresh)', quantity: 1.0 }, // 1kg
                    { name: 'Tomatoes', quantity: 4 }, // 4 pieces
                    { name: 'Onions', quantity: 2 }, // 2 pieces
                    { name: 'Cooking Oil', quantity: 0.15 } // 150ml
                ]
            },
            {
                id: '3',
                name: 'Coca Cola',
                category: 'beverages',
                description: 'Chilled soft drink',
                basePrice: 80,
                preparationTime: 2,
                isColdDrink: true,
                isAvailable: true,
                ingredients: [
                    { name: 'Coca Cola Bottles', quantity: 1 } // 1 bottle
                ]
            },
            {
                id: '4',
                name: 'Chicken Biryani',
                category: 'main',
                description: 'Aromatic basmati rice with tender chicken',
                basePrice: 420,
                preparationTime: 35,
                isColdDrink: false,
                isAvailable: true,
                ingredients: [
                    { name: 'Chicken (Fresh)', quantity: 0.3 }, // 300g
                    { name: 'Basmati Rice', quantity: 0.2 }, // 200g
                    { name: 'Onions', quantity: 1 }, // 1 piece
                    { name: 'Cooking Oil', quantity: 0.05 } // 50ml
                ]
            },
            {
                id: '5',
                name: 'Kheer',
                category: 'desserts',
                description: 'Traditional rice pudding',
                basePrice: 180,
                preparationTime: 5,
                isColdDrink: false,
                isAvailable: true,
                ingredients: [
                    { name: 'Milk', quantity: 0.25 }, // 250ml
                    { name: 'Basmati Rice', quantity: 0.05 }, // 50g
                    { name: 'Sugar', quantity: 0.03 } // 30g
                ]
            }
        ];

        // Save to localStorage for future use
        localStorage.setItem('menuItems', JSON.stringify(defaultItems));
        return defaultItems;
    }

    // Calculator Methods
    showCalculator() {
        const calculator = document.createElement('div');
        calculator.className = 'calculator-widget';
        calculator.innerHTML = `
            <div class="calculator-header">
                <span>Calculator</span>
                <button onclick="this.closest('.calculator-widget').remove()" class="calc-close">×</button>
            </div>
            <div class="calculator-display">
                <input type="text" id="calcDisplay" readonly value="0">
            </div>
            <div class="calculator-buttons">
                <button onclick="clearCalc()" class="calc-btn calc-clear">C</button>
                <button onclick="calcOperation('/')" class="calc-btn calc-operator">÷</button>
                <button onclick="calcOperation('*')" class="calc-btn calc-operator">×</button>
                <button onclick="deleteLast()" class="calc-btn calc-operator">⌫</button>

                <button onclick="calcNumber('7')" class="calc-btn">7</button>
                <button onclick="calcNumber('8')" class="calc-btn">8</button>
                <button onclick="calcNumber('9')" class="calc-btn">9</button>
                <button onclick="calcOperation('-')" class="calc-btn calc-operator">-</button>

                <button onclick="calcNumber('4')" class="calc-btn">4</button>
                <button onclick="calcNumber('5')" class="calc-btn">5</button>
                <button onclick="calcNumber('6')" class="calc-btn">6</button>
                <button onclick="calcOperation('+')" class="calc-btn calc-operator">+</button>

                <button onclick="calcNumber('1')" class="calc-btn">1</button>
                <button onclick="calcNumber('2')" class="calc-btn">2</button>
                <button onclick="calcNumber('3')" class="calc-btn">3</button>
                <button onclick="calculateResult()" class="calc-btn calc-equals" rowspan="2">=</button>

                <button onclick="calcNumber('0')" class="calc-btn calc-zero">0</button>
                <button onclick="calcNumber('.')" class="calc-btn">.</button>
                <button onclick="formatAsPKR()" class="calc-btn calc-pkr">PKR</button>
            </div>
        `;

        document.body.appendChild(calculator);
        this.initCalculator();

        // Add keyboard support
        this.addCalculatorKeyboardSupport();
    }

    addCalculatorKeyboardSupport() {
        document.addEventListener('keydown', (e) => {
            // Only handle keys when calculator is open
            if (!document.querySelector('.calculator-widget')) return;

            e.preventDefault();

            const key = e.key;

            if (key >= '0' && key <= '9') {
                window.calcNumber(key);
            } else if (key === '.') {
                window.calcNumber('.');
            } else if (key === '+') {
                window.calcOperation('+');
            } else if (key === '-') {
                window.calcOperation('-');
            } else if (key === '*') {
                window.calcOperation('*');
            } else if (key === '/') {
                window.calcOperation('/');
            } else if (key === 'Enter' || key === '=') {
                window.calculateResult();
            } else if (key === 'Escape' || key === 'c' || key === 'C') {
                window.clearCalc();
            } else if (key === 'Backspace') {
                window.deleteLast();
            }
        });
    }

    initCalculator() {
        window.calcNumber = (num) => {
            const display = document.getElementById('calcDisplay');
            if (display.value === '0') {
                display.value = num;
            } else {
                display.value += num;
            }
        };

        window.calcOperation = (op) => {
            const display = document.getElementById('calcDisplay');
            const lastChar = display.value.slice(-1);
            if (!['+', '-', '*', '/'].includes(lastChar)) {
                display.value += op;
            }
        };

        window.calculateResult = () => {
            const display = document.getElementById('calcDisplay');
            try {
                const result = eval(display.value.replace('×', '*').replace('÷', '/'));
                display.value = result.toString();
            } catch (error) {
                display.value = 'Error';
            }
        };

        window.clearCalc = () => {
            document.getElementById('calcDisplay').value = '0';
        };

        window.deleteLast = () => {
            const display = document.getElementById('calcDisplay');
            if (display.value.length > 1) {
                display.value = display.value.slice(0, -1);
            } else {
                display.value = '0';
            }
        };

        window.formatAsPKR = () => {
            const display = document.getElementById('calcDisplay');
            const value = parseFloat(display.value);
            if (!isNaN(value)) {
                display.value = `PKR ${value.toLocaleString()}`;
            }
        };

        window.deleteLast = () => {
            const display = document.getElementById('calcDisplay');
            if (display.value.length > 1) {
                display.value = display.value.slice(0, -1);
            } else {
                display.value = '0';
            }
        };
    }

    // To-Do List Methods
    loadTodoList() {
        try {
            const todoList = document.getElementById('todoList');
            if (!todoList) return;

            const tasks = this.getTasks();
            if (tasks.length === 0) {
            todoList.innerHTML = '<p class="empty-state">No tasks for today. Add a task to get started!</p>';
            return;
        }

        todoList.innerHTML = tasks.map(task => `
            <div class="todo-item ${task.completed ? 'completed' : ''}" data-priority="${task.priority}">
                <div class="todo-content">
                    <div class="todo-header">
                        <input type="checkbox" ${task.completed ? 'checked' : ''}
                               onchange="app.toggleTask('${task.id}')" class="todo-checkbox">
                        <span class="todo-title ${task.completed ? 'strikethrough' : ''}">${task.title}</span>
                        <span class="priority-badge priority-${task.priority}">${task.priority}</span>
                    </div>
                    <div class="todo-details">
                        <span class="todo-assignee"><i class="fas fa-user"></i> ${task.assignedTo}</span>
                        <span class="todo-time"><i class="fas fa-clock"></i> ${task.dueTime || 'No deadline'}</span>
                    </div>
                </div>
                <div class="todo-actions">
                    <button class="btn-icon" onclick="app.editTask('${task.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon delete" onclick="app.deleteTask('${task.id}')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
        } catch (error) {
            console.error('Error loading todo list:', error);
            const todoList = document.getElementById('todoList');
            if (todoList) {
                todoList.innerHTML = '<p class="empty-state">Error loading tasks. Please refresh the page.</p>';
            }
        }
    }

    getTasks() {
        const saved = localStorage.getItem('restaurantTasks');
        if (saved) {
            return JSON.parse(saved);
        }

        // Default tasks
        const defaultTasks = [
            {
                id: '1',
                title: 'Check inventory levels',
                priority: 'high',
                assignedTo: 'Manager',
                completed: false,
                dueTime: '9:00 AM',
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                title: 'Clean kitchen equipment',
                priority: 'medium',
                assignedTo: 'Kitchen Staff',
                completed: true,
                dueTime: '8:00 AM',
                createdAt: new Date().toISOString()
            },
            {
                id: '3',
                title: 'Update menu prices',
                priority: 'low',
                assignedTo: 'Admin',
                completed: false,
                dueTime: '2:00 PM',
                createdAt: new Date().toISOString()
            }
        ];

        localStorage.setItem('restaurantTasks', JSON.stringify(defaultTasks));
        return defaultTasks;
    }

    showAddTaskModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add New Task</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <div class="form-group">
                            <label for="taskTitle">Task Title *</label>
                            <input type="text" id="taskTitle" name="taskTitle" class="form-control" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="taskPriority">Priority *</label>
                                <select id="taskPriority" name="taskPriority" class="form-control" required>
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="taskAssignee">Assign To *</label>
                                <select id="taskAssignee" name="taskAssignee" class="form-control" required>
                                    <option value="Manager">Manager</option>
                                    <option value="Admin">Admin</option>
                                    <option value="Kitchen Staff">Kitchen Staff</option>
                                    <option value="Waiter">Waiter</option>
                                    <option value="Cashier">Cashier</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="taskTime">Due Time</label>
                            <input type="time" id="taskTime" name="taskTime" class="form-control">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveTask()">Add Task</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    saveTask() {
        const form = document.getElementById('addTaskForm');
        const formData = new FormData(form);

        if (!formData.get('taskTitle') || !formData.get('taskPriority') || !formData.get('taskAssignee')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const tasks = this.getTasks();
        const newTask = {
            id: Date.now().toString(),
            title: formData.get('taskTitle'),
            priority: formData.get('taskPriority'),
            assignedTo: formData.get('taskAssignee'),
            dueTime: formData.get('taskTime') || null,
            completed: false,
            createdAt: new Date().toISOString()
        };

        tasks.push(newTask);
        localStorage.setItem('restaurantTasks', JSON.stringify(tasks));

        document.querySelector('.modal-overlay').remove();
        this.loadTodoList();
        this.showNotification('Task added successfully!', 'success');
    }

    toggleTask(taskId) {
        const tasks = this.getTasks();
        const taskIndex = tasks.findIndex(task => task.id === taskId);

        if (taskIndex !== -1) {
            tasks[taskIndex].completed = !tasks[taskIndex].completed;
            localStorage.setItem('restaurantTasks', JSON.stringify(tasks));
            this.loadTodoList();
        }
    }

    editTask(taskId) {
        const tasks = this.getTasks();
        const task = tasks.find(t => t.id === taskId);
        if (!task) return;

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Task</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editTaskForm">
                        <div class="form-group">
                            <label for="editTaskTitle">Task Title *</label>
                            <input type="text" id="editTaskTitle" name="taskTitle" class="form-control" value="${task.title}" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="editTaskPriority">Priority *</label>
                                <select id="editTaskPriority" name="taskPriority" class="form-control" required>
                                    <option value="low" ${task.priority === 'low' ? 'selected' : ''}>Low</option>
                                    <option value="medium" ${task.priority === 'medium' ? 'selected' : ''}>Medium</option>
                                    <option value="high" ${task.priority === 'high' ? 'selected' : ''}>High</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="editTaskAssignee">Assign To *</label>
                                <select id="editTaskAssignee" name="taskAssignee" class="form-control" required>
                                    <option value="Manager" ${task.assignedTo === 'Manager' ? 'selected' : ''}>Manager</option>
                                    <option value="Admin" ${task.assignedTo === 'Admin' ? 'selected' : ''}>Admin</option>
                                    <option value="Kitchen Staff" ${task.assignedTo === 'Kitchen Staff' ? 'selected' : ''}>Kitchen Staff</option>
                                    <option value="Waiter" ${task.assignedTo === 'Waiter' ? 'selected' : ''}>Waiter</option>
                                    <option value="Cashier" ${task.assignedTo === 'Cashier' ? 'selected' : ''}>Cashier</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="editTaskTime">Due Time</label>
                            <input type="time" id="editTaskTime" name="taskTime" class="form-control" value="${task.dueTime || ''}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.updateTask('${taskId}')">Update Task</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    updateTask(taskId) {
        const form = document.getElementById('editTaskForm');
        const formData = new FormData(form);

        if (!formData.get('taskTitle') || !formData.get('taskPriority') || !formData.get('taskAssignee')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const tasks = this.getTasks();
        const taskIndex = tasks.findIndex(task => task.id === taskId);

        if (taskIndex !== -1) {
            tasks[taskIndex] = {
                ...tasks[taskIndex],
                title: formData.get('taskTitle'),
                priority: formData.get('taskPriority'),
                assignedTo: formData.get('taskAssignee'),
                dueTime: formData.get('taskTime') || null,
                updatedAt: new Date().toISOString()
            };

            localStorage.setItem('restaurantTasks', JSON.stringify(tasks));
            document.querySelector('.modal-overlay').remove();
            this.loadTodoList();
            this.showNotification('Task updated successfully!', 'success');
        }
    }

    deleteTask(taskId) {
        if (confirm('Are you sure you want to delete this task?')) {
            const tasks = this.getTasks();
            const filteredTasks = tasks.filter(task => task.id !== taskId);
            localStorage.setItem('restaurantTasks', JSON.stringify(filteredTasks));
            this.loadTodoList();
            this.showNotification('Task deleted successfully!', 'success');
        }
    }

    // Shopping/Purchase List Methods
    loadPurchaseList() {
        try {
            const purchaseList = document.getElementById('purchaseList');
            if (!purchaseList) return;

            const items = this.getPurchaseItems();
            if (items.length === 0) {
            purchaseList.innerHTML = '<p class="empty-state">No items to purchase. Add items to your shopping list!</p>';
            return;
        }

        purchaseList.innerHTML = items.map(item => `
            <div class="purchase-item ${item.status}" data-urgency="${item.urgency}">
                <div class="purchase-content">
                    <div class="purchase-header">
                        <span class="purchase-name">${item.name}</span>
                        <span class="urgency-badge urgency-${item.urgency}">${item.urgency}</span>
                    </div>
                    <div class="purchase-details">
                        <span class="purchase-quantity"><i class="fas fa-box"></i> ${item.quantity} ${item.unit}</span>
                        <span class="purchase-cost"><i class="fas fa-dollar-sign"></i> PKR ${item.estimatedCost}</span>
                        <span class="purchase-supplier"><i class="fas fa-store"></i> ${item.supplier}</span>
                    </div>
                    <div class="purchase-status">
                        <span class="status-badge status-${item.status}">${item.status}</span>
                    </div>
                </div>
                <div class="purchase-actions">
                    <button class="btn-icon" onclick="app.updatePurchaseStatus('${item.id}')" title="Update Status">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn-icon" onclick="app.editPurchaseItem('${item.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon delete" onclick="app.deletePurchaseItem('${item.id}')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
        } catch (error) {
            console.error('Error loading purchase list:', error);
            const purchaseList = document.getElementById('purchaseList');
            if (purchaseList) {
                purchaseList.innerHTML = '<p class="empty-state">Error loading purchase items. Please refresh the page.</p>';
            }
        }
    }

    getPurchaseItems() {
        const saved = localStorage.getItem('purchaseItems');
        if (saved) {
            return JSON.parse(saved);
        }

        // Default purchase items based on low stock
        const defaultItems = [
            {
                id: '1',
                name: 'Chicken (Fresh)',
                quantity: 10,
                unit: 'kg',
                estimatedCost: 4500,
                supplier: 'Local Poultry Farm',
                urgency: 'high',
                status: 'pending',
                autoGenerated: true,
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                name: 'Basmati Rice',
                quantity: 25,
                unit: 'kg',
                estimatedCost: 4500,
                supplier: 'Rice Wholesaler',
                urgency: 'medium',
                status: 'ordered',
                autoGenerated: true,
                createdAt: new Date().toISOString()
            },
            {
                id: '3',
                name: 'Cooking Oil',
                quantity: 5,
                unit: 'liter',
                estimatedCost: 1400,
                supplier: 'Oil Supplier',
                urgency: 'high',
                status: 'pending',
                autoGenerated: true,
                createdAt: new Date().toISOString()
            }
        ];

        localStorage.setItem('purchaseItems', JSON.stringify(defaultItems));
        return defaultItems;
    }

    showAddPurchaseModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add Purchase Item</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addPurchaseForm">
                        <div class="form-group">
                            <label for="purchaseName">Item Name *</label>
                            <input type="text" id="purchaseName" name="purchaseName" class="form-control" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="purchaseQuantity">Quantity *</label>
                                <input type="number" id="purchaseQuantity" name="purchaseQuantity" class="form-control" min="1" required>
                            </div>
                            <div class="form-group">
                                <label for="purchaseUnit">Unit *</label>
                                <select id="purchaseUnit" name="purchaseUnit" class="form-control" required>
                                    <option value="kg">Kilogram (kg)</option>
                                    <option value="liter">Liter</option>
                                    <option value="piece">Piece</option>
                                    <option value="pack">Pack</option>
                                    <option value="box">Box</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="purchaseCost">Estimated Cost (PKR) *</label>
                                <input type="number" id="purchaseCost" name="purchaseCost" class="form-control" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="purchaseUrgency">Urgency *</label>
                                <select id="purchaseUrgency" name="purchaseUrgency" class="form-control" required>
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="purchaseSupplier">Supplier</label>
                            <input type="text" id="purchaseSupplier" name="purchaseSupplier" class="form-control" placeholder="Supplier name">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.savePurchaseItem()">Add Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    savePurchaseItem() {
        const form = document.getElementById('addPurchaseForm');
        const formData = new FormData(form);

        if (!formData.get('purchaseName') || !formData.get('purchaseQuantity') || !formData.get('purchaseUnit') || !formData.get('purchaseCost') || !formData.get('purchaseUrgency')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const items = this.getPurchaseItems();
        const newItem = {
            id: Date.now().toString(),
            name: formData.get('purchaseName'),
            quantity: parseInt(formData.get('purchaseQuantity')),
            unit: formData.get('purchaseUnit'),
            estimatedCost: parseInt(formData.get('purchaseCost')),
            supplier: formData.get('purchaseSupplier') || 'TBD',
            urgency: formData.get('purchaseUrgency'),
            status: 'pending',
            autoGenerated: false,
            createdAt: new Date().toISOString()
        };

        items.push(newItem);
        localStorage.setItem('purchaseItems', JSON.stringify(items));

        document.querySelector('.modal-overlay').remove();
        this.loadPurchaseList();
        this.showNotification('Purchase item added successfully!', 'success');
    }

    updatePurchaseStatus(itemId) {
        const items = this.getPurchaseItems();
        const itemIndex = items.findIndex(item => item.id === itemId);

        if (itemIndex !== -1) {
            const currentStatus = items[itemIndex].status;
            const statusFlow = ['pending', 'ordered', 'received'];
            const currentIndex = statusFlow.indexOf(currentStatus);
            const nextIndex = (currentIndex + 1) % statusFlow.length;
            const newStatus = statusFlow[nextIndex];

            // If marking as "received", show purchase completion modal
            if (newStatus === 'received') {
                this.showPurchaseCompletionModal(items[itemIndex]);
                return;
            }

            items[itemIndex].status = newStatus;
            items[itemIndex].updatedAt = new Date().toISOString();

            localStorage.setItem('purchaseItems', JSON.stringify(items));
            this.loadPurchaseList();
            this.showNotification(`Status updated to ${newStatus}`, 'success');
        }
    }

    showPurchaseCompletionModal(purchaseItem) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-check-circle"></i> Mark Purchase as Completed</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="purchase-summary">
                        <h4>Purchase Details</h4>
                        <div class="summary-item">
                            <span>Item:</span>
                            <span>${purchaseItem.name}</span>
                        </div>
                        <div class="summary-item">
                            <span>Quantity:</span>
                            <span>${purchaseItem.quantity} ${purchaseItem.unit}</span>
                        </div>
                        <div class="summary-item">
                            <span>Estimated Cost:</span>
                            <span>PKR ${purchaseItem.estimatedCost}</span>
                        </div>
                        <div class="summary-item">
                            <span>Supplier:</span>
                            <span>${purchaseItem.supplier}</span>
                        </div>
                    </div>

                    <form id="purchaseCompletionForm">
                        <div class="form-group">
                            <label for="actualCost">Actual Cost Paid (PKR) *</label>
                            <input type="number" id="actualCost" class="form-control" value="${purchaseItem.estimatedCost}" min="0" step="0.01" required>
                            <small class="form-text">Enter the actual amount you paid for this purchase</small>
                        </div>

                        <div class="form-group">
                            <label for="purchaseNotes">Purchase Notes (Optional)</label>
                            <textarea id="purchaseNotes" class="form-control" rows="3" placeholder="Any additional notes about this purchase"></textarea>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="addToInventory" checked>
                                <span class="checkmark"></span>
                                Add this purchased item to inventory
                            </label>
                        </div>

                        <div class="expense-info">
                            <h5><i class="fas fa-info-circle"></i> What will happen:</h5>
                            <ul>
                                <li>✅ Purchase item will be marked as "Received"</li>
                                <li>✅ Expense will be recorded in Expenses page</li>
                                <li>✅ Cash balance will be updated</li>
                                <li id="inventoryAction">✅ Item will be added/updated in inventory</li>
                            </ul>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                            <button type="button" class="btn btn-success" onclick="app.completePurchase('${purchaseItem.id}')">
                                <i class="fas fa-check"></i> Mark as Purchased
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listener for inventory checkbox
        document.getElementById('addToInventory').addEventListener('change', function() {
            const inventoryAction = document.getElementById('inventoryAction');
            if (this.checked) {
                inventoryAction.innerHTML = '✅ Item will be added/updated in inventory';
            } else {
                inventoryAction.innerHTML = '❌ Item will NOT be added to inventory';
            }
        });
    }

    completePurchase(itemId) {
        const actualCost = parseFloat(document.getElementById('actualCost').value);
        const notes = document.getElementById('purchaseNotes').value.trim();
        const addToInventory = document.getElementById('addToInventory').checked;

        if (!actualCost || actualCost <= 0) {
            this.showNotification('Please enter a valid actual cost', 'error');
            return;
        }

        // Get purchase item
        const items = this.getPurchaseItems();
        const itemIndex = items.findIndex(item => item.id === itemId);

        if (itemIndex === -1) {
            this.showNotification('Purchase item not found', 'error');
            return;
        }

        const purchaseItem = items[itemIndex];

        // 1. Update purchase item status
        items[itemIndex].status = 'received';
        items[itemIndex].actualCost = actualCost;
        items[itemIndex].completedAt = new Date().toISOString();
        items[itemIndex].updatedAt = new Date().toISOString();
        localStorage.setItem('purchaseItems', JSON.stringify(items));

        // 2. Create expense record
        const expense = {
            id: Date.now().toString(),
            item: purchaseItem.name,
            amount: actualCost,
            category: 'other', // Default category, can be enhanced
            addToInventory: addToInventory,
            notes: notes || `Purchase from ${purchaseItem.supplier}`,
            date: new Date().toISOString().split('T')[0],
            timestamp: new Date().toISOString(),
            source: 'purchase_list',
            purchaseItemId: itemId
        };

        const expenses = this.getExpenses();
        expenses.push(expense);
        localStorage.setItem('restaurantExpenses', JSON.stringify(expenses));

        // 3. Add to inventory if requested
        if (addToInventory) {
            const inventoryItems = this.getInventoryItems();
            const existingItemIndex = inventoryItems.findIndex(inv =>
                inv.name.toLowerCase() === purchaseItem.name.toLowerCase()
            );

            if (existingItemIndex !== -1) {
                // Update existing item
                inventoryItems[existingItemIndex].quantity += purchaseItem.quantity;
                inventoryItems[existingItemIndex].lastUpdated = new Date().toISOString();
            } else {
                // Add new item
                const newInventoryItem = {
                    id: Date.now().toString() + '_inv',
                    name: purchaseItem.name,
                    quantity: purchaseItem.quantity,
                    unit: purchaseItem.unit,
                    unitPrice: actualCost / purchaseItem.quantity,
                    category: 'purchased',
                    supplier: purchaseItem.supplier,
                    expiryDate: '',
                    minStockLevel: Math.ceil(purchaseItem.quantity * 0.2),
                    lastUpdated: new Date().toISOString()
                };
                inventoryItems.push(newInventoryItem);
            }

            localStorage.setItem('inventoryItems', JSON.stringify(inventoryItems));
        }

        // Close modal and refresh
        document.querySelector('.modal-overlay').remove();
        this.loadPurchaseList();
        this.updateDashboardStats();

        const message = addToInventory
            ? `Purchase completed! ${purchaseItem.name} added to inventory and expense recorded.`
            : `Purchase completed! Expense recorded for ${purchaseItem.name}.`;

        this.showNotification(message, 'success');
    }

    editPurchaseItem(itemId) {
        const items = this.getPurchaseItems();
        const item = items.find(i => i.id === itemId);
        if (!item) return;

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Purchase Item</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editPurchaseForm">
                        <div class="form-group">
                            <label for="editPurchaseName">Item Name *</label>
                            <input type="text" id="editPurchaseName" name="purchaseName" class="form-control" value="${item.name}" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="editPurchaseQuantity">Quantity *</label>
                                <input type="number" id="editPurchaseQuantity" name="purchaseQuantity" class="form-control" min="1" value="${item.quantity}" required>
                            </div>
                            <div class="form-group">
                                <label for="editPurchaseUnit">Unit *</label>
                                <select id="editPurchaseUnit" name="purchaseUnit" class="form-control" required>
                                    <option value="kg" ${item.unit === 'kg' ? 'selected' : ''}>Kilogram (kg)</option>
                                    <option value="liter" ${item.unit === 'liter' ? 'selected' : ''}>Liter</option>
                                    <option value="piece" ${item.unit === 'piece' ? 'selected' : ''}>Piece</option>
                                    <option value="pack" ${item.unit === 'pack' ? 'selected' : ''}>Pack</option>
                                    <option value="box" ${item.unit === 'box' ? 'selected' : ''}>Box</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="editPurchaseCost">Estimated Cost (PKR) *</label>
                                <input type="number" id="editPurchaseCost" name="purchaseCost" class="form-control" min="0" value="${item.estimatedCost}" required>
                            </div>
                            <div class="form-group">
                                <label for="editPurchaseUrgency">Urgency *</label>
                                <select id="editPurchaseUrgency" name="purchaseUrgency" class="form-control" required>
                                    <option value="low" ${item.urgency === 'low' ? 'selected' : ''}>Low</option>
                                    <option value="medium" ${item.urgency === 'medium' ? 'selected' : ''}>Medium</option>
                                    <option value="high" ${item.urgency === 'high' ? 'selected' : ''}>High</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="editPurchaseSupplier">Supplier</label>
                            <input type="text" id="editPurchaseSupplier" name="purchaseSupplier" class="form-control" value="${item.supplier}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.updatePurchaseItem('${itemId}')">Update Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    updatePurchaseItem(itemId) {
        const form = document.getElementById('editPurchaseForm');
        const formData = new FormData(form);

        if (!formData.get('purchaseName') || !formData.get('purchaseQuantity') || !formData.get('purchaseUnit') || !formData.get('purchaseCost') || !formData.get('purchaseUrgency')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const items = this.getPurchaseItems();
        const itemIndex = items.findIndex(item => item.id === itemId);

        if (itemIndex !== -1) {
            items[itemIndex] = {
                ...items[itemIndex],
                name: formData.get('purchaseName'),
                quantity: parseInt(formData.get('purchaseQuantity')),
                unit: formData.get('purchaseUnit'),
                estimatedCost: parseInt(formData.get('purchaseCost')),
                supplier: formData.get('purchaseSupplier') || 'TBD',
                urgency: formData.get('purchaseUrgency'),
                updatedAt: new Date().toISOString()
            };

            localStorage.setItem('purchaseItems', JSON.stringify(items));
            document.querySelector('.modal-overlay').remove();
            this.loadPurchaseList();
            this.showNotification('Purchase item updated successfully!', 'success');
        }
    }

    deletePurchaseItem(itemId) {
        if (confirm('Are you sure you want to delete this purchase item?')) {
            const items = this.getPurchaseItems();
            const filteredItems = items.filter(item => item.id !== itemId);
            localStorage.setItem('purchaseItems', JSON.stringify(filteredItems));
            this.loadPurchaseList();
            this.showNotification('Purchase item deleted successfully!', 'success');
        }
    }

    // Helper methods for generating content
    generateTablesGrid() {
        const tables = [
            { number: 'T-01', capacity: 4, status: 'available' },
            { number: 'T-02', capacity: 2, status: 'occupied', order: 'ZQ001', amount: 1250 },
            { number: 'T-03', capacity: 6, status: 'reserved' },
            { number: 'T-04', capacity: 4, status: 'cleaning' },
            { number: 'T-05', capacity: 8, status: 'occupied', order: 'ZQ002', amount: 2100 },
            { number: 'T-06', capacity: 4, status: 'available' }
        ];

        return tables.map(table => `
            <div class="table-card ${table.status}">
                <div class="table-header">
                    <h4>${table.number}</h4>
                    <span class="table-status">${table.status}</span>
                </div>
                <div class="table-info">
                    <p><i class="fas fa-users"></i> Capacity: ${table.capacity}</p>
                    ${table.order ? `
                        <p><i class="fas fa-receipt"></i> ${table.order}</p>
                        <p><i class="fas fa-dollar-sign"></i> PKR ${table.amount}</p>
                    ` : ''}
                </div>
                <div class="table-actions">
                    ${table.status === 'available' ? 
                        '<button class="btn btn-primary btn-sm">New Order</button>' :
                        table.status === 'occupied' ?
                        '<button class="btn btn-outline btn-sm">View Order</button>' :
                        '<button class="btn btn-outline btn-sm">Manage</button>'
                    }
                </div>
            </div>
        `).join('');
    }



    generateMenuItems(filterCategory = 'all') {
        const items = this.getMenuItems();
        const filteredItems = filterCategory === 'all' ? items : items.filter(item => item.category === filterCategory);

        return filteredItems.map(item => `
            <div class="menu-item-card">
                <div class="item-header">
                    <h4>${item.name}</h4>
                    <span class="availability ${item.isAvailable ? 'available' : 'unavailable'}">
                        ${item.isAvailable ? 'Available' : 'Out of Stock'}
                    </span>
                </div>
                <div class="item-details">
                    <p class="category">${item.category.charAt(0).toUpperCase() + item.category.slice(1)}</p>
                    <p class="description">${item.description}</p>
                    <div class="pricing">
                        <span class="price">PKR ${item.basePrice}</span>
                        ${item.dineInPrice ? `<span class="dine-in-price">Dine-in: PKR ${item.dineInPrice}</span>` : ''}
                        ${item.takeawayPrice ? `<span class="takeaway-price">Takeaway: PKR ${item.takeawayPrice}</span>` : ''}
                        ${item.isColdDrink ? '<span class="cold-drink-badge">Cold Drink (+15 PKR dine-in)</span>' : ''}
                    </div>
                    <div class="prep-time">
                        <i class="fas fa-clock"></i> ${item.preparationTime} minutes
                    </div>
                </div>
                <div class="item-actions">
                    <button class="btn btn-outline btn-sm" onclick="app.editMenuItem('${item.id}')">Edit</button>
                    <button class="btn btn-primary btn-sm" onclick="app.toggleItemAvailability('${item.id}')">${item.isAvailable ? 'Disable' : 'Enable'}</button>
                    <button class="btn btn-error btn-sm" onclick="app.deleteMenuItem('${item.id}')">Delete</button>
                </div>
            </div>
        `).join('');
    }

    editMenuItem(itemId) {
        const items = this.getMenuItems();
        const item = items.find(i => i.id === itemId);
        if (!item) return;

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Edit Menu Item</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editMenuItemForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editItemName">Item Name *</label>
                                <input type="text" id="editItemName" name="itemName" class="form-control" value="${item.name}" required>
                            </div>
                            <div class="form-group">
                                <label for="editItemCategory">Category *</label>
                                <select id="editItemCategory" name="itemCategory" class="form-control" required>
                                    <option value="appetizers" ${item.category === 'appetizers' ? 'selected' : ''}>Appetizers</option>
                                    <option value="main" ${item.category === 'main' ? 'selected' : ''}>Main Course</option>
                                    <option value="beverages" ${item.category === 'beverages' ? 'selected' : ''}>Beverages</option>
                                    <option value="desserts" ${item.category === 'desserts' ? 'selected' : ''}>Desserts</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="editItemDescription">Description</label>
                            <textarea id="editItemDescription" name="itemDescription" class="form-control" rows="3">${item.description}</textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="editBasePrice">Base Price (PKR) *</label>
                                <input type="number" id="editBasePrice" name="basePrice" class="form-control" min="0" step="0.01" value="${item.basePrice}" required>
                            </div>
                            <div class="form-group">
                                <label for="editDineInPrice">Dine-in Price (PKR)</label>
                                <input type="number" id="editDineInPrice" name="dineInPrice" class="form-control" min="0" step="0.01" value="${item.dineInPrice || ''}">
                            </div>
                            <div class="form-group">
                                <label for="editTakeawayPrice">Takeaway Price (PKR)</label>
                                <input type="number" id="editTakeawayPrice" name="takeawayPrice" class="form-control" min="0" step="0.01" value="${item.takeawayPrice || ''}">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="editPrepTime">Preparation Time (minutes) *</label>
                                <input type="number" id="editPrepTime" name="prepTime" class="form-control" min="1" value="${item.preparationTime}" required>
                            </div>
                            <div class="form-group">
                                <label for="editIsColdDrink">Cold Drink?</label>
                                <select id="editIsColdDrink" name="isColdDrink" class="form-control">
                                    <option value="false" ${!item.isColdDrink ? 'selected' : ''}>No</option>
                                    <option value="true" ${item.isColdDrink ? 'selected' : ''}>Yes</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="editIsAvailable">Availability</label>
                                <select id="editIsAvailable" name="isAvailable" class="form-control">
                                    <option value="true" ${item.isAvailable ? 'selected' : ''}>Available</option>
                                    <option value="false" ${!item.isAvailable ? 'selected' : ''}>Out of Stock</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.updateMenuItem('${itemId}')">Update Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    updateMenuItem(itemId) {
        const form = document.getElementById('editMenuItemForm');
        const formData = new FormData(form);

        // Validate required fields
        if (!formData.get('itemName') || !formData.get('itemCategory') || !formData.get('basePrice') || !formData.get('prepTime')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Get existing menu items
        let menuItems = JSON.parse(localStorage.getItem('menuItems')) || [];

        // Find and update the item
        const itemIndex = menuItems.findIndex(item => item.id === itemId);
        if (itemIndex !== -1) {
            menuItems[itemIndex] = {
                ...menuItems[itemIndex],
                name: formData.get('itemName'),
                category: formData.get('itemCategory'),
                description: formData.get('itemDescription') || '',
                basePrice: parseFloat(formData.get('basePrice')),
                dineInPrice: formData.get('dineInPrice') ? parseFloat(formData.get('dineInPrice')) : null,
                takeawayPrice: formData.get('takeawayPrice') ? parseFloat(formData.get('takeawayPrice')) : null,
                preparationTime: parseInt(formData.get('prepTime')),
                isColdDrink: formData.get('isColdDrink') === 'true',
                isAvailable: formData.get('isAvailable') === 'true',
                updatedAt: new Date().toISOString()
            };

            // Save to localStorage
            localStorage.setItem('menuItems', JSON.stringify(menuItems));

            // Close modal
            document.querySelector('.modal-overlay').remove();

            // Refresh menu display
            this.refreshMenuItems();

            this.showNotification('Menu item updated successfully!', 'success');
        }
    }

    toggleItemAvailability(itemId) {
        let menuItems = JSON.parse(localStorage.getItem('menuItems')) || [];
        const itemIndex = menuItems.findIndex(item => item.id === itemId);

        if (itemIndex !== -1) {
            menuItems[itemIndex].isAvailable = !menuItems[itemIndex].isAvailable;
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            this.refreshMenuItems();

            const status = menuItems[itemIndex].isAvailable ? 'enabled' : 'disabled';
            this.showNotification(`Menu item ${status} successfully!`, 'success');
        }
    }

    deleteMenuItem(itemId) {
        if (confirm('Are you sure you want to delete this menu item?')) {
            let menuItems = JSON.parse(localStorage.getItem('menuItems')) || [];
            menuItems = menuItems.filter(item => item.id !== itemId);
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            this.refreshMenuItems();
            this.showNotification('Menu item deleted successfully!', 'success');
        }
    }

    getInventoryItems() {
        const saved = localStorage.getItem('inventoryItems');
        if (saved) {
            return JSON.parse(saved);
        }

        // Default inventory items that match menu ingredients
        const defaultItems = [
            {
                id: '1',
                name: 'Chicken (Fresh)',
                currentStock: 15,
                minimumStock: 5,
                unit: 'kg',
                unitPrice: 450,
                supplier: 'Local Poultry Farm',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '2',
                name: 'Basmati Rice',
                currentStock: 25,
                minimumStock: 10,
                unit: 'kg',
                unitPrice: 180,
                supplier: 'Rice Wholesaler',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '3',
                name: 'Cooking Oil',
                currentStock: 8,
                minimumStock: 3,
                unit: 'liter',
                unitPrice: 280,
                supplier: 'Oil Supplier',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '4',
                name: 'Tomatoes',
                currentStock: 50,
                minimumStock: 10,
                unit: 'pieces',
                unitPrice: 8,
                supplier: 'Vegetable Market',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '5',
                name: 'Onions',
                currentStock: 30,
                minimumStock: 5,
                unit: 'pieces',
                unitPrice: 12,
                supplier: 'Vegetable Market',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '6',
                name: 'Coca Cola Bottles',
                currentStock: 48,
                minimumStock: 12,
                unit: 'bottles',
                unitPrice: 65,
                supplier: 'Beverage Distributor',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '7',
                name: 'Milk',
                currentStock: 10,
                minimumStock: 3,
                unit: 'liter',
                unitPrice: 120,
                supplier: 'Dairy Farm',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '8',
                name: 'Sugar',
                currentStock: 5,
                minimumStock: 2,
                unit: 'kg',
                unitPrice: 90,
                supplier: 'Grocery Supplier',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '9',
                name: 'Pepsi Bottles',
                currentStock: 24,
                minimumStock: 6,
                unit: 'bottles',
                unitPrice: 70,
                supplier: 'Beverage Distributor',
                lastUpdated: new Date().toISOString()
            },
            {
                id: '10',
                name: 'Lemons',
                currentStock: 40,
                minimumStock: 10,
                unit: 'pieces',
                unitPrice: 5,
                supplier: 'Fruit Market',
                lastUpdated: new Date().toISOString()
            }
        ];

        localStorage.setItem('inventoryItems', JSON.stringify(defaultItems));
        return defaultItems;
    }

    generateInventoryTable() {
        const items = this.getInventoryItems();

        return `
            <table class="inventory-table">
                <thead>
                    <tr>
                        <th>Item Name</th>
                        <th>Current Stock</th>
                        <th>Minimum Stock</th>
                        <th>Unit</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map(item => {
                        const status = item.currentStock <= item.minimumStock ? 'critical' :
                                     item.currentStock <= item.minimumStock * 2 ? 'low' : 'good';
                        return `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.currentStock}</td>
                                <td>${item.minimumStock}</td>
                                <td>${item.unit}</td>
                                <td><span class="stock-status ${status}">${status}</span></td>
                                <td>
                                    <button class="btn btn-outline btn-sm" onclick="app.showUpdateInventoryModal('${item.id}')" title="Update Item">
                                        <i class="fas fa-edit"></i> Update
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="app.showRestockModal('${item.id}')" title="Restock Item">
                                        <i class="fas fa-plus"></i> Restock
                                    </button>
                                    <button class="btn btn-error btn-sm" onclick="app.deleteInventoryItem('${item.id}')" title="Delete Item">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;
    }

    // Inventory Management Methods
    generateInventoryAlerts() {
        const items = this.getInventoryItems();
        const lowStockItems = items.filter(item => item.currentStock <= item.minimumStock);
        const criticalItems = items.filter(item => item.currentStock === 0);

        if (lowStockItems.length === 0) {
            return `
                <div class="alert-card success">
                    <i class="fas fa-check-circle"></i>
                    <span>All inventory items are well stocked</span>
                </div>
            `;
        }

        return `
            <div class="alert-card warning">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${lowStockItems.length} items are running low on stock</span>
                <button class="btn btn-sm" onclick="app.showLowStockDetails()">View Details</button>
            </div>
            ${criticalItems.length > 0 ? `
                <div class="alert-card error">
                    <i class="fas fa-times-circle"></i>
                    <span>${criticalItems.length} items are out of stock</span>
                    <button class="btn btn-sm" onclick="app.showOutOfStockDetails()">View Details</button>
                </div>
            ` : ''}
        `;
    }

    updateInventoryAlerts() {
        const alertsContainer = document.getElementById('inventoryAlerts');
        if (alertsContainer) {
            alertsContainer.innerHTML = this.generateInventoryAlerts();
        }
    }

    showAddInventoryItemModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add Inventory Item</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addInventoryForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="inventoryName">Item Name *</label>
                                <input type="text" id="inventoryName" name="itemName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="inventoryUnit">Unit *</label>
                                <select id="inventoryUnit" name="unit" class="form-control" required>
                                    <option value="">Select Unit</option>
                                    <option value="kg">Kilogram (kg)</option>
                                    <option value="liter">Liter</option>
                                    <option value="pieces">Pieces</option>
                                    <option value="grams">Grams (g)</option>
                                    <option value="ml">Milliliter (ml)</option>
                                    <option value="bottles">Bottles</option>
                                    <option value="packets">Packets</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="currentStock">Current Stock *</label>
                                <input type="number" id="currentStock" name="currentStock" class="form-control" min="0" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="minimumStock">Minimum Stock *</label>
                                <input type="number" id="minimumStock" name="minimumStock" class="form-control" min="0" step="0.01" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="unitPrice">Unit Price (PKR) *</label>
                                <input type="number" id="unitPrice" name="unitPrice" class="form-control" min="0" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="supplier">Supplier</label>
                                <input type="text" id="supplier" name="supplier" class="form-control" placeholder="Supplier name">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveInventoryItem()">Add Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    saveInventoryItem() {
        const form = document.getElementById('addInventoryForm');
        const formData = new FormData(form);

        if (!formData.get('itemName') || !formData.get('unit') || !formData.get('currentStock') || !formData.get('minimumStock') || !formData.get('unitPrice')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const items = this.getInventoryItems();
        const newItem = {
            id: Date.now().toString(),
            name: formData.get('itemName'),
            currentStock: parseFloat(formData.get('currentStock')),
            minimumStock: parseFloat(formData.get('minimumStock')),
            unit: formData.get('unit'),
            unitPrice: parseFloat(formData.get('unitPrice')),
            supplier: formData.get('supplier') || 'Unknown',
            lastUpdated: new Date().toISOString()
        };

        items.push(newItem);
        localStorage.setItem('inventoryItems', JSON.stringify(items));

        document.querySelector('.modal-overlay').remove();
        this.refreshInventoryPage();
        this.showNotification('Inventory item added successfully!', 'success');
    }

    showUpdateInventoryModal(itemId) {
        const items = this.getInventoryItems();
        const item = items.find(i => i.id === itemId);

        if (!item) {
            this.showNotification('Item not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Update Inventory Item</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="updateInventoryForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="updateInventoryName">Item Name *</label>
                                <input type="text" id="updateInventoryName" name="itemName" class="form-control" value="${item.name}" required>
                            </div>
                            <div class="form-group">
                                <label for="updateInventoryUnit">Unit *</label>
                                <select id="updateInventoryUnit" name="unit" class="form-control" required>
                                    <option value="kg" ${item.unit === 'kg' ? 'selected' : ''}>Kilogram (kg)</option>
                                    <option value="liter" ${item.unit === 'liter' ? 'selected' : ''}>Liter</option>
                                    <option value="pieces" ${item.unit === 'pieces' ? 'selected' : ''}>Pieces</option>
                                    <option value="grams" ${item.unit === 'grams' ? 'selected' : ''}>Grams (g)</option>
                                    <option value="ml" ${item.unit === 'ml' ? 'selected' : ''}>Milliliter (ml)</option>
                                    <option value="bottles" ${item.unit === 'bottles' ? 'selected' : ''}>Bottles</option>
                                    <option value="packets" ${item.unit === 'packets' ? 'selected' : ''}>Packets</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="updateCurrentStock">Current Stock *</label>
                                <input type="number" id="updateCurrentStock" name="currentStock" class="form-control" min="0" step="0.01" value="${item.currentStock}" required>
                            </div>
                            <div class="form-group">
                                <label for="updateMinimumStock">Minimum Stock *</label>
                                <input type="number" id="updateMinimumStock" name="minimumStock" class="form-control" min="0" step="0.01" value="${item.minimumStock}" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="updateUnitPrice">Unit Price (PKR) *</label>
                                <input type="number" id="updateUnitPrice" name="unitPrice" class="form-control" min="0" step="0.01" value="${item.unitPrice}" required>
                            </div>
                            <div class="form-group">
                                <label for="updateSupplier">Supplier</label>
                                <input type="text" id="updateSupplier" name="supplier" class="form-control" value="${item.supplier || ''}" placeholder="Supplier name">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.updateInventoryItem('${itemId}')">Update Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    updateInventoryItem(itemId) {
        const form = document.getElementById('updateInventoryForm');
        const formData = new FormData(form);

        if (!formData.get('itemName') || !formData.get('unit') || !formData.get('currentStock') || !formData.get('minimumStock') || !formData.get('unitPrice')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const items = this.getInventoryItems();
        const itemIndex = items.findIndex(i => i.id === itemId);

        if (itemIndex === -1) {
            this.showNotification('Item not found', 'error');
            return;
        }

        items[itemIndex] = {
            ...items[itemIndex],
            name: formData.get('itemName'),
            currentStock: parseFloat(formData.get('currentStock')),
            minimumStock: parseFloat(formData.get('minimumStock')),
            unit: formData.get('unit'),
            unitPrice: parseFloat(formData.get('unitPrice')),
            supplier: formData.get('supplier') || 'Unknown',
            lastUpdated: new Date().toISOString()
        };

        localStorage.setItem('inventoryItems', JSON.stringify(items));

        document.querySelector('.modal-overlay').remove();
        this.refreshInventoryPage();
        this.showNotification('Inventory item updated successfully!', 'success');
    }

    showRestockModal(itemId) {
        const items = this.getInventoryItems();
        const item = items.find(i => i.id === itemId);

        if (!item) {
            this.showNotification('Item not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Restock Item - ${item.name}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="current-stock-info">
                        <p><strong>Current Stock:</strong> ${item.currentStock} ${item.unit}</p>
                        <p><strong>Minimum Stock:</strong> ${item.minimumStock} ${item.unit}</p>
                    </div>

                    <form id="restockForm">
                        <div class="form-group">
                            <label for="restockQuantity">Quantity to Add *</label>
                            <input type="number" id="restockQuantity" name="quantity" class="form-control" min="0.01" step="0.01" required>
                            <small class="form-text">Enter the amount to add to current stock</small>
                        </div>

                        <div class="form-group">
                            <label for="restockCost">Total Cost (PKR)</label>
                            <input type="number" id="restockCost" name="cost" class="form-control" min="0" step="0.01">
                            <small class="form-text">Optional: Total cost for this restock</small>
                        </div>

                        <div class="form-group">
                            <label for="restockNotes">Notes</label>
                            <textarea id="restockNotes" name="notes" class="form-control" rows="2" placeholder="Purchase details, supplier info, etc."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.processRestock('${itemId}')">Restock Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    processRestock(itemId) {
        const form = document.getElementById('restockForm');
        const formData = new FormData(form);

        const quantity = parseFloat(formData.get('quantity'));
        if (!quantity || quantity <= 0) {
            this.showNotification('Please enter a valid quantity', 'error');
            return;
        }

        const items = this.getInventoryItems();
        const itemIndex = items.findIndex(i => i.id === itemId);

        if (itemIndex === -1) {
            this.showNotification('Item not found', 'error');
            return;
        }

        const oldStock = items[itemIndex].currentStock;
        items[itemIndex].currentStock += quantity;
        items[itemIndex].lastUpdated = new Date().toISOString();

        // Add restock history
        if (!items[itemIndex].restockHistory) {
            items[itemIndex].restockHistory = [];
        }

        items[itemIndex].restockHistory.push({
            date: new Date().toISOString(),
            quantity: quantity,
            cost: parseFloat(formData.get('cost')) || 0,
            notes: formData.get('notes') || '',
            previousStock: oldStock,
            newStock: items[itemIndex].currentStock
        });

        localStorage.setItem('inventoryItems', JSON.stringify(items));

        document.querySelector('.modal-overlay').remove();
        this.refreshInventoryPage();
        this.showNotification(`${items[itemIndex].name} restocked successfully! Added ${quantity} ${items[itemIndex].unit}`, 'success');
    }

    deleteInventoryItem(itemId) {
        const items = this.getInventoryItems();
        const item = items.find(i => i.id === itemId);

        if (!item) {
            this.showNotification('Item not found', 'error');
            return;
        }

        if (!confirm(`Are you sure you want to delete "${item.name}"? This action cannot be undone.`)) {
            return;
        }

        const updatedItems = items.filter(i => i.id !== itemId);
        localStorage.setItem('inventoryItems', JSON.stringify(updatedItems));

        this.refreshInventoryPage();
        this.showNotification(`${item.name} deleted successfully!`, 'success');
    }

    refreshInventoryPage() {
        const inventoryPage = document.getElementById('inventoryPage');
        if (inventoryPage && inventoryPage.innerHTML.trim() !== '') {
            this.loadInventoryPage(inventoryPage);
        }
    }

    showLowStockDetails() {
        const items = this.getInventoryItems();
        const lowStockItems = items.filter(item => item.currentStock <= item.minimumStock);

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Low Stock Items</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="low-stock-list">
                        ${lowStockItems.map(item => `
                            <div class="low-stock-item">
                                <div class="item-info">
                                    <strong>${item.name}</strong>
                                    <span class="stock-info">Current: ${item.currentStock} ${item.unit} | Minimum: ${item.minimumStock} ${item.unit}</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="app.showRestockModal('${item.id}'); this.closest('.modal-overlay').remove();">
                                    <i class="fas fa-plus"></i> Restock
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showOutOfStockDetails() {
        const items = this.getInventoryItems();
        const outOfStockItems = items.filter(item => item.currentStock === 0);

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Out of Stock Items</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="out-of-stock-list">
                        ${outOfStockItems.map(item => `
                            <div class="out-of-stock-item">
                                <div class="item-info">
                                    <strong>${item.name}</strong>
                                    <span class="stock-info critical">OUT OF STOCK - Minimum: ${item.minimumStock} ${item.unit}</span>
                                </div>
                                <button class="btn btn-error btn-sm" onclick="app.showRestockModal('${item.id}'); this.closest('.modal-overlay').remove();">
                                    <i class="fas fa-exclamation-triangle"></i> Urgent Restock
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // Inventory Usage Tracking Methods
    showRecordUsageModal() {
        const inventoryItems = this.getInventoryItems();

        if (inventoryItems.length === 0) {
            this.showNotification('No inventory items available to record usage', 'warning');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-minus-circle"></i> Record Inventory Usage</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="recordUsageForm" onsubmit="app.recordInventoryUsage(event)">
                        <div class="form-group">
                            <label for="usageItem">Select Item *</label>
                            <select id="usageItem" class="form-control" required onchange="app.updateUsageItemDetails()">
                                <option value="">Choose an item...</option>
                                ${inventoryItems.map(item => `
                                    <option value="${item.id}" data-stock="${item.quantity}" data-unit="${item.unit}">
                                        ${item.name} (Available: ${item.quantity} ${item.unit})
                                    </option>
                                `).join('')}
                            </select>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="usageQuantity">Quantity Used *</label>
                                <input type="number" id="usageQuantity" class="form-control" placeholder="1" min="0.1" step="0.1" required>
                                <small class="form-text" id="availableStock">Select an item to see available stock</small>
                            </div>
                            <div class="form-group">
                                <label for="usageUnit">Unit</label>
                                <input type="text" id="usageUnit" class="form-control" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="usageReason">Reason/Purpose *</label>
                            <select id="usageReason" class="form-control" required>
                                <option value="">Select reason...</option>
                                <option value="cooking">Cooking/Food Preparation</option>
                                <option value="cleaning">Cleaning</option>
                                <option value="maintenance">Maintenance</option>
                                <option value="waste">Waste/Spoilage</option>
                                <option value="testing">Testing/Quality Check</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="usageNotes">Additional Notes (Optional)</label>
                            <textarea id="usageNotes" class="form-control" rows="3" placeholder="Additional details about this usage"></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Record Usage
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    updateUsageItemDetails() {
        const select = document.getElementById('usageItem');
        const selectedOption = select.options[select.selectedIndex];
        const quantityInput = document.getElementById('usageQuantity');
        const unitInput = document.getElementById('usageUnit');
        const stockInfo = document.getElementById('availableStock');

        if (selectedOption.value) {
            const availableStock = selectedOption.dataset.stock;
            const unit = selectedOption.dataset.unit;

            unitInput.value = unit;
            quantityInput.max = availableStock;
            stockInfo.textContent = `Available: ${availableStock} ${unit}`;
            stockInfo.style.color = 'var(--success-color)';
        } else {
            unitInput.value = '';
            quantityInput.max = '';
            stockInfo.textContent = 'Select an item to see available stock';
            stockInfo.style.color = 'var(--gray-600)';
        }
    }

    recordInventoryUsage(event) {
        event.preventDefault();

        const itemId = document.getElementById('usageItem').value;
        const quantity = parseFloat(document.getElementById('usageQuantity').value);
        const reason = document.getElementById('usageReason').value;
        const notes = document.getElementById('usageNotes').value.trim();

        if (!itemId || !quantity || !reason) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Get inventory items and find the selected item
        const inventoryItems = this.getInventoryItems();
        const itemIndex = inventoryItems.findIndex(item => item.id === itemId);

        if (itemIndex === -1) {
            this.showNotification('Selected item not found', 'error');
            return;
        }

        const item = inventoryItems[itemIndex];

        // Check if enough stock is available
        if (quantity > item.quantity) {
            this.showNotification(`Not enough stock available. Only ${item.quantity} ${item.unit} remaining`, 'error');
            return;
        }

        // Create usage record
        const usageRecord = {
            id: Date.now().toString(),
            itemId: itemId,
            itemName: item.name,
            quantityUsed: quantity,
            unit: item.unit,
            reason: reason,
            notes: notes,
            stockBefore: item.quantity,
            stockAfter: item.quantity - quantity,
            timestamp: new Date().toISOString(),
            date: new Date().toISOString().split('T')[0]
        };

        // Update inventory quantity
        inventoryItems[itemIndex].quantity -= quantity;
        inventoryItems[itemIndex].lastUpdated = new Date().toISOString();

        // Save updated inventory
        localStorage.setItem('inventoryItems', JSON.stringify(inventoryItems));

        // Save usage record
        const usageHistory = this.getInventoryUsageHistory();
        usageHistory.push(usageRecord);
        localStorage.setItem('inventoryUsageHistory', JSON.stringify(usageHistory));

        // Close modal and refresh page
        document.querySelector('.modal-overlay').remove();
        this.refreshInventoryPage();

        this.showNotification(`Usage recorded: ${quantity} ${item.unit} of ${item.name} used for ${reason}`, 'success');

        // Update dashboard if visible
        this.updateDashboardStats();
    }

    getInventoryUsageHistory() {
        return JSON.parse(localStorage.getItem('inventoryUsageHistory') || '[]');
    }

    generateUsageHistoryTable() {
        const usageHistory = this.getInventoryUsageHistory();
        const recentUsage = usageHistory
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, 10); // Show last 10 records

        if (recentUsage.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-history"></i>
                    <p>No usage history recorded yet</p>
                    <small>Click "Record Usage" to start tracking inventory usage</small>
                </div>
            `;
        }

        return `
            <table class="usage-history-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Item</th>
                        <th>Quantity Used</th>
                        <th>Remaining Stock</th>
                        <th>Reason</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    ${recentUsage.map(record => `
                        <tr>
                            <td>${new Date(record.timestamp).toLocaleDateString()}</td>
                            <td>${record.itemName}</td>
                            <td class="quantity-used">${record.quantityUsed} ${record.unit}</td>
                            <td class="remaining-stock">${record.stockAfter} ${record.unit}</td>
                            <td><span class="reason-badge ${record.reason}">${record.reason}</span></td>
                            <td>${record.notes || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    showFullUsageHistory() {
        const usageHistory = this.getInventoryUsageHistory();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-history"></i> Complete Usage History</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="usage-filters">
                        <div class="filter-group">
                            <label for="filterItem">Filter by Item:</label>
                            <select id="filterItem" class="form-control" onchange="app.filterUsageHistory()">
                                <option value="">All Items</option>
                                ${[...new Set(usageHistory.map(record => record.itemName))].map(itemName => `
                                    <option value="${itemName}">${itemName}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="filterReason">Filter by Reason:</label>
                            <select id="filterReason" class="form-control" onchange="app.filterUsageHistory()">
                                <option value="">All Reasons</option>
                                <option value="cooking">Cooking</option>
                                <option value="cleaning">Cleaning</option>
                                <option value="maintenance">Maintenance</option>
                                <option value="waste">Waste/Spoilage</option>
                                <option value="testing">Testing</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="filterDate">Filter by Date:</label>
                            <input type="date" id="filterDate" class="form-control" onchange="app.filterUsageHistory()">
                        </div>
                    </div>

                    <div class="usage-history-container" id="fullUsageHistory">
                        ${this.generateFullUsageHistoryTable(usageHistory)}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    generateFullUsageHistoryTable(records) {
        if (records.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-history"></i>
                    <p>No usage records found</p>
                </div>
            `;
        }

        return `
            <table class="usage-history-table full">
                <thead>
                    <tr>
                        <th>Date & Time</th>
                        <th>Item</th>
                        <th>Quantity Used</th>
                        <th>Stock Before</th>
                        <th>Stock After</th>
                        <th>Reason</th>
                        <th>Notes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${records
                        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                        .map(record => `
                        <tr>
                            <td>${new Date(record.timestamp).toLocaleString()}</td>
                            <td>${record.itemName}</td>
                            <td class="quantity-used">${record.quantityUsed} ${record.unit}</td>
                            <td>${record.stockBefore} ${record.unit}</td>
                            <td class="remaining-stock">${record.stockAfter} ${record.unit}</td>
                            <td><span class="reason-badge ${record.reason}">${record.reason}</span></td>
                            <td>${record.notes || '-'}</td>
                            <td>
                                <button class="btn-icon delete" onclick="app.deleteUsageRecord('${record.id}')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    filterUsageHistory() {
        const itemFilter = document.getElementById('filterItem').value;
        const reasonFilter = document.getElementById('filterReason').value;
        const dateFilter = document.getElementById('filterDate').value;

        let usageHistory = this.getInventoryUsageHistory();

        if (itemFilter) {
            usageHistory = usageHistory.filter(record => record.itemName === itemFilter);
        }

        if (reasonFilter) {
            usageHistory = usageHistory.filter(record => record.reason === reasonFilter);
        }

        if (dateFilter) {
            usageHistory = usageHistory.filter(record => record.date === dateFilter);
        }

        document.getElementById('fullUsageHistory').innerHTML = this.generateFullUsageHistoryTable(usageHistory);
    }

    deleteUsageRecord(recordId) {
        if (!confirm('Are you sure you want to delete this usage record?')) {
            return;
        }

        const usageHistory = this.getInventoryUsageHistory();
        const updatedHistory = usageHistory.filter(record => record.id !== recordId);

        localStorage.setItem('inventoryUsageHistory', JSON.stringify(updatedHistory));

        // Refresh the modal content
        this.filterUsageHistory();
        this.showNotification('Usage record deleted successfully!', 'success');
    }

    generateBillsList(filterType = 'all') {
        const orders = this.getFilteredOrders(filterType);

        if (orders.length === 0) {
            let emptyMessage = 'No bills found';
            switch(filterType) {
                case 'today':
                    emptyMessage = 'No bills found for today';
                    break;
                case 'yesterday':
                    emptyMessage = 'No bills found for yesterday';
                    break;
                case 'week':
                    emptyMessage = 'No bills found for this week';
                    break;
                default:
                    emptyMessage = 'No bills found';
            }

            return `
                <div class="empty-state">
                    <i class="fas fa-receipt" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <h3>${emptyMessage}</h3>
                    <p>Bills will appear here after processing orders through the POS system</p>
                </div>
            `;
        }

        // Sort orders by creation date (newest first)
        const sortedOrders = orders.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        return sortedOrders.map(order => `
            <div class="bill-card">
                <div class="bill-header">
                    <h4>${order.order_number}</h4>
                    <span class="payment-status ${order.status}">${order.status}</span>
                </div>
                <div class="bill-details">
                    <p><i class="fas fa-table"></i> ${order.table_number || 'Takeaway'}</p>
                    <p><i class="fas fa-users"></i> ${order.customer_count || 1} customers</p>
                    <p><i class="fas fa-shopping-cart"></i> ${order.items?.length || 0} items</p>
                    <p><i class="fas fa-dollar-sign"></i> PKR ${order.total_amount?.toFixed(0) || '0'}</p>
                    <p><i class="fas fa-clock"></i> ${new Date(order.created_at).toLocaleString()}</p>
                    ${order.per_head_charge > 0 ? `<p><i class="fas fa-user-plus"></i> Per-head: PKR ${order.per_head_charge}</p>` : ''}
                </div>
                <div class="bill-actions">
                    <button class="btn btn-outline btn-sm" onclick="app.printBill('${order.id}')" title="Print Receipt">
                        <i class="fas fa-print"></i> Print
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="app.editBill('${order.id}')" title="Edit Bill">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-error btn-sm" onclick="app.deleteBill('${order.id}')" title="Delete Bill">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Bill Management Methods
    editBill(orderId) {
        const orders = this.getOrders();
        const order = orders.find(o => o.id === orderId);

        if (!order) {
            this.showNotification('Bill not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Edit Bill - ${order.order_number}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editBillForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editOrderNumber">Order Number *</label>
                                <input type="text" id="editOrderNumber" name="orderNumber" class="form-control" value="${order.order_number}" required>
                            </div>
                            <div class="form-group">
                                <label for="editTableNumber">Table/Service *</label>
                                <input type="text" id="editTableNumber" name="tableNumber" class="form-control" value="${order.table_number || 'Takeaway'}" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="editCustomerCount">Customer Count *</label>
                                <input type="number" id="editCustomerCount" name="customerCount" class="form-control" min="1" value="${order.customer_count || 1}" required>
                            </div>
                            <div class="form-group">
                                <label for="editPaymentMethod">Payment Method *</label>
                                <select id="editPaymentMethod" name="paymentMethod" class="form-control" required>
                                    <option value="cash" ${order.payment_method === 'cash' ? 'selected' : ''}>Cash</option>
                                    <option value="card" ${order.payment_method === 'card' ? 'selected' : ''}>Card</option>
                                    <option value="udhaar" ${order.payment_method === 'udhaar' ? 'selected' : ''}>Udhaar</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Order Items</label>
                            <div id="editOrderItems" class="order-items-list">
                                ${order.items?.map((item, index) => `
                                    <div class="order-item-row">
                                        <input type="text" name="itemName_${index}" class="form-control" value="${item.name}" placeholder="Item name">
                                        <input type="number" name="itemQuantity_${index}" class="form-control" value="${item.quantity}" min="1" placeholder="Qty">
                                        <input type="number" name="itemPrice_${index}" class="form-control" value="${item.price}" min="0" step="0.01" placeholder="Price">
                                        <button type="button" class="btn btn-error btn-sm" onclick="this.closest('.order-item-row').remove(); app.updateBillTotals();">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                `).join('') || ''}
                            </div>
                            <button type="button" class="btn btn-outline btn-sm" onclick="app.addBillItem()">
                                <i class="fas fa-plus"></i> Add Item
                            </button>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="editPerHeadCharge">Per Head Charge (PKR)</label>
                                <input type="number" id="editPerHeadCharge" name="perHeadCharge" class="form-control" min="0" step="0.01" value="${order.per_head_charge || 0}">
                            </div>
                            <div class="form-group">
                                <label for="editTotalAmount">Total Amount (PKR) *</label>
                                <input type="number" id="editTotalAmount" name="totalAmount" class="form-control" min="0" step="0.01" value="${order.total_amount || 0}" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveBillEdit('${orderId}')">Save Changes</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    addBillItem() {
        const itemsList = document.getElementById('editOrderItems');
        const itemCount = itemsList.children.length;

        const newItemRow = document.createElement('div');
        newItemRow.className = 'order-item-row';
        newItemRow.innerHTML = `
            <input type="text" name="itemName_${itemCount}" class="form-control" placeholder="Item name">
            <input type="number" name="itemQuantity_${itemCount}" class="form-control" value="1" min="1" placeholder="Qty">
            <input type="number" name="itemPrice_${itemCount}" class="form-control" min="0" step="0.01" placeholder="Price">
            <button type="button" class="btn btn-error btn-sm" onclick="this.closest('.order-item-row').remove(); app.updateBillTotals();">
                <i class="fas fa-trash"></i>
            </button>
        `;

        itemsList.appendChild(newItemRow);
    }

    updateBillTotals() {
        // This method can be used to auto-calculate totals when items change
        // For now, we'll keep it simple and let users manually adjust the total
    }

    saveBillEdit(orderId) {
        const form = document.getElementById('editBillForm');
        const formData = new FormData(form);

        if (!formData.get('orderNumber') || !formData.get('totalAmount')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const orders = this.getOrders();
        const orderIndex = orders.findIndex(o => o.id === orderId);

        if (orderIndex === -1) {
            this.showNotification('Bill not found', 'error');
            return;
        }

        // Collect items from form
        const items = [];
        const itemRows = document.querySelectorAll('.order-item-row');
        itemRows.forEach((row, index) => {
            const name = formData.get(`itemName_${index}`);
            const quantity = parseInt(formData.get(`itemQuantity_${index}`));
            const price = parseFloat(formData.get(`itemPrice_${index}`));

            if (name && quantity && price) {
                items.push({
                    id: Date.now().toString() + index,
                    name: name,
                    quantity: quantity,
                    price: price
                });
            }
        });

        // Update the order
        orders[orderIndex] = {
            ...orders[orderIndex],
            order_number: formData.get('orderNumber'),
            table_number: formData.get('tableNumber'),
            customer_count: parseInt(formData.get('customerCount')),
            payment_method: formData.get('paymentMethod'),
            items: items,
            per_head_charge: parseFloat(formData.get('perHeadCharge')) || 0,
            total_amount: parseFloat(formData.get('totalAmount')),
            updated_at: new Date().toISOString()
        };

        localStorage.setItem('restaurantOrders', JSON.stringify(orders));

        // Close modal and refresh bills list
        document.querySelector('.modal-overlay').remove();
        this.refreshBillsList();

        this.showNotification('Bill updated successfully!', 'success');
        this.updateDashboardStats();
    }

    deleteBill(orderId) {
        const orders = this.getOrders();
        const order = orders.find(o => o.id === orderId);

        if (!order) {
            this.showNotification('Bill not found', 'error');
            return;
        }

        if (!confirm(`Are you sure you want to delete bill ${order.order_number}? This action cannot be undone.`)) {
            return;
        }

        const updatedOrders = orders.filter(o => o.id !== orderId);
        localStorage.setItem('restaurantOrders', JSON.stringify(updatedOrders));

        this.refreshBillsList();
        this.showNotification(`Bill ${order.order_number} deleted successfully!`, 'success');
        this.updateDashboardStats();
    }

    printBill(orderId) {
        const orders = this.getOrders();
        const order = orders.find(o => o.id === orderId);

        if (!order) {
            this.showNotification('Bill not found', 'error');
            return;
        }

        // Generate and print receipt using existing receipt format
        this.showPrintableReceipt(order);
    }

    showPrintableReceipt(order) {
        const itemsSubtotal = order.items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;
        const perHeadCharge = order.per_head_charge || 0;
        const additionalChargesTotal = order.additional_charges_total || 0;
        const discountAmount = order.discount_amount || 0;
        const subtotalAfterDiscount = itemsSubtotal + perHeadCharge + additionalChargesTotal - discountAmount;
        const tax = subtotalAfterDiscount * 0.16;
        const now = new Date(order.created_at);

        const receiptHTML = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt - ${order.order_number}</title>
                <style>
                    body { font-family: monospace; width: 300px; margin: 0 auto; padding: 20px; }
                    .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px; }
                    .item { display: flex; justify-content: space-between; margin: 5px 0; }
                    .total { border-top: 1px solid #000; padding-top: 10px; margin-top: 10px; font-weight: bold; }
                    .discount { color: #dc2626; }
                    .final-total {
                        font-size: 18px;
                        font-weight: bold;
                        border: 2px solid #000;
                        padding: 8px;
                        margin: 10px 0;
                        background: #f0f0f0;
                        text-align: center;
                    }
                    .final-total .amount {
                        font-size: 22px;
                        color: #fb923c;
                        font-weight: 900;
                    }
                    @media print { body { width: auto; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="logo">
                        <svg width="60" height="60" viewBox="0 0 100 100" style="margin: 0 auto 10px auto; display: block;">
                            <circle cx="50" cy="50" r="45" fill="#fb923c" stroke="#ea580c" stroke-width="3"/>
                            <text x="50" y="35" text-anchor="middle" fill="white" font-family="serif" font-size="16" font-weight="bold">زائقہ</text>
                            <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">ZAIQA</text>
                            <text x="50" y="70" text-anchor="middle" fill="white" font-family="Arial" font-size="8">RESTAURANT</text>
                        </svg>
                    </div>
                    <h2>ZAIQA AL-HAYAT</h2>
                    <p style="margin: 5px 0; font-size: 12px;">Authentic Pakistani Cuisine</p>
                    <p>Order #${order.order_number}</p>
                    <p>${now.toLocaleString()}</p>
                    <p>Table: ${order.table_number || 'Takeaway'}</p>
                    <p>Customers: ${order.customer_count || 1}</p>
                </div>

                <div class="items">
                    ${order.items?.map(item => `
                        <div class="item">
                            <span>${item.name} x${item.quantity}</span>
                            <span>PKR ${(item.price * item.quantity).toFixed(0)}</span>
                        </div>
                    `).join('') || ''}
                </div>

                <div class="total">
                    <div class="item">
                        <span>Items Subtotal:</span>
                        <span>PKR ${itemsSubtotal.toFixed(0)}</span>
                    </div>
                    ${perHeadCharge > 0 ? `
                        <div class="item">
                            <span>Per Head Charge (${order.customer_count} × PKR 100):</span>
                            <span>PKR ${perHeadCharge.toFixed(0)}</span>
                        </div>
                    ` : ''}
                    ${order.additional_charges?.map(charge => `
                        <div class="item">
                            <span>${charge.name}:</span>
                            <span>PKR ${charge.amount.toFixed(0)}</span>
                        </div>
                    `).join('') || ''}
                    ${discountAmount > 0 ? `
                        <div class="item discount">
                            <span>Discount (${order.discount?.reason || 'Applied'}):</span>
                            <span>-PKR ${discountAmount.toFixed(0)}</span>
                        </div>
                    ` : ''}
                    <div class="item">
                        <span>Subtotal:</span>
                        <span>PKR ${subtotalAfterDiscount.toFixed(0)}</span>
                    </div>
                    <div class="item">
                        <span>Tax (16%):</span>
                        <span>PKR ${tax.toFixed(0)}</span>
                    </div>
                </div>

                <div class="final-total">
                    <div>TOTAL AMOUNT</div>
                    <div class="amount">PKR ${order.total_amount.toFixed(0)}</div>
                </div>

                <div class="total">
                    <div class="item">
                        <span>Payment:</span>
                        <span>${order.payment_method?.toUpperCase() || 'CASH'}</span>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px; font-size: 12px;">
                    <p>Thank you for dining with us!</p>
                    <p>Visit us again soon</p>
                </div>
            </body>
            </html>
        `;

        const receiptWindow = window.open('', '_blank', 'width=400,height=600');
        receiptWindow.document.write(receiptHTML);
        receiptWindow.document.close();
        receiptWindow.print();
    }

    refreshBillsList() {
        const billingPage = document.getElementById('billingPage');
        if (billingPage && billingPage.innerHTML.trim() !== '') {
            this.loadBillingPage(billingPage);
        }
    }

    // Floating Action Button Methods
    showCalculator() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content calculator-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-calculator"></i> Calculator</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="calculator">
                        <div class="calculator-display">
                            <input type="text" id="calculatorDisplay" class="calculator-screen" value="0"
                                   onkeydown="app.handleCalculatorKeyboard(event)"
                                   onkeyup="app.handleCalculatorInput(event)"
                                   autocomplete="off">
                        </div>
                        <div class="calculator-buttons">
                            <button class="calc-btn calc-clear" onclick="app.calculatorClear()">C</button>
                            <button class="calc-btn calc-operator" onclick="app.calculatorInput('/')">/</button>
                            <button class="calc-btn calc-operator" onclick="app.calculatorInput('*')">×</button>
                            <button class="calc-btn calc-operator" onclick="app.calculatorInput('-')">-</button>

                            <button class="calc-btn calc-number" onclick="app.calculatorInput('7')">7</button>
                            <button class="calc-btn calc-number" onclick="app.calculatorInput('8')">8</button>
                            <button class="calc-btn calc-number" onclick="app.calculatorInput('9')">9</button>
                            <button class="calc-btn calc-operator calc-plus" onclick="app.calculatorInput('+')">+</button>

                            <button class="calc-btn calc-number" onclick="app.calculatorInput('4')">4</button>
                            <button class="calc-btn calc-number" onclick="app.calculatorInput('5')">5</button>
                            <button class="calc-btn calc-number" onclick="app.calculatorInput('6')">6</button>

                            <button class="calc-btn calc-number" onclick="app.calculatorInput('1')">1</button>
                            <button class="calc-btn calc-number" onclick="app.calculatorInput('2')">2</button>
                            <button class="calc-btn calc-number" onclick="app.calculatorInput('3')">3</button>
                            <button class="calc-btn calc-equals" onclick="app.calculatorEquals()">=</button>

                            <button class="calc-btn calc-number calc-zero" onclick="app.calculatorInput('0')">0</button>
                            <button class="calc-btn calc-number" onclick="app.calculatorInput('.')">.</button>
                        </div>
                        <div class="calculator-help">
                            <small>Use keyboard: Numbers, +, -, *, /, Enter (=), Escape (close), C (clear)</small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Focus on the display for immediate keyboard input
        setTimeout(() => {
            const display = document.getElementById('calculatorDisplay');
            if (display) {
                display.focus();
                display.select();
            }
        }, 100);
    }

    // Duplicate method removed - using the original showPOSSystem method

    openPOS() {
        // Alias for showPOSSystem for backward compatibility
        this.showPOSSystem();
    }

    // Calculator Methods
    calculatorInput(value) {
        const display = document.getElementById('calculatorDisplay');
        if (display.value === '0' && value !== '.') {
            display.value = value;
        } else {
            display.value += value;
        }
    }

    calculatorClear() {
        const display = document.getElementById('calculatorDisplay');
        display.value = '0';
    }

    calculatorEquals() {
        const display = document.getElementById('calculatorDisplay');
        try {
            // Replace × with * for evaluation
            const expression = display.value.replace(/×/g, '*');
            const result = eval(expression);
            display.value = result.toString();
        } catch (error) {
            display.value = 'Error';
            setTimeout(() => {
                display.value = '0';
            }, 1500);
        }
    }

    // Enhanced Calculator Keyboard Support
    handleCalculatorKeyboard(event) {
        const key = event.key;

        // Handle special keys
        if (key === 'Enter') {
            event.preventDefault();
            this.calculatorEquals();
            return;
        }

        if (key === 'Escape') {
            event.preventDefault();
            const modal = event.target.closest('.modal-overlay');
            if (modal) {
                modal.remove();
            }
            return;
        }

        if (key === 'c' || key === 'C') {
            event.preventDefault();
            this.calculatorClear();
            return;
        }

        // Allow only valid calculator characters
        const validKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                          '+', '-', '*', '/', '.', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];

        if (!validKeys.includes(key)) {
            event.preventDefault();
        }
    }

    handleCalculatorInput(event) {
        const display = event.target;
        const key = event.key;

        // Handle backspace and delete
        if (key === 'Backspace' || key === 'Delete') {
            if (display.value.length <= 1) {
                display.value = '0';
            }
            return;
        }

        // Replace * with × for display
        if (key === '*') {
            const cursorPos = display.selectionStart;
            display.value = display.value.substring(0, cursorPos - 1) + '×' + display.value.substring(cursorPos);
            display.setSelectionRange(cursorPos, cursorPos);
        }

        // Clear initial 0
        if (display.value === '0' && key !== '.' && key !== 'Backspace' && key !== 'Delete') {
            display.value = '';
        }
    }

    // Udhars (Customer Credit) Management Methods
    generateUdharsTable() {
        const udhars = this.getUdhars();

        if (udhars.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-user-clock" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <h3>No Customer Credit Accounts</h3>
                    <p>Start by adding your first udhar account</p>
                    <button class="btn btn-primary" onclick="app.showAddUdharModal()">Add First Udhar</button>
                </div>
            `;
        }

        return `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Customer Name</th>
                        <th>Phone</th>
                        <th>Outstanding Amount</th>
                        <th>Last Transaction</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${udhars.map(udhar => `
                        <tr>
                            <td>
                                <div class="customer-info">
                                    <strong>${udhar.customerName}</strong>
                                    <small>${udhar.address || 'No address'}</small>
                                </div>
                            </td>
                            <td>${udhar.phone || 'N/A'}</td>
                            <td>
                                <span class="amount ${udhar.balance < 0 ? 'negative' : 'positive'}">
                                    PKR ${Math.abs(udhar.balance).toLocaleString()}
                                </span>
                            </td>
                            <td>${new Date(udhar.lastTransaction).toLocaleDateString()}</td>
                            <td>
                                <span class="status-badge ${udhar.balance > 1000 ? 'high' : udhar.balance > 0 ? 'medium' : 'good'}">
                                    ${udhar.balance > 1000 ? 'High' : udhar.balance > 0 ? 'Medium' : 'Clear'}
                                </span>
                            </td>
                            <td>
                                <button class="btn-icon" onclick="app.showUdharDetails('${udhar.id}')" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" onclick="app.showAddPaymentModal('${udhar.id}')" title="Add Payment">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn-icon" onclick="app.showAddUdharTransactionModal('${udhar.id}')" title="Add Credit">
                                    <i class="fas fa-credit-card"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    generateKhataTable() {
        const suppliers = this.getSuppliers();

        if (suppliers.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-store" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <h3>No Supplier Accounts</h3>
                    <p>Start by adding your first supplier account</p>
                    <button class="btn btn-primary" onclick="app.showAddSupplierModal()">Add First Supplier</button>
                </div>
            `;
        }

        return `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Supplier Name</th>
                        <th>Contact</th>
                        <th>Balance</th>
                        <th>Last Transaction</th>
                        <th>Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${suppliers.map(supplier => `
                        <tr>
                            <td>
                                <div class="supplier-info">
                                    <strong>${supplier.name}</strong>
                                    <small>${supplier.category}</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div>${supplier.phone || 'N/A'}</div>
                                    <small>${supplier.address || 'No address'}</small>
                                </div>
                            </td>
                            <td>
                                <span class="amount ${supplier.balance < 0 ? 'negative' : 'positive'}">
                                    PKR ${Math.abs(supplier.balance).toLocaleString()}
                                    <small>${supplier.balance < 0 ? '(We owe)' : '(They owe)'}</small>
                                </span>
                            </td>
                            <td>${new Date(supplier.lastTransaction).toLocaleDateString()}</td>
                            <td>
                                <span class="category-badge">${supplier.category}</span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-sm btn-primary" onclick="app.showRecordPurchaseModal('${supplier.id}')" title="Record Purchase">
                                        <i class="fas fa-shopping-cart"></i> Purchase
                                    </button>
                                    <button class="btn btn-sm btn-success" onclick="app.showMakePaymentModal('${supplier.id}')" title="Make Payment">
                                        <i class="fas fa-money-bill-wave"></i> Payment
                                    </button>
                                    <button class="btn btn-sm btn-outline" onclick="app.showSupplierStatement('${supplier.id}')" title="View Statement">
                                        <i class="fas fa-file-alt"></i> Statement
                                    </button>
                                    <div class="dropdown">
                                        <button class="btn-icon dropdown-toggle" onclick="app.toggleSupplierDropdown('${supplier.id}')" title="More Actions">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div class="dropdown-menu" id="dropdown-${supplier.id}">
                                            <button onclick="app.showSupplierDetails('${supplier.id}')">
                                                <i class="fas fa-eye"></i> View Details
                                            </button>
                                            <button onclick="app.editSupplier('${supplier.id}')">
                                                <i class="fas fa-edit"></i> Edit Supplier
                                            </button>
                                            <button onclick="app.deleteSupplier('${supplier.id}')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    getUdhars() {
        const saved = localStorage.getItem('customerUdhars');
        return saved ? JSON.parse(saved) : [];
    }

    getSuppliers() {
        const saved = localStorage.getItem('supplierAccounts');
        return saved ? JSON.parse(saved) : [];
    }

    updateUdharsSummary() {
        const udhars = this.getUdhars();
        const totalCustomers = udhars.length;
        const totalAmount = udhars.reduce((sum, udhar) => sum + Math.max(0, udhar.balance), 0);
        const overdueCount = udhars.filter(udhar => udhar.balance > 0 && this.isOverdue(udhar.lastTransaction)).length;

        const customersEl = document.getElementById('totalCustomers');
        const amountEl = document.getElementById('totalUdharAmount');
        const overdueEl = document.getElementById('overdueUdhars');

        if (customersEl) customersEl.textContent = totalCustomers.toString();
        if (amountEl) amountEl.textContent = this.formatCurrency(totalAmount);
        if (overdueEl) overdueEl.textContent = overdueCount.toString();
    }

    updateKhataSummary() {
        const suppliers = this.getSuppliers();
        const totalSuppliers = suppliers.length;
        const totalPayable = suppliers.reduce((sum, supplier) => sum + Math.max(0, -supplier.balance), 0);
        const totalReceivable = suppliers.reduce((sum, supplier) => sum + Math.max(0, supplier.balance), 0);

        const suppliersEl = document.getElementById('totalSuppliers');
        const payableEl = document.getElementById('totalPayable');
        const receivableEl = document.getElementById('totalReceivable');

        if (suppliersEl) suppliersEl.textContent = totalSuppliers.toString();
        if (payableEl) payableEl.textContent = this.formatCurrency(totalPayable);
        if (receivableEl) receivableEl.textContent = this.formatCurrency(totalReceivable);
    }

    showAddUdharModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add Customer Udhar</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addUdharForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerName">Customer Name *</label>
                                <input type="text" id="customerName" name="customerName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="customerPhone">Phone Number</label>
                                <input type="tel" id="customerPhone" name="customerPhone" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="customerAddress">Address</label>
                            <textarea id="customerAddress" name="customerAddress" class="form-control" rows="2"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="initialAmount">Initial Credit Amount (PKR) *</label>
                                <input type="number" id="initialAmount" name="initialAmount" class="form-control" min="0" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="transactionDate">Date *</label>
                                <input type="date" id="transactionDate" name="transactionDate" class="form-control" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <input type="text" id="description" name="description" class="form-control" placeholder="e.g., Food order, catering service">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveUdhar()">Add Udhar</button>
                </div>
            </div>
        `;

        // Set today's date as default
        document.body.appendChild(modal);
        document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
    }

    showAddSupplierModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add Supplier Account</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addSupplierForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierName">Supplier Name *</label>
                                <input type="text" id="supplierName" name="supplierName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="supplierCategory">Category *</label>
                                <select id="supplierCategory" name="supplierCategory" class="form-control" required>
                                    <option value="">Select Category</option>
                                    <option value="Meat & Poultry">Meat & Poultry (Mughi Wala)</option>
                                    <option value="Beverages">Beverages (Pepsi Wala)</option>
                                    <option value="Vegetables">Vegetables (Sabzi Wala)</option>
                                    <option value="Dairy">Dairy Products</option>
                                    <option value="Spices">Spices & Condiments</option>
                                    <option value="Equipment">Equipment & Supplies</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierPhone">Phone Number</label>
                                <input type="tel" id="supplierPhone" name="supplierPhone" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="supplierEmail">Email</label>
                                <input type="email" id="supplierEmail" name="supplierEmail" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="supplierAddress">Address</label>
                            <textarea id="supplierAddress" name="supplierAddress" class="form-control" rows="2"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="initialBalance">Initial Balance (PKR)</label>
                            <input type="number" id="initialBalance" name="initialBalance" class="form-control" step="0.01" value="0">
                            <small class="form-text">Positive = They owe us, Negative = We owe them</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveSupplier()">Add Supplier</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    saveUdhar() {
        const form = document.getElementById('addUdharForm');
        const formData = new FormData(form);

        if (!formData.get('customerName') || !formData.get('initialAmount') || !formData.get('transactionDate')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const udhars = this.getUdhars();
        const newUdhar = {
            id: Date.now().toString(),
            customerName: formData.get('customerName'),
            phone: formData.get('customerPhone') || '',
            address: formData.get('customerAddress') || '',
            balance: parseFloat(formData.get('initialAmount')),
            lastTransaction: formData.get('transactionDate'),
            transactions: [{
                id: Date.now().toString(),
                type: 'credit',
                amount: parseFloat(formData.get('initialAmount')),
                description: formData.get('description') || 'Initial credit',
                date: formData.get('transactionDate'),
                createdAt: new Date().toISOString()
            }],
            createdAt: new Date().toISOString()
        };

        udhars.push(newUdhar);
        localStorage.setItem('customerUdhars', JSON.stringify(udhars));

        document.querySelector('.modal-overlay').remove();
        this.loadUdharsPage(document.getElementById('udharsPage'));
        this.showNotification('Customer udhar added successfully!', 'success');
    }

    saveSupplier() {
        const form = document.getElementById('addSupplierForm');
        const formData = new FormData(form);

        if (!formData.get('supplierName') || !formData.get('supplierCategory')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const suppliers = this.getSuppliers();
        const newSupplier = {
            id: Date.now().toString(),
            name: formData.get('supplierName'),
            category: formData.get('supplierCategory'),
            phone: formData.get('supplierPhone') || '',
            email: formData.get('supplierEmail') || '',
            address: formData.get('supplierAddress') || '',
            balance: parseFloat(formData.get('initialBalance')) || 0,
            lastTransaction: new Date().toISOString(),
            transactions: [],
            createdAt: new Date().toISOString()
        };

        // Add initial transaction if balance is not zero
        if (newSupplier.balance !== 0) {
            newSupplier.transactions.push({
                id: Date.now().toString(),
                type: newSupplier.balance > 0 ? 'receivable' : 'payable',
                amount: Math.abs(newSupplier.balance),
                description: 'Initial balance',
                date: new Date().toISOString().split('T')[0],
                createdAt: new Date().toISOString()
            });
        }

        suppliers.push(newSupplier);
        localStorage.setItem('supplierAccounts', JSON.stringify(suppliers));

        document.querySelector('.modal-overlay').remove();
        this.loadKhataPage(document.getElementById('khataPage'));
        this.showNotification('Supplier account added successfully!', 'success');
    }

    // Enhanced Khata Management Methods
    showRecordPurchaseModal(supplierId) {
        const suppliers = this.getSuppliers();
        const supplier = suppliers.find(s => s.id === supplierId);

        if (!supplier) {
            this.showNotification('Supplier not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-shopping-cart"></i> Record Purchase from ${supplier.name}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="supplier-info-card">
                        <h4>Supplier Information</h4>
                        <div class="info-row">
                            <span>Name:</span>
                            <span>${supplier.name}</span>
                        </div>
                        <div class="info-row">
                            <span>Category:</span>
                            <span>${supplier.category}</span>
                        </div>
                        <div class="info-row">
                            <span>Current Balance:</span>
                            <span class="amount ${supplier.balance < 0 ? 'negative' : 'positive'}">
                                PKR ${Math.abs(supplier.balance).toLocaleString()}
                                ${supplier.balance < 0 ? '(We owe)' : '(They owe)'}
                            </span>
                        </div>
                    </div>

                    <form id="recordPurchaseForm">
                        <div class="form-group">
                            <label for="purchaseDescription">Purchase Description *</label>
                            <input type="text" id="purchaseDescription" class="form-control" placeholder="e.g., Chicken 5kg, Rice 10kg" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="purchaseAmount">Purchase Amount (PKR) *</label>
                                <input type="number" id="purchaseAmount" class="form-control" min="0" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="purchaseDate">Purchase Date *</label>
                                <input type="date" id="purchaseDate" class="form-control" value="${new Date().toISOString().split('T')[0]}" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="paymentStatus">Payment Status *</label>
                            <select id="paymentStatus" class="form-control" required onchange="app.togglePaymentFields()">
                                <option value="credit">On Credit (Udhar)</option>
                                <option value="paid">Paid Immediately</option>
                                <option value="partial">Partial Payment</option>
                            </select>
                        </div>

                        <div id="paymentFields" style="display: none;">
                            <div class="form-group">
                                <label for="paidAmount">Amount Paid (PKR)</label>
                                <input type="number" id="paidAmount" class="form-control" min="0" step="0.01">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="purchaseNotes">Additional Notes</label>
                            <textarea id="purchaseNotes" class="form-control" rows="3" placeholder="Any additional details about this purchase"></textarea>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="addToExpenses" checked>
                                <span class="checkmark"></span>
                                Add this purchase to expenses
                            </label>
                        </div>

                        <div class="balance-preview">
                            <h5><i class="fas fa-calculator"></i> Balance Preview:</h5>
                            <div class="preview-calculation" id="balancePreview">
                                <div>Current Balance: PKR ${Math.abs(supplier.balance).toLocaleString()} ${supplier.balance < 0 ? '(We owe)' : '(They owe)'}</div>
                                <div id="newBalancePreview">Select payment status to see new balance</div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="app.savePurchaseRecord('${supplierId}')">
                                <i class="fas fa-save"></i> Record Purchase
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listeners for real-time balance calculation
        document.getElementById('purchaseAmount').addEventListener('input', () => this.updateBalancePreview(supplier));
        document.getElementById('paidAmount').addEventListener('input', () => this.updateBalancePreview(supplier));
    }

    togglePaymentFields() {
        const paymentStatus = document.getElementById('paymentStatus').value;
        const paymentFields = document.getElementById('paymentFields');

        if (paymentStatus === 'paid' || paymentStatus === 'partial') {
            paymentFields.style.display = 'block';
            if (paymentStatus === 'paid') {
                const purchaseAmount = parseFloat(document.getElementById('purchaseAmount').value) || 0;
                document.getElementById('paidAmount').value = purchaseAmount;
            }
        } else {
            paymentFields.style.display = 'none';
            document.getElementById('paidAmount').value = '';
        }

        // Update balance preview
        const suppliers = this.getSuppliers();
        const supplierId = document.querySelector('[onclick*="savePurchaseRecord"]').onclick.toString().match(/'([^']+)'/)[1];
        const supplier = suppliers.find(s => s.id === supplierId);
        if (supplier) {
            this.updateBalancePreview(supplier);
        }
    }

    updateBalancePreview(supplier) {
        const purchaseAmount = parseFloat(document.getElementById('purchaseAmount').value) || 0;
        const paymentStatus = document.getElementById('paymentStatus').value;
        const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;

        let newBalance = supplier.balance;

        if (paymentStatus === 'credit') {
            // Full amount on credit - we owe more
            newBalance -= purchaseAmount;
        } else if (paymentStatus === 'paid') {
            // Paid immediately - no change to balance
            // newBalance remains the same
        } else if (paymentStatus === 'partial') {
            // Partial payment - remaining amount on credit
            const remainingAmount = purchaseAmount - paidAmount;
            newBalance -= remainingAmount;
        }

        const preview = document.getElementById('newBalancePreview');
        if (preview) {
            preview.innerHTML = `
                <div style="margin-top: 0.5rem; padding-top: 0.5rem; border-top: 1px solid var(--gray-300);">
                    <strong>New Balance: PKR ${Math.abs(newBalance).toLocaleString()} ${newBalance < 0 ? '(We owe)' : '(They owe)'}</strong>
                </div>
            `;
        }
    }

    savePurchaseRecord(supplierId) {
        const description = document.getElementById('purchaseDescription').value.trim();
        const amount = parseFloat(document.getElementById('purchaseAmount').value);
        const date = document.getElementById('purchaseDate').value;
        const paymentStatus = document.getElementById('paymentStatus').value;
        const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
        const notes = document.getElementById('purchaseNotes').value.trim();
        const addToExpenses = document.getElementById('addToExpenses').checked;

        if (!description || !amount || !date) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        if ((paymentStatus === 'paid' || paymentStatus === 'partial') && paidAmount <= 0) {
            this.showNotification('Please enter the paid amount', 'error');
            return;
        }

        if (paymentStatus === 'partial' && paidAmount > amount) {
            this.showNotification('Paid amount cannot be greater than purchase amount', 'error');
            return;
        }

        // Get suppliers and update the specific supplier
        const suppliers = this.getSuppliers();
        const supplierIndex = suppliers.findIndex(s => s.id === supplierId);

        if (supplierIndex === -1) {
            this.showNotification('Supplier not found', 'error');
            return;
        }

        const supplier = suppliers[supplierIndex];

        // Calculate new balance
        let balanceChange = 0;
        if (paymentStatus === 'credit') {
            balanceChange = -amount; // We owe more
        } else if (paymentStatus === 'partial') {
            balanceChange = -(amount - paidAmount); // We owe the remaining amount
        }
        // For 'paid', no balance change

        // Create transaction record
        const transaction = {
            id: Date.now().toString(),
            type: 'purchase',
            description: description,
            amount: amount,
            paidAmount: paidAmount,
            paymentStatus: paymentStatus,
            balanceChange: balanceChange,
            notes: notes,
            date: date,
            timestamp: new Date().toISOString()
        };

        // Update supplier
        suppliers[supplierIndex].balance += balanceChange;
        suppliers[supplierIndex].lastTransaction = new Date().toISOString();
        suppliers[supplierIndex].transactions = suppliers[supplierIndex].transactions || [];
        suppliers[supplierIndex].transactions.push(transaction);

        // Save suppliers
        localStorage.setItem('supplierAccounts', JSON.stringify(suppliers));

        // Add to expenses if requested
        if (addToExpenses) {
            const expense = {
                id: Date.now().toString() + '_purchase',
                item: `Purchase from ${supplier.name}: ${description}`,
                amount: amount,
                category: 'purchases',
                notes: notes || `Purchase from ${supplier.name}`,
                date: date,
                timestamp: new Date().toISOString(),
                source: 'supplier_purchase',
                supplierId: supplierId,
                paymentStatus: paymentStatus,
                paidAmount: paidAmount
            };

            const expenses = this.getExpenses();
            expenses.push(expense);
            localStorage.setItem('restaurantExpenses', JSON.stringify(expenses));
        }

        // Close modal and refresh
        document.querySelector('.modal-overlay').remove();
        this.loadKhataPage(document.getElementById('khataPage'));
        this.updateDashboardStats();

        this.showNotification(`Purchase recorded successfully! ${supplier.name}'s balance updated.`, 'success');
    }

    showMakePaymentModal(supplierId) {
        const suppliers = this.getSuppliers();
        const supplier = suppliers.find(s => s.id === supplierId);

        if (!supplier) {
            this.showNotification('Supplier not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-money-bill-wave"></i> Make Payment to ${supplier.name}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="supplier-info-card">
                        <h4>Payment Information</h4>
                        <div class="info-row">
                            <span>Supplier:</span>
                            <span>${supplier.name}</span>
                        </div>
                        <div class="info-row">
                            <span>Current Balance:</span>
                            <span class="amount ${supplier.balance < 0 ? 'negative' : 'positive'}">
                                PKR ${Math.abs(supplier.balance).toLocaleString()}
                                ${supplier.balance < 0 ? '(We owe them)' : '(They owe us)'}
                            </span>
                        </div>
                        ${supplier.balance >= 0 ? `
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                This supplier doesn't owe us money. You can still record a payment if needed.
                            </div>
                        ` : `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                Outstanding amount to pay: PKR ${Math.abs(supplier.balance).toLocaleString()}
                            </div>
                        `}
                    </div>

                    <form id="makePaymentForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="paymentAmount">Payment Amount (PKR) *</label>
                                <input type="number" id="paymentAmount" class="form-control" min="0" step="0.01"
                                       value="${supplier.balance < 0 ? Math.abs(supplier.balance) : ''}" required>
                                <small class="form-text">Enter the amount you are paying to this supplier</small>
                            </div>
                            <div class="form-group">
                                <label for="paymentDate">Payment Date *</label>
                                <input type="date" id="paymentDate" class="form-control" value="${new Date().toISOString().split('T')[0]}" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="paymentMethod">Payment Method *</label>
                            <select id="paymentMethod" class="form-control" required>
                                <option value="cash">Cash</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="check">Check</option>
                                <option value="online">Online Payment</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="paymentReference">Reference/Receipt Number</label>
                            <input type="text" id="paymentReference" class="form-control" placeholder="e.g., Check number, transaction ID">
                        </div>

                        <div class="form-group">
                            <label for="paymentNotes">Payment Notes</label>
                            <textarea id="paymentNotes" class="form-control" rows="3" placeholder="Any additional details about this payment"></textarea>
                        </div>

                        <div class="balance-preview">
                            <h5><i class="fas fa-calculator"></i> Balance After Payment:</h5>
                            <div class="preview-calculation" id="paymentBalancePreview">
                                <div>Current Balance: PKR ${Math.abs(supplier.balance).toLocaleString()} ${supplier.balance < 0 ? '(We owe)' : '(They owe)'}</div>
                                <div id="newPaymentBalance">Enter payment amount to see new balance</div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                            <button type="button" class="btn btn-success" onclick="app.savePaymentRecord('${supplierId}')">
                                <i class="fas fa-check"></i> Record Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listener for real-time balance calculation
        document.getElementById('paymentAmount').addEventListener('input', () => this.updatePaymentBalancePreview(supplier));
    }

    updatePaymentBalancePreview(supplier) {
        const paymentAmount = parseFloat(document.getElementById('paymentAmount').value) || 0;
        const newBalance = supplier.balance + paymentAmount; // Payment reduces what we owe

        const preview = document.getElementById('newPaymentBalance');
        if (preview) {
            preview.innerHTML = `
                <div style="margin-top: 0.5rem; padding-top: 0.5rem; border-top: 1px solid var(--gray-300);">
                    <strong>New Balance: PKR ${Math.abs(newBalance).toLocaleString()} ${newBalance < 0 ? '(We owe)' : '(They owe)'}</strong>
                </div>
            `;
        }
    }

    savePaymentRecord(supplierId) {
        const amount = parseFloat(document.getElementById('paymentAmount').value);
        const date = document.getElementById('paymentDate').value;
        const method = document.getElementById('paymentMethod').value;
        const reference = document.getElementById('paymentReference').value.trim();
        const notes = document.getElementById('paymentNotes').value.trim();

        if (!amount || !date || !method) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        if (amount <= 0) {
            this.showNotification('Payment amount must be greater than zero', 'error');
            return;
        }

        // Get suppliers and update the specific supplier
        const suppliers = this.getSuppliers();
        const supplierIndex = suppliers.findIndex(s => s.id === supplierId);

        if (supplierIndex === -1) {
            this.showNotification('Supplier not found', 'error');
            return;
        }

        const supplier = suppliers[supplierIndex];

        // Create transaction record
        const transaction = {
            id: Date.now().toString(),
            type: 'payment',
            description: `Payment to ${supplier.name}`,
            amount: amount,
            method: method,
            reference: reference,
            balanceChange: amount, // Payment reduces what we owe
            notes: notes,
            date: date,
            timestamp: new Date().toISOString()
        };

        // Update supplier
        suppliers[supplierIndex].balance += amount; // Payment reduces debt (increases balance)
        suppliers[supplierIndex].lastTransaction = new Date().toISOString();
        suppliers[supplierIndex].transactions = suppliers[supplierIndex].transactions || [];
        suppliers[supplierIndex].transactions.push(transaction);

        // Save suppliers
        localStorage.setItem('supplierAccounts', JSON.stringify(suppliers));

        // Add to expenses
        const expense = {
            id: Date.now().toString() + '_payment',
            item: `Payment to ${supplier.name}`,
            amount: amount,
            category: 'supplier_payments',
            notes: notes || `Payment to ${supplier.name} via ${method}`,
            date: date,
            timestamp: new Date().toISOString(),
            source: 'supplier_payment',
            supplierId: supplierId,
            paymentMethod: method,
            reference: reference
        };

        const expenses = this.getExpenses();
        expenses.push(expense);
        localStorage.setItem('restaurantExpenses', JSON.stringify(expenses));

        // Close modal and refresh
        document.querySelector('.modal-overlay').remove();
        this.loadKhataPage(document.getElementById('khataPage'));
        this.updateDashboardStats();

        this.showNotification(`Payment of PKR ${amount.toLocaleString()} recorded successfully!`, 'success');
    }

    showSupplierStatement(supplierId) {
        const suppliers = this.getSuppliers();
        const supplier = suppliers.find(s => s.id === supplierId);

        if (!supplier) {
            this.showNotification('Supplier not found', 'error');
            return;
        }

        const transactions = supplier.transactions || [];
        const sortedTransactions = transactions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-file-alt"></i> Account Statement - ${supplier.name}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="statement-header">
                        <div class="supplier-details">
                            <h4>${supplier.name}</h4>
                            <p><strong>Category:</strong> ${supplier.category}</p>
                            <p><strong>Phone:</strong> ${supplier.phone || 'N/A'}</p>
                            <p><strong>Address:</strong> ${supplier.address || 'N/A'}</p>
                        </div>
                        <div class="balance-summary">
                            <div class="current-balance">
                                <h4>Current Balance</h4>
                                <div class="amount ${supplier.balance < 0 ? 'negative' : 'positive'}">
                                    PKR ${Math.abs(supplier.balance).toLocaleString()}
                                </div>
                                <small>${supplier.balance < 0 ? 'We owe them' : 'They owe us'}</small>
                            </div>
                        </div>
                    </div>

                    <div class="statement-filters">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="statementStartDate">From Date:</label>
                                <input type="date" id="statementStartDate" class="form-control" onchange="app.filterSupplierStatement('${supplierId}')">
                            </div>
                            <div class="filter-group">
                                <label for="statementEndDate">To Date:</label>
                                <input type="date" id="statementEndDate" class="form-control" value="${new Date().toISOString().split('T')[0]}" onchange="app.filterSupplierStatement('${supplierId}')">
                            </div>
                            <div class="filter-group">
                                <label for="transactionType">Type:</label>
                                <select id="transactionType" class="form-control" onchange="app.filterSupplierStatement('${supplierId}')">
                                    <option value="">All Transactions</option>
                                    <option value="purchase">Purchases</option>
                                    <option value="payment">Payments</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="statement-table" id="statementTransactions">
                        ${this.generateSupplierStatementTable(sortedTransactions)}
                    </div>

                    <div class="statement-actions">
                        <button class="btn btn-outline" onclick="app.printSupplierStatement('${supplierId}')">
                            <i class="fas fa-print"></i> Print Statement
                        </button>
                        <button class="btn btn-primary" onclick="app.exportSupplierStatement('${supplierId}')">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Set default start date to 30 days ago
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        document.getElementById('statementStartDate').value = startDate.toISOString().split('T')[0];
    }

    generateSupplierStatementTable(transactions) {
        if (transactions.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-file-alt"></i>
                    <p>No transactions found</p>
                </div>
            `;
        }

        let runningBalance = 0;

        return `
            <table class="statement-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>Balance Change</th>
                        <th>Running Balance</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    ${transactions.map(transaction => {
                        runningBalance += transaction.balanceChange || 0;
                        return `
                            <tr>
                                <td>${new Date(transaction.date).toLocaleDateString()}</td>
                                <td>${transaction.description}</td>
                                <td>
                                    <span class="transaction-type ${transaction.type}">
                                        ${transaction.type === 'purchase' ? 'Purchase' : 'Payment'}
                                    </span>
                                </td>
                                <td class="amount">PKR ${transaction.amount.toLocaleString()}</td>
                                <td class="balance-change ${transaction.balanceChange < 0 ? 'negative' : 'positive'}">
                                    ${transaction.balanceChange < 0 ? '-' : '+'}PKR ${Math.abs(transaction.balanceChange || 0).toLocaleString()}
                                </td>
                                <td class="running-balance ${runningBalance < 0 ? 'negative' : 'positive'}">
                                    PKR ${Math.abs(runningBalance).toLocaleString()}
                                    <small>${runningBalance < 0 ? '(Owe)' : '(Credit)'}</small>
                                </td>
                                <td>${transaction.notes || '-'}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;
    }

    filterSupplierStatement(supplierId) {
        const suppliers = this.getSuppliers();
        const supplier = suppliers.find(s => s.id === supplierId);

        if (!supplier) return;

        const startDate = document.getElementById('statementStartDate').value;
        const endDate = document.getElementById('statementEndDate').value;
        const transactionType = document.getElementById('transactionType').value;

        let transactions = supplier.transactions || [];

        // Filter by date range
        if (startDate) {
            transactions = transactions.filter(t => t.date >= startDate);
        }
        if (endDate) {
            transactions = transactions.filter(t => t.date <= endDate);
        }

        // Filter by transaction type
        if (transactionType) {
            transactions = transactions.filter(t => t.type === transactionType);
        }

        // Sort by date (newest first)
        transactions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        // Update the table
        document.getElementById('statementTransactions').innerHTML = this.generateSupplierStatementTable(transactions);
    }

    toggleSupplierDropdown(supplierId) {
        // Close all other dropdowns
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            if (menu.id !== `dropdown-${supplierId}`) {
                menu.classList.remove('show');
            }
        });

        // Toggle current dropdown
        const dropdown = document.getElementById(`dropdown-${supplierId}`);
        dropdown.classList.toggle('show');
    }

    deleteSupplier(supplierId) {
        const suppliers = this.getSuppliers();
        const supplier = suppliers.find(s => s.id === supplierId);

        if (!supplier) {
            this.showNotification('Supplier not found', 'error');
            return;
        }

        if (!confirm(`Are you sure you want to delete ${supplier.name}? This will remove all transaction history.`)) {
            return;
        }

        const updatedSuppliers = suppliers.filter(s => s.id !== supplierId);
        localStorage.setItem('supplierAccounts', JSON.stringify(updatedSuppliers));

        this.loadKhataPage(document.getElementById('khataPage'));
        this.showNotification(`${supplier.name} deleted successfully!`, 'success');
    }

    isOverdue(lastTransactionDate) {
        const lastDate = new Date(lastTransactionDate);
        const today = new Date();
        const daysDiff = (today - lastDate) / (1000 * 60 * 60 * 24);
        return daysDiff > 30; // Consider overdue after 30 days
    }

    // Staff Management Methods
    generateStaffTable() {
        const staff = this.getStaff();

        if (staff.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-users" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <h3>No Staff Members</h3>
                    <p>Start by adding your first staff member</p>
                    <button class="btn btn-primary" onclick="app.showAddStaffModal()">Add First Staff Member</button>
                </div>
            `;
        }

        return `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Position</th>
                        <th>Contact</th>
                        <th>Salary</th>
                        <th>Dehari Balance</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${staff.map(member => `
                        <tr>
                            <td>
                                <div class="staff-info">
                                    <strong>${member.name}</strong>
                                    <small>ID: ${member.employeeId}</small>
                                </div>
                            </td>
                            <td>
                                <span class="position-badge">${member.position}</span>
                            </td>
                            <td>
                                <div>
                                    <div>${member.phone || 'N/A'}</div>
                                    <small>${member.address || 'No address'}</small>
                                </div>
                            </td>
                            <td>
                                <span class="amount positive">PKR ${member.salary.toLocaleString()}</span>
                                <small>/month</small>
                            </td>
                            <td>
                                <span class="amount ${member.dehariBalance < 0 ? 'negative' : 'positive'}">
                                    PKR ${Math.abs(member.dehariBalance).toLocaleString()}
                                </span>
                                <small>${member.dehariBalance < 0 ? '(Owes)' : '(Credit)'}</small>
                            </td>
                            <td>
                                <span class="status-badge ${member.status === 'active' ? 'good' : 'medium'}">
                                    ${member.status}
                                </span>
                            </td>
                            <td>
                                <button class="btn-icon" onclick="app.showStaffDetails('${member.id}')" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" onclick="app.showAddDehariModal('${member.id}')" title="Add Dehari">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn-icon" onclick="app.editStaff('${member.id}')" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    getStaff() {
        const saved = localStorage.getItem('staffMembers');
        return saved ? JSON.parse(saved) : [];
    }

    updateStaffSummary() {
        const staff = this.getStaff();
        const totalStaff = staff.length;
        const totalSalaries = staff.reduce((sum, member) => sum + member.salary, 0);
        const totalPurchases = staff.reduce((sum, member) => sum + Math.max(0, -member.dehariBalance), 0);

        const staffEl = document.getElementById('totalStaff');
        const salariesEl = document.getElementById('totalSalaries');
        const purchasesEl = document.getElementById('totalStaffPurchases');

        if (staffEl) staffEl.textContent = totalStaff.toString();
        if (salariesEl) salariesEl.textContent = this.formatCurrency(totalSalaries);
        if (purchasesEl) purchasesEl.textContent = this.formatCurrency(totalPurchases);
    }

    showAddStaffModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add Staff Member</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addStaffForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="staffName">Full Name *</label>
                                <input type="text" id="staffName" name="staffName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="employeeId">Employee ID *</label>
                                <input type="text" id="employeeId" name="employeeId" class="form-control" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="position">Position *</label>
                                <select id="position" name="position" class="form-control" required>
                                    <option value="">Select Position</option>
                                    <option value="Manager">Manager</option>
                                    <option value="Chef">Chef</option>
                                    <option value="Cook">Cook</option>
                                    <option value="Waiter">Waiter</option>
                                    <option value="Cashier">Cashier</option>
                                    <option value="Cleaner">Cleaner</option>
                                    <option value="Delivery">Delivery Boy</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="salary">Monthly Salary (PKR) *</label>
                                <input type="number" id="salary" name="salary" class="form-control" min="0" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="staffPhone">Phone Number</label>
                                <input type="tel" id="staffPhone" name="staffPhone" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="joinDate">Join Date *</label>
                                <input type="date" id="joinDate" name="joinDate" class="form-control" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="staffAddress">Address</label>
                            <textarea id="staffAddress" name="staffAddress" class="form-control" rows="2"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="initialDehari">Initial Dehari Balance (PKR)</label>
                            <input type="number" id="initialDehari" name="initialDehari" class="form-control" step="0.01" value="0">
                            <small class="form-text">Positive = Credit to staff, Negative = Staff owes restaurant</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveStaff()">Add Staff Member</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.getElementById('joinDate').value = new Date().toISOString().split('T')[0];
    }

    saveStaff() {
        const form = document.getElementById('addStaffForm');
        const formData = new FormData(form);

        if (!formData.get('staffName') || !formData.get('employeeId') || !formData.get('position') || !formData.get('salary') || !formData.get('joinDate')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const staff = this.getStaff();

        // Check if employee ID already exists
        if (staff.find(member => member.employeeId === formData.get('employeeId'))) {
            this.showNotification('Employee ID already exists', 'error');
            return;
        }

        const newStaff = {
            id: Date.now().toString(),
            name: formData.get('staffName'),
            employeeId: formData.get('employeeId'),
            position: formData.get('position'),
            salary: parseFloat(formData.get('salary')),
            phone: formData.get('staffPhone') || '',
            address: formData.get('staffAddress') || '',
            joinDate: formData.get('joinDate'),
            dehariBalance: parseFloat(formData.get('initialDehari')) || 0,
            status: 'active',
            transactions: [],
            createdAt: new Date().toISOString()
        };

        // Add initial transaction if dehari balance is not zero
        if (newStaff.dehariBalance !== 0) {
            newStaff.transactions.push({
                id: Date.now().toString(),
                type: newStaff.dehariBalance > 0 ? 'credit' : 'debit',
                amount: Math.abs(newStaff.dehariBalance),
                description: 'Initial dehari balance',
                date: formData.get('joinDate'),
                createdAt: new Date().toISOString()
            });
        }

        staff.push(newStaff);
        localStorage.setItem('staffMembers', JSON.stringify(staff));

        document.querySelector('.modal-overlay').remove();
        this.loadStaffPage(document.getElementById('staffPage'));
        this.showNotification('Staff member added successfully!', 'success');
    }

    showAddDehariModal(staffId) {
        const staff = this.getStaff();
        const member = staff.find(s => s.id === staffId);
        if (!member) return;

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add Dehari Transaction - ${member.name}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="current-balance" style="background: var(--gray-50); padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                        <strong>Current Dehari Balance:
                            <span class="${member.dehariBalance < 0 ? 'text-danger' : 'text-success'}">
                                PKR ${Math.abs(member.dehariBalance).toLocaleString()}
                                ${member.dehariBalance < 0 ? '(Owes Restaurant)' : '(Credit)'}
                            </span>
                        </strong>
                    </div>

                    <form id="addDehariForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="transactionType">Transaction Type *</label>
                                <select id="transactionType" name="transactionType" class="form-control" required>
                                    <option value="">Select Type</option>
                                    <option value="purchase">Restaurant Purchase (Staff bought from restaurant)</option>
                                    <option value="payment">Payment (Staff paid money)</option>
                                    <option value="advance">Advance (Restaurant gave money to staff)</option>
                                    <option value="deduction">Deduction (Deduct from salary)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="amount">Amount (PKR) *</label>
                                <input type="number" id="amount" name="amount" class="form-control" min="0" step="0.01" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description *</label>
                            <input type="text" id="description" name="description" class="form-control" required placeholder="e.g., Food purchase, Salary advance, etc.">
                        </div>

                        <div class="form-group">
                            <label for="transactionDate">Date *</label>
                            <input type="date" id="transactionDate" name="transactionDate" class="form-control" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveDehariTransaction('${staffId}')">Add Transaction</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
    }

    saveDehariTransaction(staffId) {
        const form = document.getElementById('addDehariForm');
        const formData = new FormData(form);

        if (!formData.get('transactionType') || !formData.get('amount') || !formData.get('description') || !formData.get('transactionDate')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const staff = this.getStaff();
        const memberIndex = staff.findIndex(s => s.id === staffId);

        if (memberIndex === -1) {
            this.showNotification('Staff member not found', 'error');
            return;
        }

        const amount = parseFloat(formData.get('amount'));
        const type = formData.get('transactionType');

        // Calculate balance change based on transaction type
        let balanceChange = 0;
        switch (type) {
            case 'purchase':
                balanceChange = -amount; // Staff owes restaurant
                break;
            case 'payment':
                balanceChange = amount; // Staff paid, reduces debt or increases credit
                break;
            case 'advance':
                balanceChange = -amount; // Restaurant gave money, staff owes
                break;
            case 'deduction':
                balanceChange = amount; // Deduction from salary, reduces debt
                break;
        }

        const transaction = {
            id: Date.now().toString(),
            type: type,
            amount: amount,
            description: formData.get('description'),
            date: formData.get('transactionDate'),
            balanceChange: balanceChange,
            createdAt: new Date().toISOString()
        };

        staff[memberIndex].transactions.push(transaction);
        staff[memberIndex].dehariBalance += balanceChange;

        localStorage.setItem('staffMembers', JSON.stringify(staff));

        document.querySelector('.modal-overlay').remove();
        this.loadStaffPage(document.getElementById('staffPage'));
        this.showNotification('Dehari transaction added successfully!', 'success');
    }

    // Expenses Management Methods
    loadExpensesPage(pageElement) {
        const expenses = this.getExpenses();
        const today = new Date().toISOString().split('T')[0];
        const thisWeek = this.getThisWeekDateRange();
        const thisMonth = this.getThisMonthDateRange();

        // Calculate automatic expenses
        const automaticExpenses = this.calculateAutomaticExpenses();

        // Calculate totals
        const todayExpenses = this.calculateExpensesForPeriod(expenses, today, today);
        const weekExpenses = this.calculateExpensesForPeriod(expenses, thisWeek.start, thisWeek.end);
        const monthExpenses = this.calculateExpensesForPeriod(expenses, thisMonth.start, thisMonth.end);

        pageElement.innerHTML = `
            <div class="page-header">
                <h3><i class="fas fa-receipt"></i> Expenses Management</h3>
                <button class="btn btn-primary" onclick="app.showAddExpenseModal()">
                    <i class="fas fa-plus"></i> Add Manual Expense
                </button>
            </div>

            <!-- Expense Summary Cards -->
            <div class="expense-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <h3>PKR ${todayExpenses.toLocaleString()}</h3>
                        <p>Today's Expenses</p>
                        <small class="expense-breakdown">
                            Manual: PKR ${this.calculateManualExpensesForPeriod(expenses, today, today).toLocaleString()} |
                            Auto: PKR ${automaticExpenses.daily.toLocaleString()}
                        </small>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <h3>PKR ${weekExpenses.toLocaleString()}</h3>
                        <p>This Week's Expenses</p>
                        <small class="expense-breakdown">
                            Manual: PKR ${this.calculateManualExpensesForPeriod(expenses, thisWeek.start, thisWeek.end).toLocaleString()} |
                            Auto: PKR ${(automaticExpenses.weekly).toLocaleString()}
                        </small>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3>PKR ${monthExpenses.toLocaleString()}</h3>
                        <p>This Month's Expenses</p>
                        <small class="expense-breakdown">
                            Manual: PKR ${this.calculateManualExpensesForPeriod(expenses, thisMonth.start, thisMonth.end).toLocaleString()} |
                            Auto: PKR ${(automaticExpenses.monthly).toLocaleString()}
                        </small>
                    </div>
                </div>
            </div>

            <!-- Automatic Expenses Section -->
            <div class="analytics-section">
                <div class="section-header">
                    <i class="fas fa-cogs"></i>
                    <h4>Automatic Expenses (Calculated from System Data)</h4>
                </div>
                <div class="automatic-expenses-grid">
                    <div class="auto-expense-card">
                        <h5><i class="fas fa-users"></i> Staff Dehari</h5>
                        <div class="expense-amount">PKR ${automaticExpenses.staffDehari.toLocaleString()}</div>
                        <div class="expense-details">
                            <div class="expense-item">
                                <span>Total Staff Members:</span>
                                <span>${automaticExpenses.staffCount}</span>
                            </div>
                            <div class="expense-item">
                                <span>Pending Dehari:</span>
                                <span>PKR ${automaticExpenses.staffDehari.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                    <div class="auto-expense-card">
                        <h5><i class="fas fa-handshake"></i> Khata Payments</h5>
                        <div class="expense-amount">PKR ${automaticExpenses.khataPayments.toLocaleString()}</div>
                        <div class="expense-details">
                            <div class="expense-item">
                                <span>Total Suppliers:</span>
                                <span>${automaticExpenses.supplierCount}</span>
                            </div>
                            <div class="expense-item">
                                <span>Outstanding Amount:</span>
                                <span>PKR ${automaticExpenses.khataPayments.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                    <div class="auto-expense-card">
                        <h5><i class="fas fa-leaf"></i> Ingredient Costs</h5>
                        <div class="expense-amount">PKR ${automaticExpenses.ingredientCosts.toLocaleString()}</div>
                        <div class="expense-details">
                            <div class="expense-item">
                                <span>From Orders:</span>
                                <span>PKR ${automaticExpenses.ingredientCosts.toLocaleString()}</span>
                            </div>
                            <div class="expense-item">
                                <span>Inventory Value:</span>
                                <span>PKR ${automaticExpenses.inventoryValue.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Manual Expenses Section -->
            <div class="analytics-section">
                <div class="section-header">
                    <i class="fas fa-edit"></i>
                    <h4>Manual Expenses</h4>
                    <div class="section-actions">
                        <select id="expenseFilter" onchange="app.filterExpenses()" class="form-control">
                            <option value="all">All Categories</option>
                            <option value="vegetables">Vegetables</option>
                            <option value="utilities">Utilities</option>
                            <option value="equipment">Equipment</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="transport">Transport</option>
                            <option value="other">Other</option>
                        </select>
                        <input type="date" id="expenseDateFilter" onchange="app.filterExpenses()" class="form-control">
                    </div>
                </div>
                <div class="expenses-table-container">
                    <table class="expenses-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Item</th>
                                <th>Category</th>
                                <th>Amount</th>
                                <th>Added to Inventory</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="expensesTableBody">
                            ${this.generateExpensesTableRows(expenses)}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    getExpenses() {
        return JSON.parse(localStorage.getItem('restaurantExpenses') || '[]');
    }

    calculateAutomaticExpenses() {
        const staff = this.getStaff();
        const suppliers = this.getSuppliers();
        const orders = this.getOrders();
        const inventoryItems = this.getInventoryItems();

        // Staff Dehari calculation
        const staffDehari = staff.reduce((total, member) => {
            return total + (member.dehari_balance || 0);
        }, 0);

        // Khata payments calculation
        const khataPayments = suppliers.reduce((total, supplier) => {
            return total + (supplier.balance || 0);
        }, 0);

        // Ingredient costs from orders
        const ingredientCosts = this.calculateTotalIngredientCosts(orders);

        // Inventory value
        const inventoryValue = inventoryItems.reduce((total, item) => {
            return total + ((item.quantity || 0) * (item.unitPrice || 0));
        }, 0);

        return {
            staffDehari,
            khataPayments,
            ingredientCosts,
            inventoryValue,
            staffCount: staff.length,
            supplierCount: suppliers.length,
            daily: staffDehari + ingredientCosts,
            weekly: (staffDehari + ingredientCosts) * 7,
            monthly: (staffDehari + ingredientCosts) * 30
        };
    }

    calculateExpensesForPeriod(expenses, startDate, endDate) {
        const automaticExpenses = this.calculateAutomaticExpenses();
        const manualExpenses = this.calculateManualExpensesForPeriod(expenses, startDate, endDate);

        // For today, add daily automatic expenses
        if (startDate === endDate && startDate === new Date().toISOString().split('T')[0]) {
            return manualExpenses + automaticExpenses.daily;
        }

        // For periods, estimate based on days
        const start = new Date(startDate);
        const end = new Date(endDate);
        const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;

        return manualExpenses + (automaticExpenses.daily * days);
    }

    calculateManualExpensesForPeriod(expenses, startDate, endDate) {
        return expenses
            .filter(expense => {
                const expenseDate = expense.date;
                return expenseDate >= startDate && expenseDate <= endDate;
            })
            .reduce((total, expense) => total + (expense.amount || 0), 0);
    }

    getThisWeekDateRange() {
        const today = new Date();
        const firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
        const lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6));

        return {
            start: firstDay.toISOString().split('T')[0],
            end: lastDay.toISOString().split('T')[0]
        };
    }

    getThisMonthDateRange() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        return {
            start: firstDay.toISOString().split('T')[0],
            end: lastDay.toISOString().split('T')[0]
        };
    }

    generateExpensesTableRows(expenses) {
        if (expenses.length === 0) {
            return '<tr><td colspan="7" class="text-center">No manual expenses recorded</td></tr>';
        }

        return expenses
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .map(expense => `
                <tr>
                    <td>${new Date(expense.date).toLocaleDateString()}</td>
                    <td>${expense.item}</td>
                    <td><span class="category-badge ${expense.category}">${expense.category}</span></td>
                    <td class="amount">PKR ${expense.amount.toLocaleString()}</td>
                    <td>${expense.addToInventory ? '<span class="badge success">Yes</span>' : '<span class="badge secondary">No</span>'}</td>
                    <td>${expense.notes || '-'}</td>
                    <td>
                        <button class="btn-icon edit" onclick="app.editExpense('${expense.id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon delete" onclick="app.deleteExpense('${expense.id}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
    }

    showAddExpenseModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-plus"></i> Add Manual Expense</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addExpenseForm" onsubmit="app.addExpense(event)">
                        <div class="form-group">
                            <label for="expenseItem">Item Name *</label>
                            <input type="text" id="expenseItem" class="form-control" placeholder="e.g., Sabzi, Electricity Bill" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="expenseAmount">Amount (PKR) *</label>
                                <input type="number" id="expenseAmount" class="form-control" placeholder="1000" min="1" required>
                            </div>
                            <div class="form-group">
                                <label for="expenseCategory">Category *</label>
                                <select id="expenseCategory" class="form-control" required>
                                    <option value="">Select Category</option>
                                    <option value="vegetables">Vegetables</option>
                                    <option value="utilities">Utilities</option>
                                    <option value="equipment">Equipment</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="transport">Transport</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="addToInventory" onchange="app.toggleInventoryFields()">
                                <span class="checkmark"></span>
                                Add to Inventory
                            </label>
                        </div>

                        <div id="inventoryFields" class="inventory-fields" style="display: none;">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="inventoryQuantity">Quantity</label>
                                    <input type="number" id="inventoryQuantity" class="form-control" placeholder="10" min="1">
                                </div>
                                <div class="form-group">
                                    <label for="inventoryUnit">Unit</label>
                                    <select id="inventoryUnit" class="form-control">
                                        <option value="kg">Kg</option>
                                        <option value="liter">Liter</option>
                                        <option value="piece">Piece</option>
                                        <option value="packet">Packet</option>
                                        <option value="box">Box</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="expenseNotes">Notes (Optional)</label>
                            <textarea id="expenseNotes" class="form-control" rows="3" placeholder="Additional details about this expense"></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Add Expense
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    toggleInventoryFields() {
        const checkbox = document.getElementById('addToInventory');
        const fields = document.getElementById('inventoryFields');
        const quantityInput = document.getElementById('inventoryQuantity');
        const unitSelect = document.getElementById('inventoryUnit');

        if (checkbox.checked) {
            fields.style.display = 'block';
            quantityInput.required = true;
        } else {
            fields.style.display = 'none';
            quantityInput.required = false;
            quantityInput.value = '';
            unitSelect.value = 'kg';
        }
    }

    addExpense(event) {
        event.preventDefault();

        const item = document.getElementById('expenseItem').value.trim();
        const amount = parseFloat(document.getElementById('expenseAmount').value);
        const category = document.getElementById('expenseCategory').value;
        const addToInventory = document.getElementById('addToInventory').checked;
        const notes = document.getElementById('expenseNotes').value.trim();

        if (!item || !amount || !category) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const expense = {
            id: Date.now().toString(),
            item,
            amount,
            category,
            addToInventory,
            notes,
            date: new Date().toISOString().split('T')[0],
            timestamp: new Date().toISOString()
        };

        // Add to inventory if requested
        if (addToInventory) {
            const quantity = parseFloat(document.getElementById('inventoryQuantity').value);
            const unit = document.getElementById('inventoryUnit').value;

            if (!quantity) {
                this.showNotification('Please enter quantity for inventory', 'error');
                return;
            }

            const inventoryItem = {
                id: Date.now().toString() + '_inv',
                name: item,
                quantity,
                unit,
                unitPrice: amount / quantity,
                category: category,
                supplier: 'Manual Entry',
                expiryDate: '',
                minStockLevel: Math.ceil(quantity * 0.2), // 20% of quantity as minimum
                lastUpdated: new Date().toISOString()
            };

            // Check if item already exists in inventory
            const inventoryItems = this.getInventoryItems();
            const existingItemIndex = inventoryItems.findIndex(inv =>
                inv.name.toLowerCase() === item.toLowerCase()
            );

            if (existingItemIndex !== -1) {
                // Update existing item
                inventoryItems[existingItemIndex].quantity += quantity;
                inventoryItems[existingItemIndex].lastUpdated = new Date().toISOString();
            } else {
                // Add new item
                inventoryItems.push(inventoryItem);
            }

            localStorage.setItem('inventoryItems', JSON.stringify(inventoryItems));
        }

        // Save expense
        const expenses = this.getExpenses();
        expenses.push(expense);
        localStorage.setItem('restaurantExpenses', JSON.stringify(expenses));

        // Close modal and refresh page
        document.querySelector('.modal-overlay').remove();
        this.loadExpensesPage(document.getElementById('expensesPage'));

        const message = addToInventory
            ? `Expense added and ${item} added to inventory!`
            : 'Expense added successfully!';
        this.showNotification(message, 'success');

        // Update dashboard if visible
        this.updateDashboardStats();
    }

    filterExpenses() {
        const categoryFilter = document.getElementById('expenseFilter').value;
        const dateFilter = document.getElementById('expenseDateFilter').value;
        const expenses = this.getExpenses();

        let filteredExpenses = expenses;

        if (categoryFilter !== 'all') {
            filteredExpenses = filteredExpenses.filter(expense => expense.category === categoryFilter);
        }

        if (dateFilter) {
            filteredExpenses = filteredExpenses.filter(expense => expense.date === dateFilter);
        }

        document.getElementById('expensesTableBody').innerHTML = this.generateExpensesTableRows(filteredExpenses);
    }

    deleteExpense(expenseId) {
        if (!confirm('Are you sure you want to delete this expense?')) {
            return;
        }

        const expenses = this.getExpenses();
        const updatedExpenses = expenses.filter(expense => expense.id !== expenseId);

        localStorage.setItem('restaurantExpenses', JSON.stringify(updatedExpenses));
        this.loadExpensesPage(document.getElementById('expensesPage'));
        this.showNotification('Expense deleted successfully!', 'success');

        // Update dashboard if visible
        this.updateDashboardStats();
    }

    // Table Management Methods
    getTables() {
        const saved = localStorage.getItem('restaurantTables');
        if (saved) {
            return JSON.parse(saved);
        }

        // Initialize default tables if none exist
        const defaultTables = [];
        for (let i = 1; i <= 20; i++) {
            defaultTables.push({
                id: i.toString(),
                number: `T-${i.toString().padStart(2, '0')}`,
                capacity: i <= 10 ? 4 : 6, // First 10 tables have 4 seats, rest have 6
                status: 'available', // available, occupied, reserved, cleaning
                currentOrder: null,
                customers: 0,
                position: { x: (i - 1) % 5, y: Math.floor((i - 1) / 5) }
            });
        }

        localStorage.setItem('restaurantTables', JSON.stringify(defaultTables));
        return defaultTables;
    }

    generateTablesGrid() {
        const tables = this.getTables();

        return tables.map(table => `
            <div class="table-card ${table.status}" onclick="app.handleTableClick('${table.id}')">
                <div class="table-header">
                    <h4>${table.number}</h4>
                    <span class="table-capacity">${table.capacity} seats</span>
                </div>
                <div class="table-status">
                    <span class="status-indicator ${table.status}"></span>
                    <span class="status-text">${table.status.charAt(0).toUpperCase() + table.status.slice(1)}</span>
                </div>
                ${table.status === 'occupied' ? `
                    <div class="table-info">
                        <div class="customer-count">
                            <i class="fas fa-users"></i>
                            ${table.customers} customers
                        </div>
                        <div class="order-info">
                            Order: ${table.currentOrder?.order_number || 'N/A'}
                        </div>
                    </div>
                ` : ''}
                <div class="table-actions">
                    ${table.status === 'available' ?
                        '<button class="btn btn-sm btn-primary">Assign Customers</button>' :
                        table.status === 'occupied' ?
                        '<button class="btn btn-sm btn-success">View Order</button>' :
                        '<button class="btn btn-sm btn-outline">Manage</button>'
                    }
                </div>
            </div>
        `).join('');
    }

    handleTableClick(tableId) {
        const tables = this.getTables();
        const table = tables.find(t => t.id === tableId);

        if (!table) return;

        if (table.status === 'available') {
            this.showAssignCustomersModal(table);
        } else if (table.status === 'occupied') {
            this.showTableOrderModal(table);
        } else {
            this.showTableManagementModal(table);
        }
    }

    showAssignCustomersModal(table) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Assign Customers - ${table.number}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="table-info-display">
                        <div class="info-item">
                            <strong>Table:</strong> ${table.number}
                        </div>
                        <div class="info-item">
                            <strong>Capacity:</strong> ${table.capacity} seats
                        </div>
                        <div class="info-item">
                            <strong>Status:</strong> Available
                        </div>
                    </div>

                    <form id="assignCustomersForm">
                        <div class="form-group">
                            <label for="customerCount">Number of Customers *</label>
                            <input type="number" id="customerCount" name="customerCount" class="form-control"
                                   min="1" max="${table.capacity}" value="1" required>
                            <small class="form-text">Maximum ${table.capacity} customers for this table</small>
                        </div>

                        <div class="form-group">
                            <label for="customerName">Customer Name (Optional)</label>
                            <input type="text" id="customerName" name="customerName" class="form-control"
                                   placeholder="Enter customer name or leave blank">
                        </div>

                        <div class="form-group">
                            <label for="reservationType">Service Type *</label>
                            <select id="reservationType" name="reservationType" class="form-control" required>
                                <option value="immediate">Seat Immediately</option>
                                <option value="reservation">Make Reservation</option>
                            </select>
                        </div>

                        <div class="form-group" id="reservationTimeGroup" style="display: none;">
                            <label for="reservationTime">Reservation Time</label>
                            <input type="datetime-local" id="reservationTime" name="reservationTime" class="form-control">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.assignCustomersToTable('${table.id}')">Assign Table</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Show/hide reservation time based on selection
        const reservationType = document.getElementById('reservationType');
        const reservationTimeGroup = document.getElementById('reservationTimeGroup');

        reservationType.addEventListener('change', function() {
            if (this.value === 'reservation') {
                reservationTimeGroup.style.display = 'block';
                document.getElementById('reservationTime').required = true;
            } else {
                reservationTimeGroup.style.display = 'none';
                document.getElementById('reservationTime').required = false;
            }
        });
    }

    assignCustomersToTable(tableId) {
        const form = document.getElementById('assignCustomersForm');
        const formData = new FormData(form);

        if (!formData.get('customerCount') || !formData.get('reservationType')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === tableId);

        if (tableIndex === -1) {
            this.showNotification('Table not found', 'error');
            return;
        }

        const customerCount = parseInt(formData.get('customerCount'));
        const reservationType = formData.get('reservationType');

        // Create new order
        const orderNumber = 'ZQ' + Date.now().toString().slice(-6);
        const newOrder = {
            id: Date.now().toString(),
            order_number: orderNumber,
            table_id: tableId,
            table_number: tables[tableIndex].number,
            customer_name: formData.get('customerName') || 'Walk-in Customer',
            customer_count: customerCount,
            items: [],
            total_amount: 0,
            status: 'pending',
            service_type: 'dine_in',
            created_at: new Date().toISOString(),
            reservation_time: reservationType === 'reservation' ? formData.get('reservationTime') : null
        };

        // Update table status
        tables[tableIndex].status = reservationType === 'reservation' ? 'reserved' : 'occupied';
        tables[tableIndex].customers = customerCount;
        tables[tableIndex].currentOrder = newOrder;

        // Save order and table data
        const orders = this.getOrders();
        orders.push(newOrder);

        localStorage.setItem('restaurantOrders', JSON.stringify(orders));
        localStorage.setItem('restaurantTables', JSON.stringify(tables));

        // Close modal and refresh page
        document.querySelector('.modal-overlay').remove();
        this.loadTablesPage(document.getElementById('tablesPage'));

        const statusText = reservationType === 'reservation' ? 'reserved' : 'assigned';
        this.showNotification(`Table ${tables[tableIndex].number} ${statusText} successfully!`, 'success');

        // Update dashboard stats
        this.updateDashboardStats();
    }

    showTableOrderModal(table) {
        const order = table.currentOrder;
        if (!order) {
            this.showNotification('No active order found for this table', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay table-order-modal';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Table ${table.number} - Order ${order.order_number}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="table-order-info">
                        <div class="order-details">
                            <div class="detail-item">
                                <strong>Customer:</strong> ${order.customer_name}
                            </div>
                            <div class="detail-item">
                                <strong>Customers:</strong> ${order.customer_count}
                                <button class="btn btn-sm btn-outline" onclick="app.showEditCustomerCountModal('${table.id}')" style="margin-left: 0.5rem;">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                            <div class="detail-item">
                                <strong>Order Time:</strong> ${new Date(order.created_at).toLocaleString()}
                            </div>
                            <div class="detail-item">
                                <strong>Status:</strong>
                                <span class="status-badge ${order.status}">${order.status}</span>
                            </div>
                        </div>
                    </div>

                    <div class="order-items-section">
                        <div class="section-header">
                            <h4>Order Items</h4>
                            <button class="btn btn-primary" onclick="app.showAddItemToOrderModal('${order.id}')">
                                <i class="fas fa-plus"></i> Add Item
                            </button>
                        </div>

                        <div class="order-items-list" id="orderItemsList">
                            ${this.generateOrderItemsList(order.items || [])}
                        </div>
                    </div>

                    <div class="order-total-section">
                        <div class="total-breakdown">
                            <div class="total-row">
                                <span>Subtotal:</span>
                                <span id="orderSubtotal">PKR ${((order.total_amount || 0) / 1.16).toFixed(0)}</span>
                            </div>
                            <div class="total-row">
                                <span>Tax (16%):</span>
                                <span id="orderTax">PKR ${((order.total_amount || 0) * 0.16 / 1.16).toFixed(0)}</span>
                            </div>
                            <div class="total-row total-final">
                                <span><strong>Total:</strong></span>
                                <span id="orderTotal"><strong>PKR ${(order.total_amount || 0).toFixed(0)}</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="app.clearTable('${table.id}')">Clear Table</button>
                    <button class="btn btn-warning" onclick="app.updateOrderStatus('${order.id}', 'preparing')">Mark Preparing</button>
                    <button class="btn btn-success" onclick="app.finalizeTableBill('${table.id}')">Finalize Bill</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    generateOrderItemsList(items) {
        if (!items || items.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-utensils" style="font-size: 2rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <p>No items ordered yet</p>
                    <p style="font-size: 0.875rem; color: var(--gray-500);">Add items to this order</p>
                </div>
            `;
        }

        return items.map(item => `
            <div class="order-item-row">
                <div class="item-info">
                    <h5>${item.name}</h5>
                    <p class="item-description">${item.description || ''}</p>
                </div>
                <div class="item-quantity">
                    <button class="quantity-btn" onclick="app.updateOrderItemQuantity('${item.id}', -1)">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="app.updateOrderItemQuantity('${item.id}', 1)">+</button>
                </div>
                <div class="item-price">
                    <span class="unit-price">PKR ${item.price} each</span>
                    <span class="total-price">PKR ${(item.price * item.quantity).toFixed(0)}</span>
                </div>
                <div class="item-actions">
                    <button class="btn-icon" onclick="app.removeOrderItem('${item.id}')" title="Remove Item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    showAddItemToOrderModal(orderId) {
        const menuItems = this.getMenuItems();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add Item to Order</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="menu-items-selection">
                        ${menuItems.map(item => `
                            <div class="menu-item-option" onclick="app.addItemToOrder('${orderId}', '${item.id}')">
                                <div class="item-details">
                                    <h5>${item.name}</h5>
                                    <p>${item.description}</p>
                                    <span class="item-price">PKR ${item.basePrice}</span>
                                </div>
                                <div class="item-category">
                                    <span class="category-badge">${item.category}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    addItemToOrder(orderId, itemId) {
        const orders = this.getOrders();
        const orderIndex = orders.findIndex(o => o.id === orderId);

        if (orderIndex === -1) {
            this.showNotification('Order not found', 'error');
            return;
        }

        const menuItems = this.getMenuItems();
        const menuItem = menuItems.find(m => m.id === itemId);

        if (!menuItem) {
            this.showNotification('Menu item not found', 'error');
            return;
        }

        // Check if item already exists in order
        const existingItemIndex = orders[orderIndex].items.findIndex(i => i.menu_item_id === itemId);

        if (existingItemIndex !== -1) {
            // Increase quantity
            orders[orderIndex].items[existingItemIndex].quantity += 1;
        } else {
            // Add new item
            const newOrderItem = {
                id: Date.now().toString(),
                menu_item_id: itemId,
                name: menuItem.name,
                description: menuItem.description,
                price: menuItem.basePrice,
                quantity: 1
            };

            if (!orders[orderIndex].items) {
                orders[orderIndex].items = [];
            }
            orders[orderIndex].items.push(newOrderItem);
        }

        // Recalculate total
        const subtotal = orders[orderIndex].items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        orders[orderIndex].total_amount = subtotal * 1.16; // Add 16% tax

        // Update table's current order
        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === orders[orderIndex].table_id);
        if (tableIndex !== -1) {
            tables[tableIndex].currentOrder = orders[orderIndex];
            localStorage.setItem('restaurantTables', JSON.stringify(tables));
        }

        localStorage.setItem('restaurantOrders', JSON.stringify(orders));

        // Close add item modal
        document.querySelector('.modal-overlay:last-child').remove();

        // Refresh the table order modal
        const table = tables[tableIndex];
        document.querySelector('.table-order-modal').remove();
        this.showTableOrderModal(table);

        this.showNotification(`${menuItem.name} added to order`, 'success');
    }

    updateOrderItemQuantity(itemId, change) {
        const orders = this.getOrders();
        let orderFound = false;
        let orderIndex = -1;
        let itemIndex = -1;

        // Find the order and item
        for (let i = 0; i < orders.length; i++) {
            if (orders[i].items) {
                const foundItemIndex = orders[i].items.findIndex(item => item.id === itemId);
                if (foundItemIndex !== -1) {
                    orderIndex = i;
                    itemIndex = foundItemIndex;
                    orderFound = true;
                    break;
                }
            }
        }

        if (!orderFound) {
            this.showNotification('Order item not found', 'error');
            return;
        }

        // Update quantity
        const newQuantity = orders[orderIndex].items[itemIndex].quantity + change;

        if (newQuantity <= 0) {
            // Remove item if quantity becomes 0 or negative
            orders[orderIndex].items.splice(itemIndex, 1);
        } else {
            orders[orderIndex].items[itemIndex].quantity = newQuantity;
        }

        // Recalculate total
        const subtotal = orders[orderIndex].items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        orders[orderIndex].total_amount = subtotal * 1.16; // Add 16% tax

        // Update table's current order
        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === orders[orderIndex].table_id);
        if (tableIndex !== -1) {
            tables[tableIndex].currentOrder = orders[orderIndex];
            localStorage.setItem('restaurantTables', JSON.stringify(tables));
        }

        localStorage.setItem('restaurantOrders', JSON.stringify(orders));

        // Refresh the table order modal
        const table = tables[tableIndex];
        document.querySelector('.table-order-modal').remove();
        this.showTableOrderModal(table);

        this.showNotification('Item quantity updated', 'success');
    }

    removeOrderItem(itemId) {
        if (!confirm('Are you sure you want to remove this item from the order?')) {
            return;
        }

        const orders = this.getOrders();
        let orderFound = false;
        let orderIndex = -1;
        let itemIndex = -1;

        // Find the order and item
        for (let i = 0; i < orders.length; i++) {
            if (orders[i].items) {
                const foundItemIndex = orders[i].items.findIndex(item => item.id === itemId);
                if (foundItemIndex !== -1) {
                    orderIndex = i;
                    itemIndex = foundItemIndex;
                    orderFound = true;
                    break;
                }
            }
        }

        if (!orderFound) {
            this.showNotification('Order item not found', 'error');
            return;
        }

        // Remove item
        const removedItem = orders[orderIndex].items[itemIndex];
        orders[orderIndex].items.splice(itemIndex, 1);

        // Recalculate total
        const subtotal = orders[orderIndex].items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        orders[orderIndex].total_amount = subtotal * 1.16; // Add 16% tax

        // Update table's current order
        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === orders[orderIndex].table_id);
        if (tableIndex !== -1) {
            tables[tableIndex].currentOrder = orders[orderIndex];
            localStorage.setItem('restaurantTables', JSON.stringify(tables));
        }

        localStorage.setItem('restaurantOrders', JSON.stringify(orders));

        // Refresh the table order modal
        const table = tables[tableIndex];
        document.querySelector('.table-order-modal').remove();
        this.showTableOrderModal(table);

        this.showNotification(`${removedItem.name} removed from order`, 'success');
    }

    finalizeTableBill(tableId) {
        const tables = this.getTables();
        const table = tables.find(t => t.id === tableId);

        if (!table || !table.currentOrder) {
            this.showNotification('No active order found', 'error');
            return;
        }

        // Close table modal
        document.querySelector('.modal-overlay').remove();

        // Open POS system with the order pre-loaded
        this.showPOSSystemWithOrder(table.currentOrder);
    }

    showPOSSystemWithOrder(order) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay pos-modal';
        modal.innerHTML = this.generatePOSHTML();
        document.body.appendChild(modal);

        // Set current table order for per-head charge calculation
        this.setCurrentTableOrder(order);

        // Initialize POS with the order
        this.initializePOS();

        // Pre-load the order items into POS cart
        if (order.items && order.items.length > 0) {
            this.posCart = order.items.map(item => ({
                id: item.menu_item_id,
                name: item.name,
                basePrice: item.price,
                price: item.price,
                quantity: item.quantity,
                serviceType: 'dine_in',
                isColdDrink: false
            }));

            this.updatePOSCartDisplay();
        }
    }

    clearTable(tableId) {
        if (!confirm('Are you sure you want to clear this table? This will remove the current order.')) {
            return;
        }

        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === tableId);

        if (tableIndex === -1) {
            this.showNotification('Table not found', 'error');
            return;
        }

        // Clear table
        tables[tableIndex].status = 'available';
        tables[tableIndex].customers = 0;
        tables[tableIndex].currentOrder = null;

        localStorage.setItem('restaurantTables', JSON.stringify(tables));

        // Close modal and refresh
        document.querySelector('.modal-overlay').remove();
        this.loadTablesPage(document.getElementById('tablesPage'));

        this.showNotification(`Table ${tables[tableIndex].number} cleared successfully`, 'success');
        this.updateDashboardStats();
    }

    showEditCustomerCountModal(tableId) {
        const tables = this.getTables();
        const table = tables.find(t => t.id === tableId);

        if (!table || !table.currentOrder) {
            this.showNotification('Table or order not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Customer Count - ${table.number}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="current-info">
                        <p><strong>Current Customers:</strong> ${table.currentOrder.customer_count}</p>
                        <p><strong>Table Capacity:</strong> ${table.capacity} seats</p>
                    </div>

                    <form id="editCustomerCountForm">
                        <div class="form-group">
                            <label for="newCustomerCount">New Customer Count *</label>
                            <input type="number" id="newCustomerCount" name="newCustomerCount"
                                   class="form-control" min="1" max="${table.capacity}"
                                   value="${table.currentOrder.customer_count}" required>
                            <small class="form-text">Maximum ${table.capacity} customers for this table</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.updateCustomerCount('${tableId}')">Update Count</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    updateCustomerCount(tableId) {
        const form = document.getElementById('editCustomerCountForm');
        const formData = new FormData(form);
        const newCount = parseInt(formData.get('newCustomerCount'));

        if (!newCount || newCount < 1) {
            this.showNotification('Please enter a valid customer count', 'error');
            return;
        }

        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === tableId);

        if (tableIndex === -1) {
            this.showNotification('Table not found', 'error');
            return;
        }

        // Update table and order
        tables[tableIndex].customers = newCount;
        tables[tableIndex].currentOrder.customer_count = newCount;

        // Update the order in orders list
        const orders = this.getOrders();
        const orderIndex = orders.findIndex(o => o.id === tables[tableIndex].currentOrder.id);
        if (orderIndex !== -1) {
            orders[orderIndex].customer_count = newCount;
            localStorage.setItem('restaurantOrders', JSON.stringify(orders));
        }

        localStorage.setItem('restaurantTables', JSON.stringify(tables));

        // Close modal and refresh table order modal
        document.querySelector('.modal-overlay:last-child').remove();
        document.querySelector('.table-order-modal').remove();
        this.showTableOrderModal(tables[tableIndex]);

        this.showNotification('Customer count updated successfully', 'success');
    }



    showTableLayoutSettings() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Table Layout Settings</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="settings-tabs">
                        <button class="tab-btn active" onclick="app.switchSettingsTab('manage')">Manage Tables</button>
                        <button class="tab-btn" onclick="app.switchSettingsTab('add')">Add New Table</button>
                    </div>

                    <div id="manageTablesTab" class="settings-tab active">
                        <h4>Current Tables</h4>
                        <div class="tables-management-list">
                            ${this.generateTablesManagementList()}
                        </div>
                    </div>

                    <div id="addTableTab" class="settings-tab" style="display: none;">
                        <h4>Add New Table</h4>
                        <form id="addTableForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="tableNumber">Table Number *</label>
                                    <input type="text" id="tableNumber" name="tableNumber" class="form-control"
                                           placeholder="e.g., T-21" required>
                                </div>
                                <div class="form-group">
                                    <label for="tableCapacity">Capacity (Seats) *</label>
                                    <input type="number" id="tableCapacity" name="tableCapacity" class="form-control"
                                           min="1" max="12" value="4" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="tableDescription">Description (Optional)</label>
                                <input type="text" id="tableDescription" name="tableDescription" class="form-control"
                                       placeholder="e.g., Window table, VIP section">
                            </div>
                        </form>
                        <button class="btn btn-primary" onclick="app.addNewTable()">Add Table</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    generateTablesManagementList() {
        const tables = this.getTables();

        return tables.map(table => `
            <div class="table-management-item">
                <div class="table-info">
                    <h5>${table.number}</h5>
                    <p>Capacity: ${table.capacity} seats</p>
                    <span class="status-badge ${table.status}">${table.status}</span>
                </div>
                <div class="table-actions">
                    <button class="btn btn-sm btn-outline" onclick="app.editTable('${table.id}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-error" onclick="app.deleteTable('${table.id}')"
                            ${table.status === 'occupied' ? 'disabled title="Cannot delete occupied table"' : ''}>
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    switchSettingsTab(tabName) {
        // Remove active class from all tabs and buttons
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.settings-tab').forEach(tab => tab.style.display = 'none');

        // Activate selected tab
        event.target.classList.add('active');

        if (tabName === 'manage') {
            document.getElementById('manageTablesTab').style.display = 'block';
        } else if (tabName === 'add') {
            document.getElementById('addTableTab').style.display = 'block';
        }
    }

    addNewTable() {
        const form = document.getElementById('addTableForm');
        const formData = new FormData(form);

        if (!formData.get('tableNumber') || !formData.get('tableCapacity')) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        const tables = this.getTables();
        const tableNumber = formData.get('tableNumber');

        // Check if table number already exists
        if (tables.find(t => t.number.toLowerCase() === tableNumber.toLowerCase())) {
            this.showNotification('Table number already exists', 'error');
            return;
        }

        const newTable = {
            id: Date.now().toString(),
            number: tableNumber,
            capacity: parseInt(formData.get('tableCapacity')),
            description: formData.get('tableDescription') || '',
            status: 'available',
            currentOrder: null,
            customers: 0,
            position: { x: 0, y: 0 } // Default position
        };

        tables.push(newTable);
        localStorage.setItem('restaurantTables', JSON.stringify(tables));

        // Refresh the management list
        document.querySelector('.tables-management-list').innerHTML = this.generateTablesManagementList();

        // Clear form
        form.reset();

        this.showNotification(`Table ${tableNumber} added successfully!`, 'success');

        // Refresh tables page if open
        const tablesPage = document.getElementById('tablesPage');
        if (tablesPage && tablesPage.innerHTML.trim() !== '') {
            this.loadTablesPage(tablesPage);
        }
    }

    editTable(tableId) {
        const tables = this.getTables();
        const table = tables.find(t => t.id === tableId);

        if (!table) {
            this.showNotification('Table not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Table - ${table.number}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editTableForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editTableNumber">Table Number *</label>
                                <input type="text" id="editTableNumber" name="editTableNumber"
                                       class="form-control" value="${table.number}" required>
                            </div>
                            <div class="form-group">
                                <label for="editTableCapacity">Capacity (Seats) *</label>
                                <input type="number" id="editTableCapacity" name="editTableCapacity"
                                       class="form-control" min="1" max="12" value="${table.capacity}" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editTableDescription">Description</label>
                            <input type="text" id="editTableDescription" name="editTableDescription"
                                   class="form-control" value="${table.description || ''}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.saveTableEdit('${tableId}')">Save Changes</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    saveTableEdit(tableId) {
        const form = document.getElementById('editTableForm');
        const formData = new FormData(form);

        const tables = this.getTables();
        const tableIndex = tables.findIndex(t => t.id === tableId);

        if (tableIndex === -1) {
            this.showNotification('Table not found', 'error');
            return;
        }

        const newNumber = formData.get('editTableNumber');

        // Check if new number conflicts with existing tables (excluding current table)
        if (tables.find(t => t.id !== tableId && t.number.toLowerCase() === newNumber.toLowerCase())) {
            this.showNotification('Table number already exists', 'error');
            return;
        }

        tables[tableIndex].number = newNumber;
        tables[tableIndex].capacity = parseInt(formData.get('editTableCapacity'));
        tables[tableIndex].description = formData.get('editTableDescription') || '';

        localStorage.setItem('restaurantTables', JSON.stringify(tables));

        // Close modal
        document.querySelector('.modal-overlay:last-child').remove();

        // Refresh management list
        document.querySelector('.tables-management-list').innerHTML = this.generateTablesManagementList();

        this.showNotification('Table updated successfully!', 'success');

        // Refresh tables page if open
        const tablesPage = document.getElementById('tablesPage');
        if (tablesPage && tablesPage.innerHTML.trim() !== '') {
            this.loadTablesPage(tablesPage);
        }
    }

    deleteTable(tableId) {
        const tables = this.getTables();
        const table = tables.find(t => t.id === tableId);

        if (!table) {
            this.showNotification('Table not found', 'error');
            return;
        }

        if (table.status === 'occupied') {
            this.showNotification('Cannot delete occupied table', 'error');
            return;
        }

        if (!confirm(`Are you sure you want to delete table ${table.number}?`)) {
            return;
        }

        const updatedTables = tables.filter(t => t.id !== tableId);
        localStorage.setItem('restaurantTables', JSON.stringify(updatedTables));

        // Refresh management list
        document.querySelector('.tables-management-list').innerHTML = this.generateTablesManagementList();

        this.showNotification(`Table ${table.number} deleted successfully!`, 'success');

        // Refresh tables page if open
        const tablesPage = document.getElementById('tablesPage');
        if (tablesPage && tablesPage.innerHTML.trim() !== '') {
            this.loadTablesPage(tablesPage);
        }
    }

    showAddTableModal() {
        this.showTableLayoutSettings();
        // Switch to add table tab
        setTimeout(() => {
            this.switchSettingsTab('add');
            document.querySelector('.tab-btn:last-child').classList.add('active');
            document.querySelector('.tab-btn:first-child').classList.remove('active');
        }, 100);
    }

    // Reports Methods
    loadReportData() {
        const orders = this.getOrders();
        const staff = this.getStaff();
        const udhars = this.getUdhars();
        const suppliers = this.getSuppliers();
        const tables = this.getTables();
        const inventory = this.getInventoryItems();

        // Calculate today's data
        const today = new Date().toDateString();
        const todayOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.toDateString() === today;
        });

        // Calculate week's data
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const weekOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= weekAgo;
        });

        // Update summary stats
        const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const weekRevenue = weekOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const totalRevenue = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const avgOrder = orders.length > 0 ? Math.round(totalRevenue / orders.length) : 0;

        // Update elements
        this.updateElementText('reportTotalRevenue', this.formatCurrency(totalRevenue));
        this.updateElementText('reportTotalOrders', orders.length.toString());
        this.updateElementText('reportAvgOrder', this.formatCurrency(avgOrder));
        this.updateElementText('todaySales', this.formatCurrency(todayRevenue));
        this.updateElementText('weekSales', this.formatCurrency(weekRevenue));

        // Staff data
        const totalSalaries = staff.reduce((sum, member) => sum + member.salary, 0);
        this.updateElementText('totalStaffCount', staff.length.toString());
        this.updateElementText('totalSalariesAmount', this.formatCurrency(totalSalaries));

        // Udhars data
        const totalUdharsAmount = udhars.reduce((sum, udhar) => sum + udhar.balance, 0);
        this.updateElementText('totalUdharsAmount', this.formatCurrency(totalUdharsAmount));
        this.updateElementText('totalUdharsCustomers', udhars.length.toString());

        // Khata data
        const totalPayable = suppliers.reduce((sum, supplier) => sum + Math.max(0, supplier.balance), 0);
        this.updateElementText('totalKhataPayable', this.formatCurrency(totalPayable));
        this.updateElementText('totalKhataSuppliers', suppliers.length.toString());

        // Table data
        const occupiedTables = tables.filter(t => t.status === 'occupied').length;
        this.updateElementText('occupiedTablesCount', occupiedTables.toString());
        this.updateElementText('totalTablesCount', tables.length.toString());

        // Inventory data
        const lowStockItems = inventory.filter(item => item.currentStock <= item.minimumStock).length;
        this.updateElementText('lowStockCount', lowStockItems.toString());
        this.updateElementText('totalInventoryItems', inventory.length.toString());
    }

    updateElementText(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    generateCustomReport() {
        const startDate = document.getElementById('reportStartDate').value;
        const endDate = document.getElementById('reportEndDate').value;

        if (!startDate || !endDate) {
            this.showNotification('Please select both start and end dates', 'error');
            return;
        }

        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999); // Include the entire end date

        const orders = this.getOrders().filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= start && orderDate <= end;
        });

        const totalRevenue = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const avgOrder = orders.length > 0 ? Math.round(totalRevenue / orders.length) : 0;

        this.updateElementText('reportTotalRevenue', this.formatCurrency(totalRevenue));
        this.updateElementText('reportTotalOrders', orders.length.toString());
        this.updateElementText('reportAvgOrder', this.formatCurrency(avgOrder));

        this.showNotification(`Report generated for ${startDate} to ${endDate}`, 'success');
    }

    showSalesReport() {
        const orders = this.getOrders();
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Sales Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Sales Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Orders:</span>
                                <span class="value">${orders.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Revenue:</span>
                                <span class="value">${this.formatCurrency(orders.reduce((sum, order) => sum + (order.total_amount || 0), 0))}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Average Order Value:</span>
                                <span class="value">${this.formatCurrency(orders.length > 0 ? orders.reduce((sum, order) => sum + (order.total_amount || 0), 0) / orders.length : 0)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Recent Orders</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Table</th>
                                    <th>Items</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${orders.slice(-20).reverse().map(order => `
                                    <tr>
                                        <td>${order.order_number}</td>
                                        <td>${new Date(order.created_at).toLocaleDateString()}</td>
                                        <td>${order.table_number || 'Takeaway'}</td>
                                        <td>${order.items?.length || 0}</td>
                                        <td>${this.formatCurrency(order.total_amount)}</td>
                                        <td><span class="status-badge ${order.status}">${order.status}</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('sales')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showInventoryReport() {
        const inventory = this.getInventoryItems();
        const lowStockItems = inventory.filter(item => item.currentStock <= item.minimumStock);

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Inventory Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Inventory Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Items:</span>
                                <span class="value">${inventory.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Low Stock Items:</span>
                                <span class="value">${lowStockItems.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Stock Value:</span>
                                <span class="value">${this.formatCurrency(inventory.reduce((sum, item) => sum + (item.currentStock * item.unitPrice), 0))}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Inventory Status</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Item Name</th>
                                    <th>Current Stock</th>
                                    <th>Minimum Stock</th>
                                    <th>Unit Price</th>
                                    <th>Total Value</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${inventory.map(item => `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${item.currentStock} ${item.unit}</td>
                                        <td>${item.minimumStock} ${item.unit}</td>
                                        <td>${this.formatCurrency(item.unitPrice)}</td>
                                        <td>${this.formatCurrency(item.currentStock * item.unitPrice)}</td>
                                        <td>
                                            <span class="status-badge ${item.currentStock <= item.minimumStock ? 'critical' : item.currentStock <= item.minimumStock * 2 ? 'warning' : 'good'}">
                                                ${item.currentStock <= item.minimumStock ? 'Low Stock' : item.currentStock <= item.minimumStock * 2 ? 'Warning' : 'Good'}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('inventory')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showStaffReport() {
        const staff = this.getStaff();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Staff Performance Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Staff Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Staff:</span>
                                <span class="value">${staff.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Monthly Salaries:</span>
                                <span class="value">${this.formatCurrency(staff.reduce((sum, member) => sum + member.salary, 0))}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Dehari Balance:</span>
                                <span class="value">${this.formatCurrency(staff.reduce((sum, member) => sum + member.dehariBalance, 0))}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Staff Details</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Employee ID</th>
                                    <th>Position</th>
                                    <th>Salary</th>
                                    <th>Dehari Balance</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${staff.map(member => `
                                    <tr>
                                        <td>${member.name}</td>
                                        <td>${member.employeeId}</td>
                                        <td>${member.position}</td>
                                        <td>${this.formatCurrency(member.salary)}</td>
                                        <td>
                                            <span class="${member.dehariBalance < 0 ? 'text-danger' : 'text-success'}">
                                                ${this.formatCurrency(Math.abs(member.dehariBalance))}
                                                ${member.dehariBalance < 0 ? '(Owes)' : '(Credit)'}
                                            </span>
                                        </td>
                                        <td><span class="status-badge ${member.status === 'active' ? 'good' : 'medium'}">${member.status}</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('staff')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showUdharsReport() {
        const udhars = this.getUdhars();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Customer Udhars Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Udhars Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Customers:</span>
                                <span class="value">${udhars.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Outstanding:</span>
                                <span class="value">${this.formatCurrency(udhars.reduce((sum, udhar) => sum + udhar.balance, 0))}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Overdue Accounts:</span>
                                <span class="value">${udhars.filter(udhar => this.isOverdue(udhar.lastTransactionDate)).length}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Customer Accounts</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Customer Name</th>
                                    <th>Phone</th>
                                    <th>Balance</th>
                                    <th>Last Transaction</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${udhars.map(udhar => `
                                    <tr>
                                        <td>${udhar.customerName}</td>
                                        <td>${udhar.phone || 'N/A'}</td>
                                        <td>${this.formatCurrency(udhar.balance)}</td>
                                        <td>${new Date(udhar.lastTransactionDate).toLocaleDateString()}</td>
                                        <td>
                                            <span class="status-badge ${this.isOverdue(udhar.lastTransactionDate) ? 'critical' : 'good'}">
                                                ${this.isOverdue(udhar.lastTransactionDate) ? 'Overdue' : 'Current'}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('udhars')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showKhataReport() {
        const suppliers = this.getSuppliers();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Supplier Khata Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Khata Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Suppliers:</span>
                                <span class="value">${suppliers.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Payable:</span>
                                <span class="value">${this.formatCurrency(suppliers.reduce((sum, supplier) => sum + Math.max(0, supplier.balance), 0))}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Receivable:</span>
                                <span class="value">${this.formatCurrency(suppliers.reduce((sum, supplier) => sum + Math.max(0, -supplier.balance), 0))}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Supplier Accounts</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Supplier Name</th>
                                    <th>Category</th>
                                    <th>Phone</th>
                                    <th>Balance</th>
                                    <th>Last Transaction</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${suppliers.map(supplier => `
                                    <tr>
                                        <td>${supplier.name}</td>
                                        <td>${supplier.category}</td>
                                        <td>${supplier.phone || 'N/A'}</td>
                                        <td>
                                            <span class="${supplier.balance > 0 ? 'text-danger' : supplier.balance < 0 ? 'text-success' : ''}">
                                                ${this.formatCurrency(Math.abs(supplier.balance))}
                                                ${supplier.balance > 0 ? '(Payable)' : supplier.balance < 0 ? '(Receivable)' : ''}
                                            </span>
                                        </td>
                                        <td>${supplier.lastTransactionDate ? new Date(supplier.lastTransactionDate).toLocaleDateString() : 'N/A'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('khata')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showTableReport() {
        const tables = this.getTables();
        const orders = this.getOrders();

        // Calculate table utilization
        const tableStats = tables.map(table => {
            const tableOrders = orders.filter(order => order.table_id === table.id);
            return {
                ...table,
                totalOrders: tableOrders.length,
                totalRevenue: tableOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0)
            };
        });

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Table Utilization Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Table Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Tables:</span>
                                <span class="value">${tables.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Occupied:</span>
                                <span class="value">${tables.filter(t => t.status === 'occupied').length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Occupancy Rate:</span>
                                <span class="value">${Math.round((tables.filter(t => t.status === 'occupied').length / tables.length) * 100)}%</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Table Performance</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Table</th>
                                    <th>Capacity</th>
                                    <th>Status</th>
                                    <th>Total Orders</th>
                                    <th>Total Revenue</th>
                                    <th>Avg per Order</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableStats.map(table => `
                                    <tr>
                                        <td>${table.number}</td>
                                        <td>${table.capacity} seats</td>
                                        <td><span class="status-badge ${table.status}">${table.status}</span></td>
                                        <td>${table.totalOrders}</td>
                                        <td>${this.formatCurrency(table.totalRevenue)}</td>
                                        <td>${table.totalOrders > 0 ? this.formatCurrency(table.totalRevenue / table.totalOrders) : 'PKR 0'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('tables')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    exportReport(reportType) {
        this.showNotification(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report exported successfully!`, 'success');
        // In a real implementation, this would generate and download a PDF or Excel file
    }

    // Missing Report Methods
    generateCustomReport() {
        const startDate = document.getElementById('reportStartDate')?.value;
        const endDate = document.getElementById('reportEndDate')?.value;

        if (!startDate || !endDate) {
            this.showNotification('Please select both start and end dates', 'error');
            return;
        }

        this.showNotification('Custom report generated successfully!', 'success');
        this.loadReportData();
    }

    showSalesReport() {
        const orders = this.getOrders();
        const today = new Date().toDateString();
        const todayOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.toDateString() === today;
        });

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Sales Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Sales Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Today's Orders:</span>
                                <span class="value">${todayOrders.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Today's Revenue:</span>
                                <span class="value">${this.formatCurrency(todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0))}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Orders:</span>
                                <span class="value">${orders.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Revenue:</span>
                                <span class="value">${this.formatCurrency(orders.reduce((sum, order) => sum + (order.total_amount || 0), 0))}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('sales')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showInventoryReport() {
        const inventory = this.getInventoryItems();
        const lowStockItems = inventory.filter(item => item.currentStock <= item.minimumStock);

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Inventory Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Inventory Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Items:</span>
                                <span class="value">${inventory.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Low Stock Items:</span>
                                <span class="value">${lowStockItems.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Value:</span>
                                <span class="value">${this.formatCurrency(inventory.reduce((sum, item) => sum + (item.currentStock * item.unitPrice), 0))}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Low Stock Items</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Item Name</th>
                                    <th>Current Stock</th>
                                    <th>Minimum Stock</th>
                                    <th>Unit</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${lowStockItems.map(item => `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${item.currentStock}</td>
                                        <td>${item.minimumStock}</td>
                                        <td>${item.unit}</td>
                                        <td><span class="status-badge critical">Low Stock</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('inventory')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    showStaffReport() {
        const staff = this.getStaff();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>Staff Performance Report</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="report-summary">
                        <h4>Staff Summary</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">Total Staff:</span>
                                <span class="value">${staff.length}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Salaries:</span>
                                <span class="value">${this.formatCurrency(staff.reduce((sum, member) => sum + (member.salary || 0), 0))}</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Total Dehari Balance:</span>
                                <span class="value">${this.formatCurrency(staff.reduce((sum, member) => sum + (member.dehariBalance || 0), 0))}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-table">
                        <h4>Staff Details</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Position</th>
                                    <th>Salary</th>
                                    <th>Dehari Balance</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${staff.map(member => `
                                    <tr>
                                        <td>${member.name}</td>
                                        <td>${member.position}</td>
                                        <td>${this.formatCurrency(member.salary || 0)}</td>
                                        <td>${this.formatCurrency(member.dehariBalance || 0)}</td>
                                        <td><span class="status-badge good">Active</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    <button class="btn btn-primary" onclick="app.exportReport('staff')">Export Report</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // Additional missing methods for complete button functionality

    // Task Management Methods (if not already implemented)
    editTask(taskId) {
        const tasks = this.getTasks();
        const task = tasks.find(t => t.id === taskId);
        if (!task) return;

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Task</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editTaskForm">
                        <div class="form-group">
                            <label for="editTaskTitle">Task Title *</label>
                            <input type="text" id="editTaskTitle" name="title" class="form-control" value="${task.title}" required>
                        </div>
                        <div class="form-group">
                            <label for="editTaskDescription">Description</label>
                            <textarea id="editTaskDescription" name="description" class="form-control" rows="3">${task.description || ''}</textarea>
                        </div>
                        <div class="form-group">
                            <label for="editTaskPriority">Priority</label>
                            <select id="editTaskPriority" name="priority" class="form-control">
                                <option value="low" ${task.priority === 'low' ? 'selected' : ''}>Low</option>
                                <option value="medium" ${task.priority === 'medium' ? 'selected' : ''}>Medium</option>
                                <option value="high" ${task.priority === 'high' ? 'selected' : ''}>High</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editTaskDueDate">Due Date</label>
                            <input type="date" id="editTaskDueDate" name="dueDate" class="form-control" value="${task.dueDate || ''}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.updateTask('${taskId}')">Update Task</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    deleteTask(taskId) {
        if (confirm('Are you sure you want to delete this task?')) {
            let tasks = this.getTasks();
            tasks = tasks.filter(task => task.id !== taskId);
            localStorage.setItem('tasks', JSON.stringify(tasks));
            this.refreshTasks();
            this.showNotification('Task deleted successfully!', 'success');
        }
    }

    // Purchase Management Methods (if not already implemented)
    updatePurchaseStatus(itemId) {
        let purchases = this.getPurchases();
        const itemIndex = purchases.findIndex(item => item.id === itemId);

        if (itemIndex !== -1) {
            const currentStatus = purchases[itemIndex].status;
            const statusOptions = ['pending', 'ordered', 'received', 'cancelled'];
            const currentIndex = statusOptions.indexOf(currentStatus);
            const nextIndex = (currentIndex + 1) % statusOptions.length;

            purchases[itemIndex].status = statusOptions[nextIndex];
            purchases[itemIndex].lastUpdated = new Date().toISOString();

            localStorage.setItem('purchases', JSON.stringify(purchases));
            this.refreshPurchases();
            this.showNotification(`Purchase status updated to ${statusOptions[nextIndex]}`, 'success');
        }
    }

    editPurchaseItem(itemId) {
        const purchases = this.getPurchases();
        const item = purchases.find(p => p.id === itemId);
        if (!item) return;

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Purchase Item</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editPurchaseForm">
                        <div class="form-group">
                            <label for="editPurchaseItem">Item Name *</label>
                            <input type="text" id="editPurchaseItem" name="itemName" class="form-control" value="${item.itemName}" required>
                        </div>
                        <div class="form-group">
                            <label for="editPurchaseQuantity">Quantity *</label>
                            <input type="number" id="editPurchaseQuantity" name="quantity" class="form-control" value="${item.quantity}" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="editPurchaseUnit">Unit</label>
                            <input type="text" id="editPurchaseUnit" name="unit" class="form-control" value="${item.unit}">
                        </div>
                        <div class="form-group">
                            <label for="editPurchasePrice">Unit Price (PKR)</label>
                            <input type="number" id="editPurchasePrice" name="unitPrice" class="form-control" value="${item.unitPrice}" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="editPurchaseSupplier">Supplier</label>
                            <input type="text" id="editPurchaseSupplier" name="supplier" class="form-control" value="${item.supplier}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.updatePurchaseItem('${itemId}')">Update Item</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    deletePurchaseItem(itemId) {
        if (confirm('Are you sure you want to delete this purchase item?')) {
            let purchases = this.getPurchases();
            purchases = purchases.filter(item => item.id !== itemId);
            localStorage.setItem('purchases', JSON.stringify(purchases));
            this.refreshPurchases();
            this.showNotification('Purchase item deleted successfully!', 'success');
        }
    }

    // Helper methods for data retrieval
    getTasks() {
        return JSON.parse(localStorage.getItem('tasks')) || [];
    }

    getPurchases() {
        return JSON.parse(localStorage.getItem('purchases')) || [];
    }

    refreshTasks() {
        // Refresh tasks display if on dashboard
        const tasksContainer = document.querySelector('.tasks-container');
        if (tasksContainer) {
            this.loadDashboard(document.getElementById('dashboardPage'));
        }
    }

    refreshPurchases() {
        // Refresh purchases display if on dashboard
        const purchasesContainer = document.querySelector('.purchases-container');
        if (purchasesContainer) {
            this.loadDashboard(document.getElementById('dashboardPage'));
        }
    }

    // Theme Management Methods
    initializeTheme() {
        // Load saved theme preference or default to light
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);

        // Update theme toggle button icon
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // Show notification
        const themeText = theme === 'dark' ? 'Dark' : 'Light';
        this.showNotification(`${themeText} theme activated`, 'success');
    }


}

// Simple, immediate initialization
let app;

// Initialize immediately
try {
    console.log('Initializing Zaiqa Al-Hayat Restaurant Management System...');
    app = new RestaurantApp();
    window.app = app;
    console.log('✅ App initialized successfully');
} catch (error) {
    console.error('❌ App initialization failed:', error);
}

// Also initialize when DOM is ready (backup)
document.addEventListener('DOMContentLoaded', function() {
    if (!window.app) {
        try {
            app = new RestaurantApp();
            window.app = app;
            console.log('✅ App initialized on DOM ready');
        } catch (error) {
            console.error('❌ DOM ready initialization failed:', error);
        }
    }

    // Force app to be available
    if (window.app) {
        console.log('✅ App is ready and available');

        // Test a simple method
        try {
            if (typeof window.app.showNotification === 'function') {
                console.log('✅ App methods are working');
            }
        } catch (e) {
            console.error('❌ App methods test failed:', e);
        }
    }
});

// Debug function to test if app is working
window.testApp = function() {
    console.log('App object:', app);
    console.log('App methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(app)));
    app.showNotification('App is working correctly!', 'success');
};

// App is now globally available from DOMContentLoaded event

// Add global error handler to catch any JavaScript errors
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    console.error('Error message:', e.message);
    console.error('Error location:', e.filename + ':' + e.lineno + ':' + e.colno);

    // Show user-friendly error notification
    if (window.app && window.app.showNotification) {
        window.app.showNotification('A JavaScript error occurred. Please check the console for details.', 'error');
    }
});

// Add unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);

    if (window.app && window.app.showNotification) {
        window.app.showNotification('An error occurred while processing your request.', 'error');
    }
});

// Simple global functions that work without app object
window.toggleThemeSimple = function() {
    console.log('Theme toggle clicked');
    try {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Update button icon
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        alert(`${newTheme === 'dark' ? 'Dark' : 'Light'} theme activated`);
        console.log(`Theme changed to: ${newTheme}`);
    } catch (error) {
        console.error('Theme toggle error:', error);
        alert('Error changing theme: ' + error.message);
    }
};

// Simple test function
window.testButton = function() {
    alert('Button test successful!');
    console.log('Test button clicked');
};

// Fallback function for button clicks when app is not ready
window.safeAppCall = function(methodName, ...args) {
    console.log(`Attempting to call: ${methodName}`);

    if (window.app && typeof window.app[methodName] === 'function') {
        try {
            console.log(`Calling app.${methodName}`);
            return window.app[methodName](...args);
        } catch (error) {
            console.error(`Error calling app.${methodName}:`, error);
            alert(`Error: ${error.message}`);
        }
    } else {
        console.error(`App not ready or method ${methodName} not found`);
        console.log('Available app methods:', window.app ? Object.getOwnPropertyNames(Object.getPrototypeOf(window.app)) : 'App not available');
        alert('System is still loading. Please wait a moment and try again.');
    }
};
