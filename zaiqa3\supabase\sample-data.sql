-- Sample data for Zaiqa Restaurant Management System
-- Run this after setting up the main schema

-- Insert demo users (Note: You'll need to create these users in Supabase Auth first)
-- Then update the IDs below to match the actual user IDs from auth.users

-- Sample menu categories
INSERT INTO menu_categories (name, description, sort_order, is_active) VALUES
('Appetizers', 'Start your meal with these delicious appetizers', 1, true),
('Main Course', 'Traditional desi main course dishes', 2, true),
('Rice & Bread', 'Basmati rice and fresh bread varieties', 3, true),
('Beverages', 'Cold and hot beverages', 4, true),
('Desserts', 'Sweet endings to your meal', 5, true);

-- Sample menu items
INSERT INTO menu_items (category_id, name, description, base_price, dine_in_price, takeaway_price, is_cold_drink, preparation_time, is_available) VALUES
-- Appetizers
((SELECT id FROM menu_categories WHERE name = 'Appetizers'), 'Chicken Tikka', 'Grilled chicken pieces marinated in yogurt and spices', 450, 450, 450, false, 20, true),
((SELECT id FROM menu_categories WHERE name = 'Appetizers'), 'Seekh Kebab', 'Minced meat kebabs grilled to perfection', 380, 380, 380, false, 18, true),
((SELECT id FROM menu_categories WHERE name = 'Appetizers'), 'Chicken Wings', 'Spicy chicken wings with special sauce', 320, 320, 320, false, 15, true),

-- Main Course
((SELECT id FROM menu_categories WHERE name = 'Main Course'), 'Chicken Karhai (1kg)', 'Traditional chicken karhai for 4-6 people', 1200, 1200, 1200, false, 30, true),
((SELECT id FROM menu_categories WHERE name = 'Main Course'), 'Mutton Karhai (1kg)', 'Tender mutton pieces in traditional karhai', 1800, 1800, 1800, false, 45, true),
((SELECT id FROM menu_categories WHERE name = 'Main Course'), 'Chicken Handi', 'Creamy chicken curry cooked in clay pot', 950, 950, 950, false, 25, true),
((SELECT id FROM menu_categories WHERE name = 'Main Course'), 'Beef Nihari', 'Slow-cooked beef stew with aromatic spices', 650, 650, 650, false, 40, true),
((SELECT id FROM menu_categories WHERE name = 'Main Course'), 'Fish Curry', 'Fresh fish in spicy curry sauce', 850, 850, 850, false, 20, true),

-- Rice & Bread
((SELECT id FROM menu_categories WHERE name = 'Rice & Bread'), 'Chicken Biryani', 'Aromatic basmati rice with tender chicken', 420, 420, 420, false, 35, true),
((SELECT id FROM menu_categories WHERE name = 'Rice & Bread'), 'Mutton Biryani', 'Premium basmati rice with mutton pieces', 580, 580, 580, false, 40, true),
((SELECT id FROM menu_categories WHERE name = 'Rice & Bread'), 'Plain Rice', 'Steamed basmati rice', 180, 180, 180, false, 15, true),
((SELECT id FROM menu_categories WHERE name = 'Rice & Bread'), 'Naan', 'Fresh baked bread', 60, 60, 60, false, 8, true),
((SELECT id FROM menu_categories WHERE name = 'Rice & Bread'), 'Roti', 'Whole wheat flatbread', 25, 25, 25, false, 5, true),

-- Beverages
((SELECT id FROM menu_categories WHERE name = 'Beverages'), 'Coca Cola', 'Chilled soft drink', 80, 95, 80, true, 2, true),
((SELECT id FROM menu_categories WHERE name = 'Beverages'), 'Sprite', 'Lemon-lime soft drink', 80, 95, 80, true, 2, true),
((SELECT id FROM menu_categories WHERE name = 'Beverages'), 'Fresh Lime', 'Fresh lime juice with mint', 120, 135, 120, true, 5, true),
((SELECT id FROM menu_categories WHERE name = 'Beverages'), 'Lassi (Sweet)', 'Traditional yogurt drink', 150, 165, 150, true, 3, true),
((SELECT id FROM menu_categories WHERE name = 'Beverages'), 'Doodh Patti', 'Traditional milk tea', 80, 80, 80, false, 5, true),
((SELECT id FROM menu_categories WHERE name = 'Beverages'), 'Green Tea', 'Refreshing green tea', 60, 60, 60, false, 3, true),

-- Desserts
((SELECT id FROM menu_categories WHERE name = 'Desserts'), 'Kheer', 'Traditional rice pudding', 180, 180, 180, false, 5, true),
((SELECT id FROM menu_categories WHERE name = 'Desserts'), 'Gulab Jamun', 'Sweet milk dumplings in syrup', 150, 150, 150, false, 3, true),
((SELECT id FROM menu_categories WHERE name = 'Desserts'), 'Kulfi', 'Traditional ice cream', 120, 120, 120, false, 2, true);

-- Sample restaurant tables
INSERT INTO restaurant_tables (table_number, capacity, status, position_x, position_y) VALUES
('T-01', 4, 'available', 100, 100),
('T-02', 2, 'available', 250, 100),
('T-03', 6, 'available', 400, 100),
('T-04', 4, 'available', 550, 100),
('T-05', 8, 'available', 100, 250),
('T-06', 4, 'available', 250, 250),
('T-07', 2, 'available', 400, 250),
('T-08', 6, 'available', 550, 250),
('T-09', 4, 'available', 100, 400),
('T-10', 4, 'available', 250, 400),
('T-11', 2, 'available', 400, 400),
('T-12', 8, 'available', 550, 400);

-- Sample inventory items
INSERT INTO inventory_items (name, unit, current_stock, minimum_stock, cost_per_unit, supplier) VALUES
-- Proteins
('Chicken (Fresh)', 'kg', 25, 5, 450, 'Local Poultry Farm'),
('Mutton (Fresh)', 'kg', 15, 3, 850, 'Meat Supplier Co.'),
('Beef (Fresh)', 'kg', 20, 4, 650, 'Meat Supplier Co.'),
('Fish (Fresh)', 'kg', 10, 2, 550, 'Fish Market'),

-- Grains & Staples
('Basmati Rice', 'kg', 50, 10, 180, 'Rice Wholesaler'),
('Wheat Flour', 'kg', 40, 8, 85, 'Flour Mill'),
('Lentils (Mixed)', 'kg', 25, 5, 120, 'Grocery Supplier'),

-- Vegetables
('Onions', 'kg', 20, 5, 60, 'Vegetable Market'),
('Tomatoes', 'kg', 15, 5, 80, 'Vegetable Market'),
('Ginger-Garlic Paste', 'kg', 8, 2, 180, 'Spice Supplier'),
('Green Chilies', 'kg', 5, 1, 120, 'Vegetable Market'),
('Coriander (Fresh)', 'kg', 3, 1, 200, 'Vegetable Market'),

-- Dairy
('Yogurt', 'kg', 12, 3, 150, 'Dairy Farm'),
('Milk', 'liter', 20, 5, 90, 'Dairy Farm'),
('Cream', 'liter', 8, 2, 280, 'Dairy Farm'),

-- Cooking Essentials
('Cooking Oil', 'liter', 15, 3, 280, 'Oil Supplier'),
('Ghee', 'kg', 10, 2, 650, 'Dairy Farm'),
('Salt', 'kg', 25, 5, 35, 'Grocery Supplier'),

-- Spices
('Red Chili Powder', 'kg', 5, 1, 320, 'Spice Supplier'),
('Turmeric Powder', 'kg', 3, 1, 280, 'Spice Supplier'),
('Cumin Powder', 'kg', 2, 0.5, 450, 'Spice Supplier'),
('Coriander Powder', 'kg', 2, 0.5, 380, 'Spice Supplier'),
('Garam Masala', 'kg', 2, 0.5, 850, 'Spice Supplier'),

-- Beverages
('Coca Cola (Cans)', 'piece', 100, 20, 45, 'Beverage Distributor'),
('Sprite (Cans)', 'piece', 80, 20, 45, 'Beverage Distributor'),
('Tea Leaves', 'kg', 5, 1, 650, 'Tea Supplier'),
('Sugar', 'kg', 30, 5, 85, 'Grocery Supplier');

-- Sample recipe ingredients (linking menu items to inventory)
-- Chicken Tikka
INSERT INTO recipe_ingredients (menu_item_id, inventory_item_id, quantity_required) VALUES
((SELECT id FROM menu_items WHERE name = 'Chicken Tikka'), (SELECT id FROM inventory_items WHERE name = 'Chicken (Fresh)'), 0.5),
((SELECT id FROM menu_items WHERE name = 'Chicken Tikka'), (SELECT id FROM inventory_items WHERE name = 'Yogurt'), 0.1),
((SELECT id FROM menu_items WHERE name = 'Chicken Tikka'), (SELECT id FROM inventory_items WHERE name = 'Ginger-Garlic Paste'), 0.02);

-- Chicken Karhai (1kg)
INSERT INTO recipe_ingredients (menu_item_id, inventory_item_id, quantity_required) VALUES
((SELECT id FROM menu_items WHERE name = 'Chicken Karhai (1kg)'), (SELECT id FROM inventory_items WHERE name = 'Chicken (Fresh)'), 0.8),
((SELECT id FROM menu_items WHERE name = 'Chicken Karhai (1kg)'), (SELECT id FROM inventory_items WHERE name = 'Onions'), 0.2),
((SELECT id FROM menu_items WHERE name = 'Chicken Karhai (1kg)'), (SELECT id FROM inventory_items WHERE name = 'Tomatoes'), 0.3),
((SELECT id FROM menu_items WHERE name = 'Chicken Karhai (1kg)'), (SELECT id FROM inventory_items WHERE name = 'Cooking Oil'), 0.1);

-- Chicken Biryani
INSERT INTO recipe_ingredients (menu_item_id, inventory_item_id, quantity_required) VALUES
((SELECT id FROM menu_items WHERE name = 'Chicken Biryani'), (SELECT id FROM inventory_items WHERE name = 'Chicken (Fresh)'), 0.3),
((SELECT id FROM menu_items WHERE name = 'Chicken Biryani'), (SELECT id FROM inventory_items WHERE name = 'Basmati Rice'), 0.2),
((SELECT id FROM menu_items WHERE name = 'Chicken Biryani'), (SELECT id FROM inventory_items WHERE name = 'Onions'), 0.1),
((SELECT id FROM menu_items WHERE name = 'Chicken Biryani'), (SELECT id FROM inventory_items WHERE name = 'Yogurt'), 0.05);

-- Update system settings with restaurant-specific values
UPDATE system_settings SET value = 'Zaiqa Restaurant' WHERE key = 'restaurant_name';
UPDATE system_settings SET value = 'PKR' WHERE key = 'currency';
UPDATE system_settings SET value = '0.16' WHERE key = 'tax_rate';
UPDATE system_settings SET value = '15' WHERE key = 'cold_drink_dine_in_markup';
UPDATE system_settings SET value = '100' WHERE key = 'default_per_head_charge';

-- Add some additional settings
INSERT INTO system_settings (key, value, description) VALUES
('restaurant_address', 'Main Bazaar, Lahore, Pakistan', 'Restaurant address for bills'),
('restaurant_phone', '+92-42-1234567', 'Restaurant contact number'),
('service_charge_rate', '0.05', 'Service charge rate (5%)'),
('enable_service_charge', 'false', 'Whether to apply service charge'),
('bill_footer_message', 'Thank you for dining with us!', 'Message at bottom of bills'),
('kitchen_display_refresh', '30', 'Kitchen display refresh interval in seconds');

-- Create some sample low-stock scenarios for demo
UPDATE inventory_items SET current_stock = 2.5 WHERE name = 'Chicken (Fresh)';
UPDATE inventory_items SET current_stock = 8 WHERE name = 'Basmati Rice';
UPDATE inventory_items SET current_stock = 1.5 WHERE name = 'Cooking Oil';
