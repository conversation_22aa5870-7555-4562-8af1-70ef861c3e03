<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
</head>
<body>
    <h1>JavaScript Test Page</h1>
    <button onclick="testFunction()">Click Me - Test 1</button>
    <button onclick="alert('Direct alert works!')">Click Me - Test 2</button>
    <button id="testBtn3">Click Me - Test 3</button>
    
    <div id="output"></div>

    <script>
        console.log('JavaScript is loading...');
        
        function testFunction() {
            alert('Test function works!');
            document.getElementById('output').innerHTML = 'Button 1 clicked successfully!';
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM is ready');
            
            document.getElementById('testBtn3').addEventListener('click', function() {
                alert('Event listener works!');
                document.getElementById('output').innerHTML = 'Button 3 clicked successfully!';
            });
        });
        
        console.log('JavaScript loaded successfully');
    </script>
</body>
</html>
